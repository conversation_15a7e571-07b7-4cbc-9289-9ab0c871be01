#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复选框界面
"""

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

def test_checkbox_ui():
    """测试复选框界面"""
    print("测试复选框界面...")
    
    # 创建测试窗口
    root = ttk.Window(
        title="复选框测试",
        themename="cosmo",
        size=(600, 400)
    )
    
    # 主容器
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill=BOTH, expand=YES)
    
    # 标题
    title_label = ttk.Label(
        main_frame,
        text="复选框列表测试",
        font=("Microsoft YaHei", 14, "bold")
    )
    title_label.pack(pady=(0, 10))
    
    # 列表容器
    list_container = ttk.Frame(main_frame)
    list_container.pack(fill=BOTH, expand=YES)
    
    # 创建滚动画布
    canvas = ttk.Canvas(list_container)
    scrollbar = ttk.Scrollbar(list_container, orient=VERTICAL, command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    canvas.pack(side=LEFT, fill=BOTH, expand=YES)
    scrollbar.pack(side=RIGHT, fill=Y)
    
    # 添加测试复选框
    checkboxes = []
    test_items = [
        "Chrome - 百度首页",
        "记事本 - 新建文档 [最小化]",
        "Cursor - test.py",
        "PDF阅读器 - 文档.pdf [最小化]",
        "文件管理器 - D盘 [最小化]",
        "微信 - 聊天窗口",
        "QQ - 好友列表 [最小化]",
        "Excel - 工作表1.xlsx",
        "Word - 文档1.docx [最小化]",
        "PowerPoint - 演示文稿1.pptx"
    ]
    
    def on_checkbox_change():
        selected_count = sum(1 for var, _ in checkboxes if var.get())
        status_label.config(text=f"选中项目: {selected_count}")
    
    for i, item in enumerate(test_items):
        checkbox_frame = ttk.Frame(scrollable_frame)
        checkbox_frame.pack(fill=X, padx=5, pady=2)
        
        var = ttk.BooleanVar()
        checkbox = ttk.Checkbutton(
            checkbox_frame,
            text=item,
            variable=var,
            command=on_checkbox_change
        )
        checkbox.pack(anchor=W)
        
        checkboxes.append((var, checkbox))
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=X, pady=(10, 0))
    
    def select_all():
        for var, _ in checkboxes:
            var.set(True)
        on_checkbox_change()
    
    def deselect_all():
        for var, _ in checkboxes:
            var.set(False)
        on_checkbox_change()
    
    ttk.Button(button_frame, text="全选", command=select_all).pack(side=LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="取消全选", command=deselect_all).pack(side=LEFT, padx=(0, 5))
    
    # 状态标签
    status_label = ttk.Label(button_frame, text="选中项目: 0")
    status_label.pack(side=RIGHT)
    
    print("复选框界面测试窗口已创建")
    root.mainloop()

def main():
    print("=== 复选框界面测试 ===")
    test_checkbox_ui()

if __name__ == "__main__":
    main()