# FlyClock Tool

PPT/PDF全屏放映计时辅助工具

## 项目简介

FlyClock Tool是一个专为演示场景设计的计时辅助工具，能够自动检测PPT/PDF全屏状态并启动计时，为演讲者提供直观的时间管理支持。

## 主要功能

### 🎯 核心功能
- **自动检测**：智能识别PPT/PDF全屏状态，自动启动/停止计时
- **悬浮显示**：始终置顶的计时器窗口，可拖拽移动
- **双模式计时**：支持正计时和倒计时模式
- **音频提示**：到时提醒和提前警告功能
- **远程控制**：通过Web界面远程控制计时器

### 📱 兼容性支持
- **Microsoft Office PowerPoint**：支持PPT、PPTX、PPS、PPSX格式
- **WPS演示**：完全兼容WPS演示文稿格式
- **PDF阅读器**：支持Adobe Reader、Foxit Reader、SumatraPDF等主流PDF阅读器

### 🎨 用户体验
- **界面友好**：简洁直观的操作界面
- **高度可定制**：字体、颜色、透明度等可自由调整
- **系统托盘**：支持最小化到系统托盘
- **配置持久化**：自动保存用户设置

## 技术架构

### 技术栈
- **Python 3.8+**：主要开发语言
- **Tkinter**：GUI界面框架
- **win32gui**：Windows窗口检测
- **pygame**：音频播放支持

### 项目结构
```
flyclock_tool/
├── core/                   # 核心业务逻辑
│   ├── config_manager.py   # 配置管理
│   ├── window_detector.py  # 窗口检测
│   ├── timer_engine.py     # 计时器引擎
│   └── audio_player.py     # 音频播放
├── gui/                    # 图形用户界面
│   ├── main_window.py      # 主控制界面
│   ├── timer_widget.py     # 悬浮计时器
│   └── settings_dialog.py  # 设置对话框
├── remote/                 # 远程控制
│   └── http_server.py      # HTTP服务器
├── assets/                 # 资源文件
│   ├── sounds/             # 音频文件
│   └── web/                # Web界面
└── main.py                 # 程序入口
```

## 安装使用

### 环境要求
- Windows 10/11
- Python 3.8或更高版本

### 安装步骤
1. 克隆项目到本地
```bash
git clone <repository-url>
cd flyclock_tool
```

2. 安装依赖包
```bash
pip install -r requirements.txt
```

3. 运行程序
```bash
python main.py
```

### 打包发布
使用PyInstaller打包为独立exe文件：
```bash
python build.py
```

## 使用说明

### 基本使用
1. 启动FlyClock Tool
2. 设置计时时长和模式
3. 打开PPT或PDF文件并进入全屏模式
4. 程序自动检测并开始计时
5. 悬浮计时器显示剩余时间

### 高级功能
- **远程控制**：启用HTTP服务器，通过手机/平板控制
- **音频提示**：设置提前警告时间和提示音
- **外观定制**：调整字体、颜色、透明度等

## 开发状态

- [x] 项目基础架构搭建
- [ ] 配置管理系统开发
- [ ] 计时器核心引擎开发
- [ ] 窗口检测模块开发
- [ ] 悬浮计时器窗口开发
- [ ] 主控制界面开发
- [ ] 音频播放功能开发
- [ ] 远程控制功能开发
- [ ] 应用打包和部署

## 版本信息

- **当前版本**：1.0.0
- **开发状态**：开发中
- **更新日期**：2025-07-28

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过Issue联系我们。