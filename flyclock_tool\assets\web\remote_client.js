/**
 * FlyClock 远程控制客户端
 */

class RemoteClient {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.isAuthenticated = false;
        this.requiresAuth = false;
        this.currentFile = null;
        this.files = [];
        
        // 初始化
        this.init();
    }
    
    init() {
        // 自动连接到服务器
        this.connect();

        // 绑定键盘事件
        this.bindKeyboardEvents();

        // 绑定触摸手势
        this.bindTouchEvents();

        // 检测移动设备
        this.isMobile = this.detectMobile();

        // 移动端优化
        if (this.isMobile) {
            this.optimizeForMobile();
        }
    }
    
    connect() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        let host = window.location.hostname || 'localhost';
        let port;

        // 如果是通过HTTP访问的，WebSocket端口 = HTTP端口 + 1
        if (window.location.protocol === 'http:' || window.location.protocol === 'https:') {
            host = window.location.hostname;
            const httpPort = parseInt(window.location.port) || 8080;
            port = (httpPort + 1).toString(); // WebSocket端口 = HTTP端口 + 1
        }
        // 如果是通过file://协议打开的，使用默认端口
        else if (window.location.protocol === 'file:') {
            host = this.getLocalIP() || 'localhost';
            port = '8081'; // 默认WebSocket端口
        }

        const url = `${protocol}//${host}:${port}`;
        console.log('尝试连接到WebSocket:', url);

        this.updateConnectionStatus('connecting', '连接中...');

        try {
            this.websocket = new WebSocket(url);
            
            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.updateConnectionStatus('connected', '已连接');
            };
            
            this.websocket.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                this.isAuthenticated = false;
                this.updateConnectionStatus('disconnected', '连接断开');
                
                // 5秒后尝试重连
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.connect();
                    }
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.updateConnectionStatus('disconnected', '连接错误');
            };
            
        } catch (error) {
            console.error('连接失败:', error);
            this.updateConnectionStatus('disconnected', '连接失败');
        }
    }
    
    handleMessage(data) {
        console.log('收到消息:', data);
        
        switch (data.type) {
            case 'welcome':
                this.handleWelcome(data);
                break;
            case 'auth_result':
                this.handleAuthResult(data);
                break;
            case 'file_list':
                this.handleFileList(data);
                break;
            case 'file_selected':
                this.handleFileSelected(data);
                break;
            case 'status':
                this.handleStatus(data);
                break;
            case 'preview':
                this.handlePreview(data);
                break;
            case 'error':
                this.handleError(data);
                break;
            default:
                console.warn('未知消息类型:', data.type);
        }
    }
    
    handleWelcome(data) {
        this.requiresAuth = data.requires_auth;
        
        if (this.requiresAuth) {
            this.showAuthModal();
        } else {
            this.isAuthenticated = true;
            this.onAuthenticated();
        }
    }
    
    handleAuthResult(data) {
        if (data.success) {
            this.isAuthenticated = true;
            this.hideAuthModal();
            this.onAuthenticated();
        } else {
            alert('认证失败: ' + data.message);
        }
    }
    
    handleFileList(data) {
        this.files = data.files;
        this.updateFileList();
    }
    
    handleFileSelected(data) {
        this.currentFile = data.file_path;
        this.updateCurrentFileDisplay();
        this.updatePageInfo(data.current_page, data.total_pages);
    }
    
    handleStatus(data) {
        this.updateStatus(data);
    }
    
    handlePreview(data) {
        this.updatePreview(data.image_data);
    }
    
    handleError(data) {
        console.error('服务器错误:', data.message);
        alert('错误: ' + data.message);
    }
    
    onAuthenticated() {
        // 认证成功后的操作
        this.refreshFiles();
    }
    
    sendMessage(data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(data));
        } else {
            console.error('WebSocket未连接');
        }
    }
    
    // UI更新方法
    updateConnectionStatus(status, text) {
        const statusEl = document.getElementById('connectionStatus');
        statusEl.className = `connection-status ${status}`;
        statusEl.textContent = text;
    }
    
    showAuthModal() {
        document.getElementById('authModal').classList.remove('hidden');
        document.getElementById('passwordInput').focus();
    }
    
    hideAuthModal() {
        document.getElementById('authModal').classList.add('hidden');
    }
    
    updateFileList() {
        const fileListEl = document.getElementById('fileList');
        
        if (this.files.length === 0) {
            fileListEl.innerHTML = '<div style="padding: 20px; text-align: center; color: #999;">未找到支持的文件</div>';
            return;
        }
        
        fileListEl.innerHTML = '';
        
        this.files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.onclick = () => this.selectFile(file.path);
            
            const fileName = document.createElement('div');
            fileName.className = 'file-name';
            fileName.textContent = file.name;
            
            const fileInfo = document.createElement('div');
            fileInfo.className = 'file-info';
            fileInfo.textContent = `${file.type.toUpperCase()} • ${this.formatFileSize(file.size)}`;
            
            fileItem.appendChild(fileName);
            fileItem.appendChild(fileInfo);
            fileListEl.appendChild(fileItem);
        });
    }
    
    updateCurrentFileDisplay() {
        const currentFileEl = document.getElementById('currentFile');
        if (this.currentFile) {
            const fileName = this.currentFile.split(/[/\\]/).pop();
            currentFileEl.textContent = fileName;
        } else {
            currentFileEl.textContent = '未选择';
        }
    }
    
    updatePageInfo(current, total) {
        document.getElementById('currentPage').textContent = `${current}/${total}`;
        document.getElementById('pageInfo').textContent = `第 ${current} 页，共 ${total} 页`;
    }
    
    updateStatus(status) {
        // 更新计时器显示
        if (status.current_time !== undefined) {
            this.updateTimerDisplay(status.current_time);
        }
        
        // 更新播放状态
        let playStatusText = '停止';
        if (status.timer_running) {
            playStatusText = status.timer_paused ? '暂停' : '播放中';
        }
        document.getElementById('playStatus').textContent = playStatusText;
        
        // 更新文件和页面信息
        if (status.current_file) {
            this.currentFile = status.current_file;
            this.updateCurrentFileDisplay();
            this.updatePageInfo(status.current_page, status.total_pages);
        }
    }
    
    updateTimerDisplay(seconds) {
        const timerEl = document.getElementById('timerDisplay');
        const timeStr = this.formatTime(seconds);
        timerEl.textContent = timeStr;
        
        // 更新计时器颜色
        timerEl.className = 'timer-display ';
        if (seconds <= 0) {
            timerEl.className += 'timer-finished';
        } else if (seconds <= 60) {
            timerEl.className += 'timer-warning';
        } else {
            timerEl.className += 'timer-normal';
        }
    }
    
    updatePreview(imageData) {
        const previewArea = document.getElementById('previewArea');
        
        if (imageData) {
            previewArea.innerHTML = `<img class="preview-image" src="data:image/png;base64,${imageData}" alt="预览">`;
        } else {
            previewArea.innerHTML = '<div class="preview-placeholder">预览加载失败</div>';
        }
    }
    
    // 工具方法
    formatTime(seconds) {
        const isNegative = seconds < 0;
        const absSeconds = Math.abs(seconds);
        
        const hours = Math.floor(absSeconds / 3600);
        const minutes = Math.floor((absSeconds % 3600) / 60);
        const secs = absSeconds % 60;
        
        let timeStr;
        if (hours > 0) {
            timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            timeStr = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        
        return isNegative ? '-' + timeStr : timeStr;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    bindKeyboardEvents() {
        document.addEventListener('keydown', (event) => {
            if (!this.isAuthenticated) return;

            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    this.prevPage();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    this.nextPage();
                    break;
                case ' ':
                    event.preventDefault();
                    this.playPresentation();
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.pausePresentation();
                    break;
            }
        });
    }

    bindTouchEvents() {
        let startX = 0;
        let startY = 0;
        let startTime = 0;

        const previewArea = document.getElementById('previewArea');

        // 触摸开始
        previewArea.addEventListener('touchstart', (event) => {
            if (!this.isAuthenticated) return;

            const touch = event.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            startTime = Date.now();

            event.preventDefault();
        }, { passive: false });

        // 触摸结束
        previewArea.addEventListener('touchend', (event) => {
            if (!this.isAuthenticated) return;

            const touch = event.changedTouches[0];
            const endX = touch.clientX;
            const endY = touch.clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // 检测滑动手势
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50 && deltaTime < 500) {
                if (deltaX > 0) {
                    // 向右滑动 - 上一页
                    this.prevPage();
                    this.showGestureHint('上一页');
                } else {
                    // 向左滑动 - 下一页
                    this.nextPage();
                    this.showGestureHint('下一页');
                }
            }
            // 检测点击手势
            else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
                // 短点击 - 播放/暂停
                this.togglePlayPause();
                this.showGestureHint('播放/暂停');
            }

            event.preventDefault();
        }, { passive: false });

        // 防止默认的触摸行为
        previewArea.addEventListener('touchmove', (event) => {
            event.preventDefault();
        }, { passive: false });
    }

    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768);
    }

    optimizeForMobile() {
        // 添加移动端特定的样式类
        document.body.classList.add('mobile-device');

        // 隐藏鼠标悬停提示
        const style = document.createElement('style');
        style.textContent = `
            .mobile-device .btn:hover {
                background: inherit !important;
            }
        `;
        document.head.appendChild(style);

        // 添加触摸反馈
        this.addTouchFeedback();
    }

    addTouchFeedback() {
        // 为所有按钮添加触摸反馈
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('touchstart', () => {
                btn.style.transform = 'scale(0.95)';
            });

            btn.addEventListener('touchend', () => {
                setTimeout(() => {
                    btn.style.transform = 'scale(1)';
                }, 100);
            });
        });
    }

    getLocalIP() {
        // 尝试获取本地IP地址（简化版本）
        // 在实际应用中，可能需要用户手动输入IP地址
        const commonIPs = [
            '***********', '***********', '***********00', '***********00',
            '********', '**********'
        ];

        // 这里返回localhost，实际使用时需要用户配置
        return 'localhost';
    }

    showGestureHint(action) {
        // 显示手势提示
        const hint = document.createElement('div');
        hint.className = 'gesture-hint';
        hint.textContent = action;
        hint.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1.2em;
            z-index: 9999;
            pointer-events: none;
            animation: fadeInOut 1s ease-in-out;
        `;

        // 添加动画样式
        if (!document.getElementById('gesture-hint-style')) {
            const style = document.createElement('style');
            style.id = 'gesture-hint-style';
            style.textContent = `
                @keyframes fadeInOut {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(hint);

        setTimeout(() => {
            document.body.removeChild(hint);
        }, 1000);
    }

    togglePlayPause() {
        // 切换播放/暂停状态
        const playStatus = document.getElementById('playStatus').textContent;
        if (playStatus === '播放中') {
            this.pausePresentation();
        } else {
            this.playPresentation();
        }
    }
    
    // 控制方法
    selectFile(filePath) {
        // 更新UI选中状态
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.target.closest('.file-item').classList.add('selected');
        
        // 发送选择文件消息
        this.sendMessage({
            type: 'select_file',
            file_path: filePath
        });
    }
    
    refreshFiles() {
        this.sendMessage({
            type: 'get_files'
        });
    }
    
    nextPage() {
        this.sendMessage({
            type: 'control',
            action: 'next_page'
        });
    }
    
    prevPage() {
        this.sendMessage({
            type: 'control',
            action: 'prev_page'
        });
    }
    
    playPresentation() {
        this.sendMessage({
            type: 'control',
            action: 'play'
        });
    }
    
    pausePresentation() {
        this.sendMessage({
            type: 'control',
            action: 'pause'
        });
    }
    
    resetTimer() {
        this.sendMessage({
            type: 'control',
            action: 'reset'
        });
    }
}

// 全局函数（供HTML调用）
let remoteClient;

// 认证相关
function authenticate() {
    const password = document.getElementById('passwordInput').value;
    remoteClient.sendMessage({
        type: 'auth',
        password: password
    });
}

function cancelAuth() {
    remoteClient.hideAuthModal();
}

// 控制函数
function refreshFiles() {
    remoteClient.refreshFiles();
}

function nextPage() {
    remoteClient.nextPage();
}

function prevPage() {
    remoteClient.prevPage();
}

function playPresentation() {
    remoteClient.playPresentation();
}

function pausePresentation() {
    remoteClient.pausePresentation();
}

function resetTimer() {
    remoteClient.resetTimer();
}

function enterFullscreen() {
    remoteClient.sendMessage({
        type: 'control',
        action: 'fullscreen'
    });
}

function exitFullscreen() {
    remoteClient.sendMessage({
        type: 'control',
        action: 'exit_fullscreen'
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    remoteClient = new RemoteClient();
});
