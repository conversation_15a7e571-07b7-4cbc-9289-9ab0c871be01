#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI界面模块
使用tkinter创建左右分栏的主界面，集成进程监控和窗口控制功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import logging
from typing import List, Dict, Optional, Callable

# 导入项目模块
try:
    from ..core.process_monitor import get_process_monitor
    from ..core.window_controller import get_window_controller
    from ..core.config_manager import get_config_manager
    from ..utils.timer_manager import get_timer_manager
except ImportError:
    # 开发环境导入
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.process_monitor import get_process_monitor
    from core.window_controller import get_window_controller
    from core.config_manager import get_config_manager
    from utils.timer_manager import get_timer_manager

# 配置日志
logger = logging.getLogger(__name__)


class MainWindow:
    """主窗口类 - 进程监控和窗口切换的主界面"""
    
    def __init__(self):
        """初始化主窗口"""
        # 创建主窗口
        self.window = tk.Tk()
        self.window.title("Process Monitor - 进程监控和窗口切换工具")
        self.window.geometry("900x650")
        self.window.minsize(800, 600)
        
        # 设置窗口图标和样式
        self.window.configure(bg='#F0F0F0')
        
        # 初始化组件
        self.process_monitor = get_process_monitor(update_interval=2.0)
        self.window_controller = get_window_controller(max_workers=3)
        self.config_manager = get_config_manager()
        self.timer_manager = None  # 稍后初始化
        
        # GUI组件
        self.process_listbox = None
        self.interval_var = tk.StringVar(value="5")
        self.status_var = tk.StringVar(value="就绪")
        self.is_monitoring = False
        
        # 进程数据
        self.current_processes = []
        self.selected_process_pids = []
        
        # 线程锁
        self._ui_lock = threading.Lock()
        
        # 设置界面
        self.setup_ui()
        
        # 加载配置
        self.load_config()
        
        # 绑定窗口事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        logger.info("MainWindow 初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.window, bg='#F0F0F0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        self.setup_left_panel(main_frame)
        self.setup_right_panel(main_frame)
        
        # 创建底部状态栏
        self.setup_status_bar()
        
        logger.info("GUI界面设置完成")    
    def setup_left_panel(self, parent):
        """设置左侧面板（进程列表区域）"""
        # 左侧框架
        left_frame = tk.Frame(parent, bg='#F0F0F0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 进程列表标题
        title_frame = tk.Frame(left_frame, bg='#F0F0F0')
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(title_frame, text="系统进程列表", font=('Microsoft YaHei', 12, 'bold'), 
                bg='#F0F0F0').pack(side=tk.LEFT)
        
        # 刷新按钮
        refresh_btn = tk.Button(title_frame, text="刷新", command=self.refresh_processes,
                               bg='#E1E1E1', relief=tk.RAISED, padx=10)
        refresh_btn.pack(side=tk.RIGHT)
        
        # 搜索框
        search_frame = tk.Frame(left_frame, bg='#F0F0F0')
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(search_frame, text="搜索:", bg='#F0F0F0').pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 进程列表框架
        list_frame = tk.Frame(left_frame, bg='#F0F0F0')
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview用于显示进程列表
        columns = ('name', 'pid', 'path')
        self.process_tree = ttk.Treeview(list_frame, columns=columns, show='tree headings',
                                        selectmode='extended')
        
        # 设置列标题
        self.process_tree.heading('#0', text='选择')
        self.process_tree.heading('name', text='进程名称')
        self.process_tree.heading('pid', text='PID')
        self.process_tree.heading('path', text='路径')
        
        # 设置列宽
        self.process_tree.column('#0', width=60, minwidth=60)
        self.process_tree.column('name', width=150, minwidth=100)
        self.process_tree.column('pid', width=80, minwidth=60)
        self.process_tree.column('path', width=300, minwidth=200)
        
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.process_tree.yview)
        self.process_tree.configure(yscrollcommand=tree_scroll.set)
        
        # 布局
        self.process_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.process_tree.bind('<<TreeviewSelect>>', self.on_process_selection_changed)
        
        # 选择统计标签
        self.selection_label = tk.Label(left_frame, text="已选择: 0 个进程", 
                                       bg='#F0F0F0', fg='#666666')
        self.selection_label.pack(fill=tk.X, pady=(10, 0))    
    def setup_right_panel(self, parent):
        """设置右侧面板（控制面板区域）"""
        # 右侧框架
        right_frame = tk.Frame(parent, bg='#F0F0F0', width=280)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        right_frame.pack_propagate(False)  # 固定宽度
        
        # 控制面板标题
        title_label = tk.Label(right_frame, text="控制面板", font=('Microsoft YaHei', 12, 'bold'),
                              bg='#F0F0F0')
        title_label.pack(fill=tk.X, pady=(0, 20))
        
        # 时间间隔设置
        interval_frame = tk.LabelFrame(right_frame, text="时间间隔设置", bg='#F0F0F0',
                                      font=('Microsoft YaHei', 10))
        interval_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(interval_frame, text="切换间隔(秒):", bg='#F0F0F0').pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        interval_input_frame = tk.Frame(interval_frame, bg='#F0F0F0')
        interval_input_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.interval_entry = tk.Entry(interval_input_frame, textvariable=self.interval_var, width=10)
        self.interval_entry.pack(side=tk.LEFT)
        
        tk.Label(interval_input_frame, text="秒", bg='#F0F0F0').pack(side=tk.LEFT, padx=(5, 0))
        
        # 预设间隔按钮
        preset_frame = tk.Frame(interval_frame, bg='#F0F0F0')
        preset_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        for interval in [1, 3, 5, 10]:
            btn = tk.Button(preset_frame, text=f"{interval}s", width=4,
                           command=lambda i=interval: self.set_interval(i),
                           bg='#E1E1E1', relief=tk.RAISED)
            btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 控制按钮
        control_frame = tk.LabelFrame(right_frame, text="监控控制", bg='#F0F0F0',
                                     font=('Microsoft YaHei', 10))
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.start_button = tk.Button(control_frame, text="开始监控", command=self.toggle_monitoring,
                                     bg='#4CAF50', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                                     relief=tk.RAISED, padx=20, pady=5)
        self.start_button.pack(fill=tk.X, padx=10, pady=10)
        
        self.test_button = tk.Button(control_frame, text="测试切换", command=self.test_switch,
                                    bg='#2196F3', fg='white', relief=tk.RAISED, padx=20, pady=5)
        self.test_button.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 统计信息
        stats_frame = tk.LabelFrame(right_frame, text="统计信息", bg='#F0F0F0',
                                   font=('Microsoft YaHei', 10))
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=8, width=30,
                                                   font=('Consolas', 9), bg='#FFFFFF')
        self.stats_text.pack(fill=tk.BOTH, padx=10, pady=10)
        
        # 操作按钮
        action_frame = tk.Frame(right_frame, bg='#F0F0F0')
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.save_config_btn = tk.Button(action_frame, text="保存配置", command=self.save_config,
                                        bg='#FF9800', fg='white', relief=tk.RAISED)
        self.save_config_btn.pack(fill=tk.X, padx=0, pady=(0, 5))
        
        self.reset_stats_btn = tk.Button(action_frame, text="重置统计", command=self.reset_stats,
                                        bg='#9E9E9E', fg='white', relief=tk.RAISED)
        self.reset_stats_btn.pack(fill=tk.X, padx=0)    
    def setup_status_bar(self):
        """设置底部状态栏"""
        status_frame = tk.Frame(self.window, bg='#E0E0E0', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = tk.Label(status_frame, textvariable=self.status_var,
                                    bg='#E0E0E0', anchor=tk.W, padx=10)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 进程计数标签
        self.process_count_label = tk.Label(status_frame, text="进程: 0",
                                           bg='#E0E0E0', padx=10)
        self.process_count_label.pack(side=tk.RIGHT)
    
    def on_search_changed(self, *args):
        """搜索框内容变化事件"""
        search_text = self.search_var.get().lower()
        self.filter_processes(search_text)
    
    def on_process_selection_changed(self, event):
        """进程选择变化事件"""
        selected_items = self.process_tree.selection()
        self.selected_process_pids = []
        
        for item in selected_items:
            values = self.process_tree.item(item, 'values')
            if values:
                try:
                    pid = int(values[1])  # PID在第二列
                    self.selected_process_pids.append(pid)
                except ValueError:
                    continue
        
        # 更新选择统计
        count = len(self.selected_process_pids)
        self.selection_label.config(text=f"已选择: {count} 个进程")
        
        # 更新按钮状态
        self.update_button_states()
    
    def set_interval(self, interval):
        """设置时间间隔"""
        self.interval_var.set(str(interval))
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.is_monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()
    
    def start_monitoring(self):
        """开始监控"""
        if not self.selected_process_pids:
            messagebox.showwarning("警告", "请先选择要监控的进程！")
            return
        
        try:
            interval = float(self.interval_var.get())
            if interval < 0.1:
                messagebox.showerror("错误", "时间间隔不能小于0.1秒！")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的时间间隔！")
            return
        
        # 初始化定时器管理器
        if self.timer_manager is None:
            self.timer_manager = get_timer_manager(self.execute_switch, interval)
        else:
            self.timer_manager.set_interval(interval)
        
        # 启动定时器
        success = self.timer_manager.start_timer()
        if success:
            self.is_monitoring = True
            self.start_button.config(text="停止监控", bg='#F44336')
            self.status_var.set(f"监控中 - 间隔: {interval}秒")
            logger.info(f"开始监控 {len(self.selected_process_pids)} 个进程")
        else:
            messagebox.showerror("错误", "启动监控失败！")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.timer_manager:
            self.timer_manager.stop_timer()
        
        self.is_monitoring = False
        self.start_button.config(text="开始监控", bg='#4CAF50')
        self.status_var.set("已停止")
        logger.info("监控已停止")    
    def execute_switch(self):
        """执行窗口切换（定时器回调函数）"""
        try:
            # 发送Ctrl+Tab
            success = self.window_controller.send_ctrl_tab(async_mode=True)
            
            # 更新统计信息（在主线程中）
            self.window.after(0, self.update_stats_display)
            
            logger.info("执行窗口切换")
            return success
        except Exception as e:
            logger.error(f"执行窗口切换失败: {e}")
            return False
    
    def test_switch(self):
        """测试窗口切换"""
        if not self.selected_process_pids:
            messagebox.showwarning("警告", "请先选择要监控的进程！")
            return
        
        success = self.window_controller.send_ctrl_tab(async_mode=False)
        if success:
            self.status_var.set("测试切换成功")
            messagebox.showinfo("成功", "测试切换执行成功！")
        else:
            self.status_var.set("测试切换失败")
            messagebox.showerror("失败", "测试切换执行失败！")
    
    def refresh_processes(self):
        """刷新进程列表"""
        self.status_var.set("正在刷新进程列表...")
        
        # 在后台线程中获取进程列表
        def fetch_processes():
            try:
                processes = self.process_monitor.get_exe_processes()
                # 在主线程中更新UI
                self.window.after(0, lambda: self.update_process_list(processes))
            except Exception as e:
                logger.error(f"获取进程列表失败: {e}")
                self.window.after(0, lambda: self.status_var.set("刷新失败"))
        
        threading.Thread(target=fetch_processes, daemon=True).start()
    
    def update_process_list(self, processes):
        """更新进程列表显示"""
        # 清空现有列表
        for item in self.process_tree.get_children():
            self.process_tree.delete(item)
        
        self.current_processes = processes
        
        # 添加进程到列表
        for process in processes:
            # 截断路径显示
            path = process.get('exe_path', 'Unknown')
            if len(path) > 50:
                path = '...' + path[-47:]
            
            item_id = self.process_tree.insert('', 'end', 
                                              values=(process['name'], process['pid'], path))
            
            # 如果之前选中过这个进程，重新选中
            if process['pid'] in self.selected_process_pids:
                self.process_tree.selection_add(item_id)
        
        # 应用搜索过滤
        search_text = self.search_var.get().lower()
        if search_text:
            self.filter_processes(search_text)
        
        # 更新状态
        count = len(processes)
        self.process_count_label.config(text=f"进程: {count}")
        self.status_var.set(f"已加载 {count} 个进程")
        
        logger.info(f"进程列表已更新，共 {count} 个进程")
    
    def filter_processes(self, search_text):
        """过滤进程列表"""
        if not search_text:
            # 显示所有进程
            for item in self.process_tree.get_children():
                self.process_tree.item(item, tags=())
            return
        
        # 隐藏不匹配的进程
        visible_count = 0
        for item in self.process_tree.get_children():
            values = self.process_tree.item(item, 'values')
            if values:
                name = values[0].lower()
                path = values[2].lower()
                
                if search_text in name or search_text in path:
                    self.process_tree.item(item, tags=())
                    visible_count += 1
                else:
                    self.process_tree.item(item, tags=('hidden',))
        
        # 配置隐藏样式
        self.process_tree.tag_configure('hidden', foreground='#CCCCCC')
        
        self.status_var.set(f"搜索结果: {visible_count} 个进程")    
    def update_stats_display(self):
        """更新统计信息显示"""
        try:
            # 获取窗口控制器统计
            window_stats = self.window_controller.get_stats()
            
            # 获取定时器统计
            timer_stats = {}
            if self.timer_manager:
                timer_stats = self.timer_manager.get_stats()
            
            # 格式化统计信息
            stats_text = "=== 窗口控制统计 ===\n"
            stats_text += f"Ctrl+Tab发送: {window_stats.get('ctrl_tab_sent', 0)}\n"
            stats_text += f"窗口激活: {window_stats.get('windows_activated', 0)}\n"
            stats_text += f"操作失败: {window_stats.get('operations_failed', 0)}\n"
            stats_text += f"队列操作: {window_stats.get('operations_queued', 0)}\n\n"
            
            if timer_stats:
                stats_text += "=== 定时器统计 ===\n"
                stats_text += f"总执行次数: {timer_stats.get('total_executions', 0)}\n"
                stats_text += f"成功次数: {timer_stats.get('successful_executions', 0)}\n"
                stats_text += f"失败次数: {timer_stats.get('failed_executions', 0)}\n"
                stats_text += f"成功率: {timer_stats.get('success_rate', 0):.1%}\n"
                
                running_time = timer_stats.get('running_time', 0)
                if running_time > 0:
                    stats_text += f"运行时间: {running_time:.1f}秒\n"
            
            stats_text += f"\n=== 进程信息 ===\n"
            stats_text += f"已选择进程: {len(self.selected_process_pids)}\n"
            stats_text += f"总进程数: {len(self.current_processes)}\n"
            
            # 更新显示
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def reset_stats(self):
        """重置统计信息"""
        try:
            # 重置窗口控制器统计
            self.window_controller.reset_stats()
            
            # 重置定时器统计
            if self.timer_manager:
                self.timer_manager.reset_stats()
            
            # 更新显示
            self.update_stats_display()
            
            self.status_var.set("统计信息已重置")
            messagebox.showinfo("成功", "统计信息已重置！")
            
        except Exception as e:
            logger.error(f"重置统计信息失败: {e}")
            messagebox.showerror("错误", f"重置统计信息失败: {e}")
    
    def load_config(self):
        """加载配置"""
        try:
            # 加载窗口位置和大小
            position = self.config_manager.get_window_position()
            size = self.config_manager.get_window_size()
            
            self.window.geometry(f"{size['width']}x{size['height']}+{position['x']}+{position['y']}")
            
            # 加载时间间隔
            interval = self.config_manager.get_timer_interval()
            self.interval_var.set(str(interval))
            
            # 加载选中的进程名称（用于恢复选择）
            selected_names = self.config_manager.get_selected_names()
            if selected_names:
                # 启动后刷新进程列表以恢复选择
                self.window.after(1000, self.refresh_processes)
            
            logger.info("配置加载完成")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            # 保存窗口位置和大小
            geometry = self.window.geometry()
            # 解析geometry字符串 "widthxheight+x+y"
            size_pos = geometry.split('+')
            size = size_pos[0].split('x')
            
            if len(size) == 2 and len(size_pos) >= 3:
                width, height = int(size[0]), int(size[1])
                x, y = int(size_pos[1]), int(size_pos[2])
                
                self.config_manager.set_window_size(width, height, save=False)
                self.config_manager.set_window_position(x, y, save=False)
            
            # 保存时间间隔
            try:
                interval = float(self.interval_var.get())
                self.config_manager.set_timer_interval(int(interval), save=False)
            except ValueError:
                pass
            
            # 保存选中的进程PID和名称
            self.config_manager.set_selected_pids(self.selected_process_pids, save=False)
            
            # 获取选中进程的名称
            selected_names = []
            for item in self.process_tree.selection():
                values = self.process_tree.item(item, 'values')
                if values:
                    selected_names.append(values[0])  # 进程名称
            
            self.config_manager.set_selected_names(selected_names, save=False)
            
            # 一次性保存所有配置
            success = self.config_manager.save_config()
            
            if success:
                self.status_var.set("配置已保存")
                messagebox.showinfo("成功", "配置保存成功！")
            else:
                messagebox.showerror("错误", "配置保存失败！")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")    
    def update_button_states(self):
        """更新按钮状态"""
        has_selection = len(self.selected_process_pids) > 0
        
        # 测试按钮只有在有选择时才启用
        self.test_button.config(state=tk.NORMAL if has_selection else tk.DISABLED)
        
        # 开始按钮在有选择且未监控时才能启动监控
        if self.is_monitoring:
            self.start_button.config(state=tk.NORMAL)  # 停止按钮总是可用
        else:
            self.start_button.config(state=tk.NORMAL if has_selection else tk.DISABLED)
    
    def get_selected_processes(self):
        """获取选中的进程信息"""
        selected_processes = []
        
        for pid in self.selected_process_pids:
            for process in self.current_processes:
                if process['pid'] == pid:
                    selected_processes.append(process)
                    break
        
        return selected_processes
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        def auto_refresh():
            if not self.window.winfo_exists():
                return
            
            # 如果没有在监控，定期刷新进程列表
            if not self.is_monitoring:
                self.refresh_processes()
            
            # 定期更新统计信息
            self.update_stats_display()
            
            # 5秒后再次刷新
            self.window.after(5000, auto_refresh)
        
        # 启动自动刷新
        self.window.after(2000, auto_refresh)  # 2秒后开始
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 停止监控
            if self.is_monitoring:
                self.stop_monitoring()
            
            # 保存配置
            self.save_config()
            
            # 关闭所有组件
            if self.timer_manager:
                self.timer_manager.shutdown()
            
            self.window_controller.shutdown()
            self.process_monitor.stop_monitoring()
            
            logger.info("应用程序正在关闭")
            
        except Exception as e:
            logger.error(f"关闭应用程序时出错: {e}")
        finally:
            self.window.destroy()
    
    def run(self):
        """运行主窗口"""
        try:
            # 初始加载进程列表
            self.refresh_processes()
            
            # 启动自动刷新
            self.start_auto_refresh()
            
            # 更新按钮状态
            self.update_button_states()
            
            # 初始更新统计信息
            self.update_stats_display()
            
            logger.info("主窗口开始运行")
            
            # 启动主循环
            self.window.mainloop()
            
        except Exception as e:
            logger.error(f"运行主窗口时出错: {e}")
            messagebox.showerror("错误", f"运行主窗口时出错: {e}")


# 主窗口工厂函数
def create_main_window():
    """
    创建主窗口实例
    
    Returns:
        MainWindow: 主窗口实例
    """
    return MainWindow()


# 全局主窗口实例
_main_window = None


def get_main_window():
    """
    获取主窗口实例（单例模式）
    
    Returns:
        MainWindow: 主窗口实例
    """
    global _main_window
    if _main_window is None:
        _main_window = MainWindow()
    return _main_window