#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化ALT+TAB窗口切换工具
功能：选择exe进程，设置切换时间间隔，自动循环ALT+TAB切换窗口
使用ttkbootstrap现代化界面设计

作者：AI Assistant
版本：2.0.0
"""

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import threading
import time
import psutil
import pyautogui
import pygetwindow as gw
import os

# 禁用pyautogui的安全检查
pyautogui.FAILSAFE = False


class ModernAltTabSwitcher:
    """现代化ALT+TAB窗口切换器"""

    def __init__(self):
        # 创建简洁窗口
        self.root = ttk.Window(
            title="窗口切换工具",
            themename="cosmo",  # 使用简洁主题
            size=(1000, 600),
            resizable=(True, True)
        )

        # 状态变量
        self.is_switching = False
        self.switch_thread = None
        self.selected_processes = []
        self.interval = 5.0
        self.switch_count = 0

        # 窗口数据（改为窗口列表）
        self.window_list = []
        self.window_checkboxes = []  # 存储复选框控件
        self.current_pid = os.getpid()  # 获取当前程序PID
        self.current_switch_index = 0  # 当前切换索引

        # Windows系统进程黑名单
        self.system_processes = {
            'dwm.exe', 'winlogon.exe', 'csrss.exe', 'smss.exe', 'wininit.exe',
            'services.exe', 'lsass.exe', 'svchost.exe', 'spoolsv.exe', 'explorer.exe',
            'taskhost.exe', 'taskhostw.exe', 'RuntimeBroker.exe', 'WmiPrvSE.exe',
            'dllhost.exe', 'conhost.exe', 'audiodg.exe', 'SearchIndexer.exe',
            'SearchProtocolHost.exe', 'SearchFilterHost.exe', 'fontdrvhost.exe',
            'dwm.exe', 'winlogon.exe', 'LogonUI.exe', 'userinit.exe', 'rdpclip.exe',
            'ctfmon.exe', 'sihost.exe', 'ShellExperienceHost.exe', 'StartMenuExperienceHost.exe',
            'SecurityHealthSystray.exe', 'SecurityHealthService.exe', 'MsMpEng.exe',
            'NisSrv.exe', 'WinStore.App.exe', 'ApplicationFrameHost.exe',
            'SystemSettings.exe', 'SettingSyncHost.exe', 'UserOOBEBroker.exe',
            'TiWorker.exe', 'TrustedInstaller.exe', 'WUDFHost.exe', 'WerFault.exe',
            'WerFaultSecure.exe', 'wuauclt.exe', 'MoUsoCoreWorker.exe', 'UsoClient.exe'
        }

        # 临时窗口标题黑名单（过滤这些常见的对话框和临时窗口）
        self.temp_window_titles = {
            '下载', 'Downloads', '设置', 'Settings', '属性', 'Properties',
            '打开', 'Open', '保存', 'Save', '另存为', 'Save As',
            '选择文件', 'Choose File', '浏览文件夹', 'Browse Folder',
            '确认', 'Confirm', '警告', 'Warning', '错误', 'Error',
            '信息', 'Information', '提示', 'Tip', '帮助', 'Help',
            '关于', 'About', '首选项', 'Preferences', '选项', 'Options',
            '查找', 'Find', '替换', 'Replace', '转到', 'Go To',
            '打印', 'Print', '页面设置', 'Page Setup', '预览', 'Preview',
            '新建', 'New', '重命名', 'Rename', '删除', 'Delete',
            '复制', 'Copy', '粘贴', 'Paste', '剪切', 'Cut'
        }

        # 创建界面
        self.create_ui()

        # 加载窗口列表
        self.refresh_windows()

    def create_ui(self):
        """创建现代化用户界面"""
        # 主容器
        main_container = ttk.Frame(self.root, padding=15)
        main_container.pack(fill=BOTH, expand=YES)

        # 标题区域
        self.create_header(main_container)

        # 内容区域
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill=BOTH, expand=YES, pady=(20, 0))

        # 左侧进程列表区域
        self.create_process_panel(content_frame)

        # 右侧控制面板
        self.create_control_panel(content_frame)

    def create_header(self, parent):
        """创建简洁标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 15))

        # 简洁标题
        title_label = ttk.Label(
            header_frame,
            text="窗口切换工具",
            font=("Microsoft YaHei", 16, "bold")
        )
        title_label.pack(pady=10)

    def create_process_panel(self, parent):
        """创建简洁进程列表面板"""
        # 左侧面板
        left_panel = ttk.LabelFrame(
            parent,
            text="窗口列表",
            padding=8
        )
        left_panel.pack(side=LEFT, fill=BOTH, expand=YES, padx=(0, 8))

        # 进程列表容器
        list_container = ttk.Frame(left_panel)
        list_container.pack(fill=BOTH, expand=YES, pady=(0, 10))

        # 创建表头
        header_frame = ttk.Frame(list_container)
        header_frame.pack(fill=X, padx=2, pady=(0, 2))

        # 表头标签 - 调整宽度和间距
        ttk.Label(header_frame, text="选择", width=4).pack(side=LEFT)
        ttk.Label(header_frame, text="窗口标题", width=50).pack(side=LEFT, padx=(5, 0))
        ttk.Label(header_frame, text="程序名", width=20).pack(side=LEFT, padx=(5, 0))
        ttk.Label(header_frame, text="进程名", width=15).pack(side=LEFT, padx=(5, 0))

        # 分隔线
        separator = ttk.Separator(list_container, orient='horizontal')
        separator.pack(fill=X, padx=2, pady=(0, 2))

        # 创建滚动画布用于复选框列表
        canvas = ttk.Canvas(list_container)
        scrollbar = ttk.Scrollbar(list_container, orient=VERTICAL, command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        canvas.pack(side=LEFT, fill=BOTH, expand=YES)
        scrollbar.pack(side=RIGHT, fill=Y)

        # 存储画布引用
        self.canvas = canvas

        # 按钮区域
        button_frame = ttk.Frame(left_panel)
        button_frame.pack(fill=X, pady=(5, 0))

        # 刷新按钮
        refresh_btn = ttk.Button(
            button_frame,
            text="刷新窗口列表",
            command=self.refresh_windows,
            width=12
        )
        refresh_btn.pack(side=LEFT, padx=(0, 5))

        # 全选按钮
        select_all_btn = ttk.Button(
            button_frame,
            text="全选",
            command=self.select_all_windows,
            width=8
        )
        select_all_btn.pack(side=LEFT, padx=(0, 5))

        # 取消全选按钮
        deselect_all_btn = ttk.Button(
            button_frame,
            text="取消全选",
            command=self.deselect_all_windows,
            width=8
        )
        deselect_all_btn.pack(side=LEFT)

    def create_control_panel(self, parent):
        """创建简洁控制面板"""
        # 右侧控制面板
        right_panel = ttk.LabelFrame(
            parent,
            text="控制面板",
            padding=10,
            width=220
        )
        right_panel.pack(side=RIGHT, fill=Y)
        right_panel.pack_propagate(False)

        # 时间间隔设置
        self.create_interval_section(right_panel)

        # 控制按钮区域
        self.create_control_section(right_panel)

        # 状态信息区域
        self.create_status_section(right_panel)

        # 开发者信息区域
        self.create_developer_info(right_panel)

    def create_interval_section(self, parent):
        """创建简洁时间间隔设置区域"""
        interval_frame = ttk.LabelFrame(
            parent,
            text="切换间隔设置",
            padding=10
        )
        interval_frame.pack(fill=X, pady=(0, 15))

        # 间隔输入
        ttk.Label(interval_frame, text="间隔时间(秒):").pack(anchor=W)

        self.interval_var = ttk.StringVar(value="5")
        interval_entry = ttk.Entry(
            interval_frame,
            textvariable=self.interval_var,
            width=15
        )
        interval_entry.pack(anchor=W, pady=(5, 10))

        # 快速设置按钮
        ttk.Label(interval_frame, text="快速设置:").pack(anchor=W)

        button_frame = ttk.Frame(interval_frame)
        button_frame.pack(fill=X, pady=(5, 0))

        quick_times = [1, 3, 5, 10]
        for seconds in quick_times:
            btn = ttk.Button(
                button_frame,
                text=f"{seconds}s",
                command=lambda s=seconds: self.set_interval(s),
                width=6
            )
            btn.pack(side=LEFT, padx=(0, 5))

    def create_control_section(self, parent):
        """创建简洁控制按钮区域"""
        control_frame = ttk.LabelFrame(
            parent,
            text="切换控制",
            padding=10
        )
        control_frame.pack(fill=X, pady=(0, 15))

        # 主控制按钮
        self.start_button = ttk.Button(
            control_frame,
            text="开始切换",
            command=self.toggle_switching,
            width=15
        )
        self.start_button.pack(fill=X, pady=(0, 10))

        # 测试按钮
        test_button = ttk.Button(
            control_frame,
            text="测试ALT+TAB",
            command=self.test_alt_tab,
            width=15
        )
        test_button.pack(fill=X)

    def create_status_section(self, parent):
        """创建简洁状态信息区域"""
        status_frame = ttk.LabelFrame(
            parent,
            text="状态信息",
            padding=10
        )
        status_frame.pack(fill=X)

        # 状态标签
        self.status_var = ttk.StringVar(value="就绪")
        status_label = ttk.Label(
            status_frame,
            textvariable=self.status_var,
            font=("Microsoft YaHei", 10)
        )
        status_label.pack(anchor=W, pady=(0, 5))

        # 切换次数
        self.count_var = ttk.StringVar(value="切换次数: 0")
        count_label = ttk.Label(
            status_frame,
            textvariable=self.count_var,
            font=("Microsoft YaHei", 9)
        )
        count_label.pack(anchor=W, pady=(0, 5))

        # 选中进程数
        self.selected_var = ttk.StringVar(value="选中窗口: 0")
        selected_label = ttk.Label(
            status_frame,
            textvariable=self.selected_var,
            font=("Microsoft YaHei", 9)
        )
        selected_label.pack(anchor=W)

    def create_developer_info(self, parent):
        """创建开发者信息区域"""
        dev_frame = ttk.LabelFrame(
            parent,
            text="关于",
            padding=10
        )
        dev_frame.pack(fill=X, pady=(15, 0))

        # 开发者信息
        dev_info = ttk.Label(
            dev_frame,
            text="珠海分行智能科技部\n开发者：黎民浩",
            font=("Microsoft YaHei", 9),
            justify=CENTER
        )
        dev_info.pack(anchor=CENTER)

    def refresh_windows(self):
        """刷新窗口列表（显示可见窗口）"""
        # 清空现有数据
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        self.window_list.clear()
        self.window_checkboxes.clear()

        try:
            # 获取所有窗口
            all_windows = gw.getAllWindows()

            # 过滤出有效窗口
            valid_windows = []
            current_title = self.root.title()  # 获取当前程序窗口标题

            # 用于去重的集合（标题+程序名组合）
            seen_windows = set()

            for window in all_windows:
                try:
                    # 基本过滤条件
                    if (window.title and
                        not window.title.isspace() and
                        window.title != current_title and  # 排除自身窗口
                        window.visible and
                        window.width > 100 and   # 提高尺寸要求，过滤小对话框
                        window.height > 50 and
                        window.title not in ['Program Manager', 'Desktop']):  # 排除系统窗口

                        # 过滤临时窗口标题
                        if window.title in self.temp_window_titles:
                            continue

                        # 获取程序名（从窗口标题推断或使用默认值）
                        program_name = self.get_program_name_from_window(window)

                        # 过滤系统进程
                        if program_name in self.system_processes:
                            continue

                        # 创建去重标识（标题+程序名）
                        window_key = f"{window.title}|{program_name}"

                        # 去重检查
                        if window_key in seen_windows:
                            continue

                        seen_windows.add(window_key)

                        valid_windows.append({
                            'window': window,
                            'title': window.title,
                            'program': program_name
                        })

                except Exception:
                    continue

            # 按窗口标题排序
            valid_windows.sort(key=lambda x: x['title'].lower())

            # 添加到复选框列表
            for win_data in valid_windows:
                # 截断过长的标题并添加状态信息
                display_title = win_data['title']
                if len(display_title) > 45:
                    display_title = display_title[:42] + "..."

                # 添加状态标识
                if win_data['window'].isMinimized:
                    display_title += " [最小化]"

                # 创建表格行框架
                row_frame = ttk.Frame(self.scrollable_frame)
                row_frame.pack(fill=X, padx=2, pady=0)

                # 创建复选框变量
                var = ttk.BooleanVar()

                # 获取进程名
                process_name = self.get_process_name_from_window(win_data['window'])
                if not process_name:
                    process_name = "未知"

                # 复选框列
                checkbox = ttk.Checkbutton(
                    row_frame,
                    variable=var,
                    command=self.on_checkbox_change
                )
                checkbox.pack(side=LEFT, anchor=W)

                # 窗口标题列
                title_label = ttk.Label(
                    row_frame,
                    text=display_title,
                    width=50,
                    anchor=W
                )
                title_label.pack(side=LEFT, padx=(5, 0))

                # 程序名列
                program_label = ttk.Label(
                    row_frame,
                    text=win_data['program'],
                    width=20,
                    anchor=W
                )
                program_label.pack(side=LEFT, padx=(5, 0))

                # 进程名列
                process_label = ttk.Label(
                    row_frame,
                    text=process_name,
                    width=15,
                    anchor=W
                )
                process_label.pack(side=LEFT, padx=(5, 0))

                # 存储数据
                self.window_list.append(win_data)
                self.window_checkboxes.append({
                    'var': var,
                    'checkbox': checkbox,
                    'window_data': win_data,
                    'row_frame': row_frame
                })

            # 更新状态
            self.status_var.set(f"已加载 {len(valid_windows)} 个窗口")

        except Exception as e:
            self.status_var.set(f"获取窗口列表失败: {e}")
            Messagebox.show_error("错误", f"获取窗口列表失败: {e}")

    def get_program_name_from_window(self, window):
        """从窗口获取程序名"""
        try:
            # 尝试从窗口标题推断程序名
            title = window.title.lower()

            # 常见程序的识别规则
            if 'chrome' in title or 'google' in title:
                return 'Chrome'
            elif 'firefox' in title:
                return 'Firefox'
            elif 'edge' in title:
                return 'Edge'
            elif 'notepad' in title or '记事本' in title:
                return 'Notepad'
            elif 'word' in title:
                return 'Word'
            elif 'excel' in title:
                return 'Excel'
            elif 'powerpoint' in title:
                return 'PowerPoint'
            elif 'code' in title or 'cursor' in title:
                return 'Code Editor'
            elif 'cmd' in title or 'powershell' in title:
                return 'Terminal'
            else:
                # 默认返回窗口标题的第一部分
                parts = window.title.split(' - ')
                if len(parts) > 1:
                    return parts[-1]  # 通常程序名在最后
                else:
                    return parts[0][:20]  # 限制长度
        except:
            return "Unknown"

    def get_process_name_from_window(self, window):
        """从窗口获取进程名"""
        try:
            import psutil
            # 尝试通过窗口句柄获取进程ID
            if hasattr(window, '_hWnd'):
                import win32process
                try:
                    _, pid = win32process.GetWindowThreadProcessId(window._hWnd)
                    process = psutil.Process(pid)
                    return process.name()
                except:
                    pass

            # 备用方法：通过窗口标题推断
            title = window.title.lower()
            if 'chrome' in title or 'google' in title:
                return 'chrome.exe'
            elif 'firefox' in title or 'mozilla' in title:
                return 'firefox.exe'
            elif 'edge' in title or 'microsoft edge' in title:
                return 'msedge.exe'
            elif 'notepad' in title or '记事本' in title:
                return 'notepad.exe'
            elif 'word' in title or 'microsoft word' in title:
                return 'winword.exe'
            elif 'excel' in title or 'microsoft excel' in title:
                return 'excel.exe'
            elif 'powerpoint' in title or 'microsoft powerpoint' in title:
                return 'powerpnt.exe'
            elif 'explorer' in title or '文件资源管理器' in title:
                return 'explorer.exe'
            elif 'cmd' in title or '命令提示符' in title:
                return 'cmd.exe'
            elif 'powershell' in title:
                return 'powershell.exe'
            else:
                return None
        except:
            return None

    def select_all_windows(self):
        """全选所有窗口"""
        for checkbox_data in self.window_checkboxes:
            checkbox_data['var'].set(True)
        self.on_checkbox_change()

    def deselect_all_windows(self):
        """取消全选所有窗口"""
        for checkbox_data in self.window_checkboxes:
            checkbox_data['var'].set(False)
        self.on_checkbox_change()

    def on_checkbox_change(self):
        """复选框状态改变事件"""
        # 更新选中的窗口列表
        self.selected_processes = []
        selected_count = 0

        for checkbox_data in self.window_checkboxes:
            if checkbox_data['var'].get():
                self.selected_processes.append(checkbox_data['window_data'])
                selected_count += 1

        # 更新状态显示
        self.selected_var.set(f"选中窗口: {selected_count}")

        # 更新按钮状态
        self.start_button.configure(state=NORMAL if selected_count > 0 else DISABLED)

    def set_interval(self, seconds):
        """设置时间间隔"""
        self.interval_var.set(str(seconds))
        self.status_var.set(f"间隔已设置为 {seconds} 秒")

    def test_alt_tab(self):
        """测试ALT+TAB功能"""
        try:
            pyautogui.hotkey('alt', 'tab')
            self.status_var.set("ALT+TAB测试成功")
        except Exception as e:
            self.status_var.set(f"ALT+TAB测试失败: {e}")
            Messagebox.show_error("错误", f"ALT+TAB测试失败: {e}")

    def toggle_switching(self):
        """切换开始/停止状态"""
        if self.is_switching:
            self.stop_switching()
        else:
            self.start_switching()

    def start_switching(self):
        """开始自动切换"""
        # 检查是否选择了进程
        if not self.selected_processes:
            Messagebox.show_warning("警告", "请先选择要监控的exe进程！")
            return

        # 检查时间间隔
        try:
            self.interval = float(self.interval_var.get())
            if self.interval < 0.1:
                Messagebox.show_error("错误", "时间间隔不能小于0.1秒！")
                return
        except ValueError:
            Messagebox.show_error("错误", "请输入有效的时间间隔！")
            return

        # 开始切换
        self.is_switching = True
        self.switch_count = 0
        self.current_switch_index = 0  # 重置切换索引
        self.start_button.configure(text="停止切换")
        self.status_var.set(f"开始在{len(self.selected_processes)}个窗口间切换 - 间隔: {self.interval}秒")

        # 启动切换线程
        self.switch_thread = threading.Thread(target=self.switching_loop, daemon=True)
        self.switch_thread.start()

    def stop_switching(self):
        """停止自动切换"""
        self.is_switching = False
        self.start_button.configure(text="开始切换")
        self.status_var.set("已停止切换")

    def switching_loop(self):
        """切换循环（在后台线程中运行）"""
        while self.is_switching and self.selected_processes:
            try:
                # 获取当前要切换到的窗口
                current_window_data = self.selected_processes[self.current_switch_index]
                window = current_window_data['window']
                window_title = current_window_data['title']

                # 直接激活窗口
                success = self.activate_window_direct(window)

                if success:
                    self.switch_count += 1
                    # 更新计数器和当前窗口信息
                    self.root.after(0, lambda: self.count_var.set(f"切换次数: {self.switch_count}"))
                    # 截断标题显示
                    display_title = window_title[:30] + "..." if len(window_title) > 30 else window_title
                    self.root.after(0, lambda: self.status_var.set(f"已切换到: {display_title}"))
                else:
                    self.root.after(0, lambda: self.status_var.set(f"无法激活窗口: {window_title[:20]}"))

                # 移动到下一个窗口
                self.current_switch_index = (self.current_switch_index + 1) % len(self.selected_processes)

                # 等待指定时间
                time.sleep(self.interval)

            except Exception as e:
                # 在主线程中显示错误
                self.root.after(0, lambda: self.status_var.set(f"切换出错: {e}"))
                break

        # 切换结束，重置状态
        if self.is_switching:  # 如果是因为错误结束的
            self.root.after(0, self.stop_switching)

    def activate_window_direct(self, window):
        """直接激活指定窗口"""
        try:
            # 检查窗口是否仍然有效
            if not window.title:
                return False

            # 如果窗口被最小化，先恢复
            if window.isMinimized:
                window.restore()
                time.sleep(0.1)  # 短暂等待

            # 激活窗口
            window.activate()
            return True

        except Exception as e:
            print(f"激活窗口失败: {e}")
            # 备用方案：使用ALT+TAB
            try:
                pyautogui.hotkey('alt', 'tab')
                return True
            except:
                return False

    def run(self):
        """运行应用程序"""
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 复选框界面不需要绑定选择事件

        # 启动主循环
        self.root.mainloop()



    def on_closing(self):
        """窗口关闭事件"""
        if self.is_switching:
            self.stop_switching()
        self.root.destroy()


def main():
    """主函数"""
    try:
        print("启动ALT+TAB窗口切换工具...")

        # 检查依赖
        try:
            import psutil
            import pyautogui
            import ttkbootstrap
            print("依赖检查通过")
        except ImportError as e:
            print(f"缺少依赖模块: {e}")
            print("请运行: pip install psutil pyautogui ttkbootstrap")
            return

        # 创建并运行应用
        app = ModernAltTabSwitcher()
        print("界面已创建")

        app.run()

    except Exception as e:
        print(f"程序运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()