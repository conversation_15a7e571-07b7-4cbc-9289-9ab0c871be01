# Copyright 2024 <PERSON>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
mDNS service discovery module for MCP Memory Service.

This module provides mDNS service advertisement and discovery capabilities
for the MCP Memory Service HTTP/HTTPS interface.
"""

from .mdns_service import ServiceAdvertiser, ServiceDiscovery
from .client import DiscoveryClient

__all__ = ['ServiceAdvertiser', 'ServiceDiscovery', 'DiscoveryClient']