#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试过滤后的进程列表
"""

import psutil
import os

def test_filtered_processes():
    """测试过滤系统进程后的结果"""
    print("测试过滤系统进程...")
    
    current_pid = os.getpid()
    current_name = None
    
    # Windows系统进程黑名单
    system_processes = {
        'dwm.exe', 'winlogon.exe', 'csrss.exe', 'smss.exe', 'wininit.exe',
        'services.exe', 'lsass.exe', 'svchost.exe', 'spoolsv.exe', 'explorer.exe',
        'taskhost.exe', 'taskhostw.exe', 'RuntimeBroker.exe', 'WmiPrvSE.exe',
        'dllhost.exe', 'conhost.exe', 'audiodg.exe', 'SearchIndexer.exe',
        'SearchProtocolHost.exe', 'SearchFilterHost.exe', 'fontdrvhost.exe',
        'dwm.exe', 'winlogon.exe', 'LogonUI.exe', 'userinit.exe', 'rdpclip.exe',
        'ctfmon.exe', 'sihost.exe', 'ShellExperienceHost.exe', 'StartMenuExperienceHost.exe',
        'SecurityHealthSystray.exe', 'SecurityHealthService.exe', 'MsMpEng.exe',
        'NisSrv.exe', 'WinStore.App.exe', 'ApplicationFrameHost.exe',
        'SystemSettings.exe', 'SettingSyncHost.exe', 'UserOOBEBroker.exe',
        'TiWorker.exe', 'TrustedInstaller.exe', 'WUDFHost.exe', 'WerFault.exe',
        'WerFaultSecure.exe', 'wuauclt.exe', 'MoUsoCoreWorker.exe', 'UsoClient.exe'
    }
    
    try:
        current_proc = psutil.Process(current_pid)
        current_name = current_proc.name()
        print(f"当前进程: {current_name} (PID: {current_pid})")
    except:
        current_name = "python.exe"
    
    all_processes = []
    user_processes = []
    
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            name = proc.info['name']
            pid = proc.info['pid']
            
            if name and name.lower().endswith('.exe'):
                all_processes.append({'name': name, 'pid': pid})
                
                # 过滤条件：排除系统进程、自身进程
                if (pid != current_pid and 
                    name != current_name and 
                    name not in system_processes):
                    user_processes.append({'name': name, 'pid': pid})
                    
        except:
            continue
    
    # 去重
    seen_names = set()
    unique_user_processes = []
    for proc in sorted(user_processes, key=lambda x: x['name'].lower()):
        if proc['name'] not in seen_names:
            seen_names.add(proc['name'])
            unique_user_processes.append(proc)
    
    print(f"\n总exe进程数: {len(all_processes)}")
    print(f"过滤后用户进程数: {len(unique_user_processes)}")
    print(f"过滤掉的系统进程数: {len(all_processes) - len(user_processes)}")
    
    print(f"\n前20个用户进程:")
    for i, proc in enumerate(unique_user_processes[:20]):
        print(f"  {i+1:2d}. {proc['name']:<30} (PID: {proc['pid']})")
    
    return len(unique_user_processes) > 0

def main():
    print("=== 进程过滤测试 ===")
    result = test_filtered_processes()
    print(f"\n测试结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    main()