# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run_red_version.py'],
    pathex=[],
    binaries=[],
    datas=[('timer_window_red.py', '.'), ('window_detector_fixed.py', '.'), ('../core', 'core'), ('../assets', 'assets')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ToolClock_V1.1.0_RedBeep',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
