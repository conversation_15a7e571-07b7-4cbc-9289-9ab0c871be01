#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 带日志运行脚本
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('toolclock_debug.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """启动ToolClock并记录日志"""
    logger.info("=" * 60)
    logger.info("🔧 ToolClock 启动中...")
    logger.info("=" * 60)
    
    try:
        logger.info("📦 导入模块...")
        
        from gui.timer_window import TimerWindow
        logger.info("✓ TimerWindow 导入成功")

        from window_detector_fixed import WindowDetectorFixed
        logger.info("✓ WindowDetectorFixed 导入成功")
        
        logger.info("🚀 创建计时器窗口...")
        
        # 创建计时器窗口
        timer_window = TimerWindow()
        logger.info("✓ 计时器窗口创建成功")
        
        logger.info("🔍 创建窗口检测器...")
        
        # 创建窗口检测器
        detector = WindowDetectorFixed(timer_window)
        logger.info("✓ 窗口检测器创建成功")
        
        logger.info("🎯 ToolClock已启动！")
        logger.info("📋 修复后的逻辑:")
        logger.info("  ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）")
        logger.info("  ✅ 按F5或全屏放映 → 自动开始倒计时")
        logger.info("  ✅ 退出全屏 → 自动停止倒计时")
        logger.info("  ✅ 切换文件 → 更新时间设置（不启动）")
        logger.info("=" * 60)
        logger.info("🔍 实时监控开始（请进行测试）:")
        logger.info("=" * 60)
        
        # 启动GUI主循环
        logger.info("▶️ 启动GUI主循环...")
        
        timer_window.run()
        
    except KeyboardInterrupt:
        logger.info("✓ ToolClock已停止")
    except Exception as e:
        logger.error(f"✗ ToolClock启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        input("按回车键退出...")

if __name__ == "__main__":
    main()
