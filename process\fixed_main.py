#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版主程序 - 跳过可能有问题的初始化步骤
"""

import sys
import os
import logging
import tempfile

# 禁用日志避免卡顿
logging.disable(logging.CRITICAL)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """修复版主函数"""
    print("Process Monitor - 修复版启动")
    print("=" * 50)
    
    try:
        print("1. 检查依赖项...")
        import psutil
        import pyautogui
        import tkinter as tk
        print("   依赖项检查通过")
        
        print("2. 创建简化的GUI界面...")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("Process Monitor - 进程监控工具")
        root.geometry("800x600")
        root.configure(bg='#F0F0F0')
        
        # 创建主框架
        main_frame = tk.Frame(root, bg='#F0F0F0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧进程列表框架
        left_frame = tk.Frame(main_frame, bg='#F0F0F0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 进程列表标题
        tk.Label(left_frame, text="系统进程列表", font=('Microsoft YaHei', 12, 'bold'), 
                bg='#F0F0F0').pack(anchor=tk.W, pady=(0, 10))
        
        # 进程列表
        from tkinter import ttk
        columns = ('name', 'pid')
        process_tree = ttk.Treeview(left_frame, columns=columns, show='tree headings',
                                   selectmode='extended')
        
        process_tree.heading('#0', text='选择')
        process_tree.heading('name', text='进程名称')
        process_tree.heading('pid', text='PID')
        
        process_tree.column('#0', width=60)
        process_tree.column('name', width=200)
        process_tree.column('pid', width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=process_tree.yview)
        process_tree.configure(yscrollcommand=scrollbar.set)
        
        process_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧控制面板
        right_frame = tk.Frame(main_frame, bg='#F0F0F0', width=250)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        right_frame.pack_propagate(False)
        
        tk.Label(right_frame, text="控制面板", font=('Microsoft YaHei', 12, 'bold'),
                bg='#F0F0F0').pack(pady=(0, 20))
        
        # 时间间隔设置
        interval_frame = tk.LabelFrame(right_frame, text="时间间隔设置", bg='#F0F0F0')
        interval_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(interval_frame, text="切换间隔(秒):", bg='#F0F0F0').pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        interval_var = tk.StringVar(value="5")
        interval_entry = tk.Entry(interval_frame, textvariable=interval_var, width=10)
        interval_entry.pack(anchor=tk.W, padx=10, pady=(0, 10))
        
        # 控制按钮
        control_frame = tk.LabelFrame(right_frame, text="监控控制", bg='#F0F0F0')
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        start_button = tk.Button(control_frame, text="开始监控", 
                               bg='#4CAF50', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                               padx=20, pady=5)
        start_button.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态标签
        status_var = tk.StringVar(value="就绪")
        status_label = tk.Label(right_frame, textvariable=status_var, bg='#F0F0F0')
        status_label.pack(fill=tk.X, pady=10)
        
        print("3. 加载进程列表...")
        
        # 获取进程列表
        processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] and proc.info['name'].lower().endswith('.exe'):
                    processes.append(proc.info)
                    if len(processes) >= 50:  # 限制数量避免卡顿
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 添加进程到列表
        for proc in processes:
            process_tree.insert('', 'end', values=(proc['name'], proc['pid']))
        
        status_var.set(f"已加载 {len(processes)} 个进程")
        
        print(f"4. GUI界面创建完成，加载了 {len(processes)} 个进程")
        print("   程序现在应该显示在屏幕上")
        
        # 运行主循环
        root.mainloop()
        
        print("程序正常退出")
        return 0
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)