#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器测试
"""

import os
import sys
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socket

def get_local_ip():
    """获取本机IP地址"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "127.0.0.1"

def main():
    print("🔧 简单HTTP服务器测试")
    print("=" * 40)
    
    # 配置
    port = 8080
    web_dir = Path("flyclock_tool/assets/web")
    
    print(f"📁 Web目录: {web_dir.absolute()}")
    print(f"📁 目录存在: {web_dir.exists()}")
    
    if web_dir.exists():
        files = list(web_dir.glob("*"))
        print(f"📄 文件列表: {[f.name for f in files]}")
    
    # 获取IP
    local_ip = get_local_ip()
    print(f"🌐 本机IP: {local_ip}")
    
    # 切换到web目录
    os.chdir(web_dir)
    print(f"📂 当前工作目录: {os.getcwd()}")
    
    try:
        # 创建服务器
        httpd = HTTPServer(('0.0.0.0', port), SimpleHTTPRequestHandler)
        
        print(f"\n✅ HTTP服务器启动成功")
        print(f"   端口: {port}")
        print(f"   绑定: 0.0.0.0")
        
        print(f"\n📱 访问地址:")
        print(f"   本地: http://localhost:{port}/remote_client.html")
        print(f"   局域网: http://{local_ip}:{port}/remote_client.html")
        
        print(f"\n⏰ 服务器运行中... 按 Ctrl+C 停止")
        
        # 启动服务器
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
