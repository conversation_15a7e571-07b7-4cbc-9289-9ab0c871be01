@echo off
echo FlyClock远程控制修复工具
echo ========================

echo.
echo 1. 检查防火墙规则...
netsh advfirewall firewall show rule name="FlyClock-8080" dir=in >nul 2>&1
if %errorlevel% neq 0 (
    echo 防火墙规则不存在，正在添加...
    netsh advfirewall firewall add rule name="FlyClock-8080" dir=in action=allow protocol=TCP localport=8080
    if %errorlevel% equ 0 (
        echo ✓ 防火墙规则添加成功
    ) else (
        echo ✗ 防火墙规则添加失败，请以管理员身份运行
    )
) else (
    echo ✓ 防火墙规则已存在
)

echo.
echo 2. 检查端口占用...
netstat -an | findstr :8080 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo ✓ 端口8080正在监听
) else (
    echo ✗ 端口8080未监听
)

echo.
echo 3. 启动HTTP服务器...
cd /d "%~dp0assets\web"
echo 当前目录: %cd%
echo 启动HTTP服务器在端口8080...
d:\trae\.venv\Scripts\python.exe -m http.server 8080 --bind 0.0.0.0

pause
