#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 干净版本打包脚本
基于run_with_log.py的完整打包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")

def check_pyinstaller():
    """检查PyInstaller"""
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("请先安装PyInstaller: pip install pyinstaller")
        return False

def build_toolclock():
    """构建ToolClock exe"""
    print("\\n构建ToolClock exe...")
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'ToolClock',
            
            # 添加数据文件
            '--add-data', 'gui;gui',
            '--add-data', 'core;core',
            '--add-data', 'assets;assets',
            '--add-data', 'window_detector_fixed.py;.',
            
            # 关键隐藏导入
            '--hidden-import', 'threading',
            '--hidden-import', 'time',
            '--hidden-import', 'logging',
            '--hidden-import', 'json',
            '--hidden-import', 'pathlib',
            
            # GUI相关
            '--hidden-import', 'tkinter',
            '--hidden-import', 'tkinter.ttk',
            '--hidden-import', 'tkinter.messagebox',
            '--hidden-import', 'tkinter.filedialog',
            '--hidden-import', 'tkinter.simpledialog',
            
            # 音频相关
            '--hidden-import', 'pygame',
            '--hidden-import', 'pygame.mixer',
            '--hidden-import', 'winsound',
            
            # Windows API
            '--hidden-import', 'win32gui',
            '--hidden-import', 'win32api',
            '--hidden-import', 'win32con',
            '--hidden-import', 'win32process',
            
            # 收集pygame模块
            '--collect-all', 'pygame',
            
            'run_with_log.py'
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("ToolClock exe构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("ToolClock 干净版本打包工具")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建exe
    success = build_toolclock()
    
    # 清理临时文件（保留dist目录）
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
    
    # 总结
    print("\\n" + "=" * 60)
    print("构建总结")
    print("=" * 60)
    print(f"ToolClock: {'成功' if success else '失败'}")
    
    if success:
        print("\\nToolClock exe构建成功！")
        print("输出文件: dist/ToolClock.exe")
        print("\\n基于: run_with_log.py (已验证正常工作)")
        print("包含: 所有必要的依赖和功能")
    else:
        print("\\n构建失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()