#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试 - 精确定位exe问题
"""

import tkinter as tk
import threading
import time

class MinimalTimer:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("Minimal Timer Test")
        self.window.geometry("300x150")
        
        self.is_running = False
        self.current_time = 10
        
        # 显示标签
        self.time_label = tk.Label(self.window, text="10", font=("Arial", 24))
        self.time_label.pack(pady=20)
        
        # 开始按钮
        self.start_button = tk.Button(self.window, text="开始倒计时", command=self.start_timer)
        self.start_button.pack(pady=10)
        
        # 状态标签
        self.status_label = tk.Label(self.window, text="准备就绪", fg="green")
        self.status_label.pack(pady=5)
        
        # 测试win32
        self.test_win32()
    
    def test_win32(self):
        """测试win32功能"""
        try:
            import win32gui
            foreground = win32gui.GetForegroundWindow()
            self.status_label.configure(text=f"win32正常: {foreground}", fg="green")
        except Exception as e:
            self.status_label.configure(text=f"win32错误: {e}", fg="red")
    
    def start_timer(self):
        """开始计时"""
        if not self.is_running:
            self.is_running = True
            self.start_button.configure(text="运行中...", state="disabled")
            
            # 启动计时线程
            timer_thread = threading.Thread(target=self.timer_loop, daemon=True)
            timer_thread.start()
            print("计时器线程已启动")
    
    def timer_loop(self):
        """计时循环"""
        print("进入计时循环")
        while self.is_running and self.current_time > 0:
            print(f"倒计时: {self.current_time}")
            time.sleep(1)
            self.current_time -= 1
            
            # 更新UI
            try:
                self.window.after(0, self.update_display)
            except Exception as e:
                print(f"UI更新失败: {e}")
        
        print("计时循环结束")
        self.window.after(0, self.timer_finished)
    
    def update_display(self):
        """更新显示"""
        self.time_label.configure(text=str(self.current_time))
    
    def timer_finished(self):
        """计时结束"""
        self.time_label.configure(text="完成!", fg="red")
        self.start_button.configure(text="重新开始", state="normal")
        self.is_running = False
        self.current_time = 10
    
    def run(self):
        """运行程序"""
        self.window.mainloop()

if __name__ == "__main__":
    print("启动最小化计时器测试...")
    app = MinimalTimer()
    app.run()