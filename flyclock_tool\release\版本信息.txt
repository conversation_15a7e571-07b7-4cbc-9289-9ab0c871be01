ToolClock - PPT/PDF全屏放映计时辅助工具
版本信息

========================================
版本: V1.1.0 (负数时间显示修复版)
构建时间: 2025-07-31
文件大小: 19.39 MB
========================================

## 🆕 V1.1.0 最新修复内容

✅ 文件名清理功能修复
   - 修复右键调整文件时间时文件名显示"润文档"等后缀的问题
   - 支持WPS全屏格式的正确解析

✅ 基于用户确认的智能判断逻辑
   - 实现计时结束后用户确认对话框的智能判断
   - 只有用户确认后，二次全屏才显示红色背景和负数时间

✅ 负数时间显示优化
   - 修复二次全屏时负数时间显示问题
   - 负数时间从-00:00开始动态递增（-00:01, -00:02...）
   - 二次全屏时不会自动退出全屏，保持演示状态

✅ 持续蜂鸣音问题修复
   - 修复用户点击确定按钮后蜂鸣音不停止的问题
   - 优化音频播放控制逻辑

✅ 计时器状态管理优化
   - 修复退出全屏时不应重置已确认计时器的问题
   - 完善计时器线程安全机制

## 🎯 本版本特性

✅ 智能全屏检测
   - 自动识别PPT/PDF全屏状态
   - 支持多种演示软件
   - 精准的检测算法

✅ 悬浮计时器
   - 始终置顶显示
   - 可拖拽移动位置
   - 正计时/倒计时模式

✅ 音频提醒系统
   - 到时提醒功能
   - 提前警告设置
   - 自定义提示音

✅ 用户界面
   - 右键上下文菜单
   - 完整设置界面
   - 外观自定义选项

✅ 配置管理
   - 自动保存设置
   - 默认配置支持
   - JSON格式配置文件

## 🔧 技术规格

- 开发语言: Python 3.13
- GUI框架: Tkinter
- 音频支持: pygame
- Windows API: pywin32
- 打包工具: PyInstaller

## 📦 文件信息

- 可执行文件: FlyClock.exe
- 文件大小: 30.98 MB
- 运行环境: Windows 10/11
- 依赖项: 无（已内置所有依赖）

## 🚀 安装说明

1. 无需安装，直接运行FlyClock.exe
2. 首次运行会创建配置文件
3. 所有设置自动保存

## 🔄 更新历史

### V1.1.0 (2025-07-31) - 负数时间显示修复版
- ✅ 修复文件名清理功能（移除"润文档"等后缀）
- ✅ 实现基于用户确认的智能判断逻辑
- ✅ 修复二次全屏时红色背景和负数时间显示问题
- ✅ 优化负数时间从-00:00开始的显示逻辑
- ✅ 修复持续蜂鸣音问题
- ✅ 完善计时器状态管理和线程安全
- ✅ 修复退出全屏时计时器重置问题
- ✅ 添加详细的调试信息和错误处理

### V1.0.0 (2025-07-29)
- 初始发布版本
- 实现核心计时功能
- 添加全屏检测算法
- 集成音频提醒系统
- 完成用户界面设计
- 移除远程控制功能（简化版本）

## 📋 已知限制

- 仅支持Windows平台
- 需要管理员权限进行窗口检测
- 某些特殊显示设置可能影响检测精度

## 🔮 后续计划

- 优化检测算法性能
- 增加更多演示软件支持
- 添加数据统计功能
- 考虑跨平台支持

========================================
开发者: AI Assistant (Claude)
构建环境: Python 3.13 + PyInstaller 6.14.2
========================================
