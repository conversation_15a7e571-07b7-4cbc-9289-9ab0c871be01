[Unit]
Description=MCP Memory Service with Consolidation and mDNS
Documentation=https://github.com/doobidoo/mcp-memory-service
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=hkr
Group=hkr
WorkingDirectory=/home/<USER>/repositories/mcp-memory-service
Environment=PATH=/home/<USER>/repositories/mcp-memory-service/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=PYTHONPATH=/home/<USER>/repositories/mcp-memory-service/src
Environment=MCP_CONSOLIDATION_ENABLED=true
Environment=MCP_MDNS_ENABLED=true
Environment=MCP_HTTPS_ENABLED=true
Environment=MCP_MDNS_SERVICE_NAME="memory"
Environment=MCP_MDNS_SERVICE_TYPE="_http._tcp.local."
Environment=MCP_HTTP_ENABLED=true
Environment=MCP_HTTP_HOST=0.0.0.0
Environment=MCP_HTTP_PORT=443
Environment=MCP_MEMORY_STORAGE_BACKEND=sqlite_vec
Environment=MCP_API_KEY=mcp-0b1ccbde2197a08dcb12d41af4044be6
ExecStart=/home/<USER>/repositories/mcp-memory-service/venv/bin/python /home/<USER>/repositories/mcp-memory-service/scripts/run_http_server.py
AmbientCapabilities=CAP_NET_BIND_SERVICE
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mcp-memory-service

[Install]
WantedBy=multi-user.target