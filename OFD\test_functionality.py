#!/usr/bin/env python3
"""
OFD转PDF工具功能测试脚本
用于测试各项具体功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试必要的导入"""
    print("=== 导入测试 ===")
    
    try:
        from easyofd import OFD
        print("✓ 成功导入 easyofd.OFD")
    except ImportError as e:
        print(f"✗ 导入 easyofd.OFD 失败: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ 成功导入 PyQt5.QtWidgets.QApplication")
    except ImportError as e:
        print(f"✗ 导入 PyQt5.QtWidgets.QApplication 失败: {e}")
        return False
    
    try:
        from preview_window import PreviewWindow
        print("✓ 成功导入 PreviewWindow")
    except ImportError as e:
        print(f"✗ 导入 PreviewWindow 失败: {e}")
        return False
        
    print()
    return True

def test_ofd_processing():
    """测试OFD处理功能"""
    print("=== OFD处理测试 ===")
    
    # 查找OFD文件
    ofd_files = list(Path(".").glob("*.ofd"))
    if not ofd_files:
        print("ℹ 没有找到OFD文件用于测试")
        print()
        return True
    
    print(f"找到 {len(ofd_files)} 个OFD文件")
    
    try:
        from easyofd import OFD
        
        # 测试第一个文件
        test_file = ofd_files[0]
        print(f"测试文件: {test_file}")
        
        # 读取文件
        with open(test_file, "rb") as f:
            data = f.read()
        print(f"✓ 成功读取文件，大小: {len(data)} 字节")
        
        # 解析OFD
        ofd = OFD()
        ofd.read(data)
        print("✓ 成功解析OFD文件")
        
        # 转换为PDF
        pdf_data = ofd.to_pdf()
        print(f"✓ 成功转换为PDF，大小: {len(pdf_data)} 字节")
        
        # 生成图像
        images = ofd.to_img()
        print(f"✓ 成功生成图像，共 {len(images)} 页")
        
    except Exception as e:
        print(f"✗ OFD处理过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    print()
    return True

def test_preview_window():
    """测试预览窗口功能"""
    print("=== 预览窗口测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from preview_window import PreviewWindow
        
        # 创建应用实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建预览窗口
        window = PreviewWindow()
        print("✓ 成功创建PreviewWindow实例")
        
        # 检查必要方法
        methods_to_check = ['load_file', 'load_ofd_file', 'load_pdf_file', 'show_page']
        for method in methods_to_check:
            if hasattr(window, method):
                print(f"✓ PreviewWindow具有{method}方法")
            else:
                print(f"✗ PreviewWindow缺少{method}方法")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ 预览窗口测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("OFD转PDF工具功能测试")
    print("=" * 40)
    
    # 运行各项测试
    tests = [
        test_imports,
        test_ofd_processing,
        test_preview_window
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试 {test.__name__} 运行时出错: {e}")
            results.append(False)
    
    # 输出总结
    print("=" * 40)
    print("测试总结:")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过!")
    else:
        print("❌ 部分测试失败")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)