2025-07-31 17:38:15,983 - INFO - ============================================================
2025-07-31 17:38:15,983 - INFO - 🔧 <PERSON><PERSON><PERSON><PERSON> 启动中...
2025-07-31 17:38:15,983 - INFO - ============================================================
2025-07-31 17:38:15,983 - INFO - 📦 导入模块...
2025-07-31 17:38:15,984 - ERROR - ✗ <PERSON><PERSON><PERSON>lock启动失败: No module named 'gui'
2025-07-31 17:38:15,984 - ERROR - Traceback (most recent call last):
  File "run_with_log.py", line 37, in main
ModuleNotFoundError: No module named 'gui'

2025-07-31 17:40:42,177 - INFO - ============================================================
2025-07-31 17:40:42,177 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:40:42,177 - INFO - ============================================================
2025-07-31 17:40:42,177 - INFO - 📦 导入模块...
2025-07-31 17:40:42,178 - ERROR - ✗ ToolClock启动失败: No module named 'gui'
2025-07-31 17:40:42,179 - ERROR - Traceback (most recent call last):
  File "run_with_log.py", line 37, in main
ModuleNotFoundError: No module named 'gui'

2025-07-31 17:41:45,206 - INFO - ============================================================
2025-07-31 17:41:45,206 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:41:45,206 - INFO - ============================================================
2025-07-31 17:41:45,206 - INFO - 📦 导入模块...
2025-07-31 17:41:45,243 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:41:45,266 - WARNING - pygame未安装，将使用系统默认音频播放
2025-07-31 17:41:45,266 - INFO - ✓ TimerWindow 从release目录导入成功
2025-07-31 17:41:45,266 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:41:45,266 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:41:45,381 - INFO - 窗口检测已启动
2025-07-31 17:41:45,381 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:41:45,381 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:41:45,381 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:41:45,381 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:41:45,381 - INFO - 📋 修复后的逻辑:
2025-07-31 17:41:45,381 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:41:45,381 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:41:45,381 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:41:45,381 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:41:45,381 - INFO - ============================================================
2025-07-31 17:41:45,381 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:41:45,381 - INFO - ============================================================
2025-07-31 17:41:45,381 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:50:42,934 - INFO - 窗口检测已停止
2025-07-31 17:50:42,935 - INFO - 已停止所有音频播放
2025-07-31 17:50:42,935 - INFO - 音频资源清理完成
