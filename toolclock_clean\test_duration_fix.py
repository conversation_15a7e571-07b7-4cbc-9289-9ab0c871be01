#!/usr/bin/env python3
"""
测试计时时长修复
验证set_duration方法是否正确保存配置
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config_manager import ConfigManager
from gui.timer_window import TimerWindow

def test_duration_fix():
    """测试计时时长修复"""
    print("=" * 60)
    print("测试计时时长修复")
    print("=" * 60)
    
    # 1. 创建配置管理器
    config_manager = ConfigManager()
    
    # 2. 检查初始配置
    print("\n1. 检查初始配置:")
    initial_duration = config_manager.get_setting('timer.duration', None)
    initial_default = config_manager.get_setting('timer.default_duration', '00:08:00')
    print(f"   timer.duration: {initial_duration}")
    print(f"   timer.default_duration: {initial_default}")
    
    # 3. 创建计时器窗口（不显示）
    print("\n2. 创建计时器窗口...")
    try:
        # 创建一个隐藏的根窗口
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        timer_window = TimerWindow()
        timer_window.window.withdraw()  # 隐藏计时器窗口
        
        print("   ✓ 计时器窗口创建成功")
        
        # 4. 测试set_duration方法
        print("\n3. 测试set_duration方法:")
        test_durations = ["00:05:00", "00:10:00", "00:15:00"]
        
        for duration in test_durations:
            print(f"\n   测试设置时长: {duration}")
            
            # 设置时长
            timer_window.set_duration(duration)
            
            # 检查内存中的值
            print(f"   内存中的total_time: {timer_window.total_time}秒")
            print(f"   内存中的current_time: {timer_window.current_time}秒")
            
            # 强制重新加载配置文件
            config_manager.load_config()

            # 检查配置文件中的值
            saved_duration = config_manager.get_setting('timer.duration')
            print(f"   配置文件中的timer.duration: {saved_duration}")
            
            # 验证是否一致
            if saved_duration == duration:
                print(f"   ✓ 配置保存成功")
            else:
                print(f"   ✗ 配置保存失败，期望: {duration}, 实际: {saved_duration}")
            
            time.sleep(1)
        
        # 5. 测试重新加载配置
        print("\n4. 测试重新加载配置:")
        timer_window.load_config()
        final_duration = config_manager.get_setting('timer.duration')
        print(f"   重新加载后的timer.duration: {final_duration}")
        print(f"   重新加载后的total_time: {timer_window.total_time}秒")
        
        # 清理
        timer_window.window.destroy()
        root.destroy()
        
        print("\n5. 测试完成!")
        print("   ✓ set_duration方法现在会正确保存配置到文件")
        
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_config_file():
    """检查配置文件内容"""
    print("\n" + "=" * 60)
    print("检查配置文件内容")
    print("=" * 60)
    
    config_path = Path.home() / ".flyclock" / "config.json"
    
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            timer_config = config.get('timer', {})
            print(f"timer.duration: {timer_config.get('duration', 'Not found')}")
            print(f"timer.default_duration: {timer_config.get('default_duration', 'Not found')}")
            
            files_config = config.get('files', {})
            file_timers = files_config.get('file_specific_timers', {})
            print(f"\n文件特定计时器:")
            for file_path, duration in file_timers.items():
                print(f"  {file_path}: {duration}")
                
        except Exception as e:
            print(f"读取配置文件失败: {e}")
    else:
        print("配置文件不存在")

if __name__ == "__main__":
    test_duration_fix()
    check_config_file()
