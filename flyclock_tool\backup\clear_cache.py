#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理Python缓存文件
"""

import os
import shutil
from pathlib import Path

def clear_cache():
    """清理所有Python缓存"""
    project_root = Path(__file__).parent
    
    # 清理__pycache__目录
    for pycache_dir in project_root.rglob('__pycache__'):
        try:
            shutil.rmtree(pycache_dir)
            print(f"✓ 删除缓存目录: {pycache_dir}")
        except Exception as e:
            print(f"✗ 删除失败: {pycache_dir} - {e}")
    
    # 清理.pyc文件
    for pyc_file in project_root.rglob('*.pyc'):
        try:
            pyc_file.unlink()
            print(f"✓ 删除缓存文件: {pyc_file}")
        except Exception as e:
            print(f"✗ 删除失败: {pyc_file} - {e}")
    
    print("缓存清理完成！")

if __name__ == "__main__":
    clear_cache()
