#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 倒计时修复综合测试脚本
测试所有修复后的功能
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def test_timer_thread_safety():
    """测试计时器线程安全性"""
    print("=" * 60)
    print("1. 计时器线程安全性测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.gui.timer_window import TimerWindow
        
        # 创建计时器实例
        timer = TimerWindow()
        timer.set_duration("00:00:10")  # 10秒测试
        
        print(f"✓ 计时器创建成功，设置时长: 10秒")
        print(f"  当前时间: {timer.current_time}秒")
        
        # 启动计时器
        timer.start_timer()
        print(f"✓ 计时器启动，状态: 运行={timer.is_running}, 暂停={timer.is_paused}")
        
        # 监控5秒
        for i in range(5):
            time.sleep(1)
            print(f"  第{i+1}秒: {timer.current_time}秒剩余")
        
        # 停止计时器
        timer.reset_timer()
        print(f"✓ 计时器已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 线程安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_matching():
    """测试文件匹配功能"""
    print("\n" + "=" * 60)
    print("2. 文件匹配功能测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置测试文件
        test_files = {
            'presentation.pptx': '00:10:00',
            'document.pdf': '00:15:00',
            'slides.ppt': '00:08:00',
            'C:\\Users\\<USER>\\Desktop\\report.pptx': '00:12:00'
        }
        config_manager.set_setting('files.file_specific_timers', test_files)
        
        detector = WindowDetectorFixed()
        
        # 测试各种窗口标题格式
        test_cases = [
            ("PowerPoint - presentation.pptx", "00:10:00"),
            ("Microsoft PowerPoint - presentation.pptx - 只读", "00:10:00"),
            ("Adobe Acrobat - document.pdf", "00:15:00"),
            ("slides.ppt - PowerPoint", "00:08:00"),
            ("report.pptx - Microsoft PowerPoint", "00:12:00"),
            ("unknown_file.pptx", None)
        ]
        
        print("测试窗口标题匹配:")
        success_count = 0
        for window_title, expected in test_cases:
            result = detector._get_file_specific_duration(window_title)
            if result == expected:
                print(f"✅ '{window_title}' -> {result}")
                success_count += 1
            else:
                print(f"❌ '{window_title}' -> 期望: {expected}, 实际: {result}")
        
        print(f"\n匹配成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count >= len(test_cases) * 0.8  # 80%成功率
        
    except Exception as e:
        print(f"❌ 文件匹配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fullscreen_simulation():
    """测试全屏检测模拟"""
    print("\n" + "=" * 60)
    print("3. 全屏检测模拟测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.gui.timer_window import TimerWindow
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置测试文件
        test_files = {
            'test_presentation.pptx': '00:02:00'  # 2分钟测试
        }
        config_manager.set_setting('files.file_specific_timers', test_files)
        config_manager.set_setting('timer.auto_start', True)
        
        # 创建计时器和检测器
        timer = TimerWindow()
        detector = WindowDetectorFixed(timer)
        
        print("✓ 计时器和检测器创建成功")
        
        # 模拟全屏检测
        window_title = "PowerPoint - test_presentation.pptx"
        print(f"\n🖥️ 模拟全屏检测: {window_title}")
        
        # 记录初始状态
        initial_time = timer.current_time
        print(f"📊 初始状态: {initial_time}秒")
        
        # 触发全屏检测
        detector._on_fullscreen_enter(12345, window_title, "POWERPNT.EXE")
        
        # 检查结果
        final_time = timer.current_time
        is_running = timer.is_running
        
        print(f"📊 检测后状态:")
        print(f"   - 时间: {final_time}秒")
        print(f"   - 运行状态: {is_running}")
        
        # 验证结果
        if final_time == 120:  # 2分钟 = 120秒
            print("✅ 时间设置正确")
        else:
            print(f"❌ 时间设置错误，期望120秒，实际{final_time}秒")
        
        if is_running:
            print("✅ 计时器已启动")
            
            # 监控几秒钟
            print("⏱️ 监控倒计时...")
            for i in range(3):
                time.sleep(1)
                print(f"   第{i+1}秒: {timer.current_time}秒剩余")
            
            timer.reset_timer()
            print("✅ 测试完成，计时器已重置")
        else:
            print("❌ 计时器未启动")
        
        return final_time == 120 and is_running
        
    except Exception as e:
        print(f"❌ 全屏检测模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边缘情况"""
    print("\n" + "=" * 60)
    print("4. 边缘情况测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.gui.timer_window import TimerWindow
        
        detector = WindowDetectorFixed()
        timer = TimerWindow()
        
        # 测试空配置
        print("🧪 测试空配置...")
        result = detector._get_file_specific_duration("test.pptx")
        if result is None:
            print("✅ 空配置处理正确")
        else:
            print(f"❌ 空配置处理错误: {result}")
        
        # 测试无效时间格式
        print("🧪 测试无效时间格式...")
        try:
            timer.set_duration("invalid_format")
            print("✅ 无效格式处理正确")
        except Exception as e:
            print(f"⚠️ 无效格式处理: {e}")
        
        # 测试特殊字符窗口标题
        print("🧪 测试特殊字符窗口标题...")
        special_titles = [
            "PowerPoint - 测试文档[只读].pptx",
            "Adobe Acrobat - 报告(最终版).pdf",
            "演示文稿 - 项目汇报-2024.pptx"
        ]
        
        for title in special_titles:
            result = detector._extract_filenames_from_title(title)
            print(f"   '{title}' -> {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边缘情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 ToolClock 倒计时修复综合测试")
    print("测试所有修复后的功能...")
    
    tests = [
        ("计时器线程安全性", test_timer_thread_safety),
        ("文件匹配功能", test_file_matching),
        ("全屏检测模拟", test_fullscreen_simulation),
        ("边缘情况", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！倒计时问题已修复")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
