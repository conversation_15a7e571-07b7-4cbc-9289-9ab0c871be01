#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件名清理功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from window_detector_fixed import WindowDetectorFixed

def test_filename_cleanup():
    """测试文件名清理功能"""
    print("=" * 60)
    print("🧪 测试文件名清理功能")
    print("=" * 60)
    
    # 创建窗口检测器实例
    detector = WindowDetectorFixed(None)
    
    # 测试用例
    test_cases = [
        "2025年上半年11工作总结与展望-李超.pptx - 润文档",
        "上半年工作总结-黎快人.pptx - 润文档",
        "年中工作总结-李宇锜（2025年）.pptx - WPS Office",
        "PowerPoint - 我的演示文稿.pptx",
        "WPS演示 - 项目汇报.pptx",
        "Adobe Acrobat - 报告.pdf",
        "简单文件名.pptx",
        "没有扩展名的标题 - 润文档"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}:")
        print(f"   原始: '{test_case}'")
        
        cleaned = detector._get_clean_filename(test_case)
        print(f"   清理后: '{cleaned}'")
        
        # 验证是否移除了"润文档"等后缀
        if "润文档" in test_case and "润文档" not in cleaned:
            print("   ✅ 成功移除'润文档'后缀")
        elif "WPS Office" in test_case and "WPS Office" not in cleaned:
            print("   ✅ 成功移除'WPS Office'后缀")
        elif "PowerPoint -" in test_case and "PowerPoint -" not in cleaned:
            print("   ✅ 成功移除'PowerPoint -'前缀")
        elif "WPS演示 -" in test_case and "WPS演示 -" not in cleaned:
            print("   ✅ 成功移除'WPS演示 -'前缀")
        elif "Adobe Acrobat -" in test_case and "Adobe Acrobat -" not in cleaned:
            print("   ✅ 成功移除'Adobe Acrobat -'前缀")
        else:
            print("   ℹ️ 无需特殊处理或已正确处理")
    
    print("\n" + "=" * 60)
    print("✅ 文件名清理功能测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_filename_cleanup()
