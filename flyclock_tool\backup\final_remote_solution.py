#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock远程控制最终解决方案
解决ERR_EMPTY_RESPONSE和404错误问题
"""

import sys
import os
import threading
import asyncio
import time
import socket
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_local_ip():
    """获取本机IP地址"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "127.0.0.1"

def main():
    print("🎯 FlyClock远程控制最终解决方案")
    print("=" * 50)
    
    # 端口配置
    http_port = 8080
    ws_port = 8081
    
    # Web目录配置
    web_dir = Path(__file__).parent / "assets" / "web"
    print(f"📁 Web目录: {web_dir}")
    print(f"📁 Web目录存在: {web_dir.exists()}")
    
    if not web_dir.exists():
        print("❌ Web目录不存在，无法启动服务器")
        return
    
    # 显示文件列表
    files = list(web_dir.glob("*"))
    print(f"📄 Web文件: {[f.name for f in files]}")
    
    # 网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP: {local_ip}")
    
    # 自定义HTTP请求处理器
    class FlyClock_HTTPRequestHandler(SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory=str(web_dir), **kwargs)
        
        def log_message(self, format, *args):
            print(f"📡 HTTP请求: {format % args}")
        
        def end_headers(self):
            # 添加CORS头部，允许跨域访问
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    # 启动HTTP服务器
    def run_http_server():
        try:
            # 确保绑定到所有接口
            httpd = HTTPServer(('0.0.0.0', http_port), FlyClock_HTTPRequestHandler)
            print(f"✅ HTTP服务器启动成功")
            print(f"   端口: {http_port}")
            print(f"   绑定: 0.0.0.0 (所有接口)")
            print(f"📱 本地访问: http://localhost:{http_port}/remote_client.html")
            print(f"📱 局域网访问: http://{local_ip}:{http_port}/remote_client.html")
            print(f"🔗 测试连接: http://{local_ip}:{http_port}/test_connection.html")
            
            # 启动服务器
            httpd.serve_forever()
            
        except Exception as e:
            print(f"❌ HTTP服务器启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 启动WebSocket服务器
    def run_websocket_server():
        try:
            from remote.websocket_server import RemoteControlServer
            from gui.timer_window import TimerWindow
            
            # 创建计时器窗口
            timer_window = TimerWindow()
            
            # 在后台线程中运行计时器窗口
            def timer_thread():
                try:
                    timer_window.run()
                except Exception as e:
                    print(f"计时器窗口运行失败: {e}")
            
            threading.Thread(target=timer_thread, daemon=True).start()
            time.sleep(2)  # 等待计时器窗口初始化
            
            # 创建WebSocket服务器
            server = RemoteControlServer(timer_window=timer_window, port=ws_port)
            
            # 启动WebSocket服务器
            async def start_ws():
                await server.start_server()
                print(f"✅ WebSocket服务器启动成功，端口: {ws_port}")
                
                # 保持运行
                try:
                    await asyncio.Future()
                except asyncio.CancelledError:
                    await server.stop_server()
            
            # 运行WebSocket服务器
            asyncio.run(start_ws())
            
        except Exception as e:
            print(f"❌ WebSocket服务器启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    try:
        print(f"\n🚀 启动服务器...")
        
        # 启动HTTP服务器线程
        http_thread = threading.Thread(target=run_http_server, daemon=True)
        http_thread.start()
        
        # 等待HTTP服务器启动
        time.sleep(2)
        
        # 启动WebSocket服务器线程
        ws_thread = threading.Thread(target=run_websocket_server, daemon=True)
        ws_thread.start()
        
        print(f"\n🎉 FlyClock远程控制服务器启动完成！")
        print(f"📱 请在手机或其他设备上访问: http://{local_ip}:{http_port}/remote_client.html")
        print(f"🔧 如果无法访问，请检查防火墙设置")
        print(f"💡 防火墙命令: netsh advfirewall firewall add rule name=\"FlyClock-{http_port}\" dir=in action=allow protocol=TCP localport={http_port}")
        print(f"\n按 Ctrl+C 停止服务器...")
        
        # 保持主线程运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n🛑 服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
