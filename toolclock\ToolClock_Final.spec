# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('core', 'core'),
        ('assets', 'assets'),
        ('window_detector_fixed.py', '.'),
    ],
    hiddenimports=[
        # 核心模块
        'threading',
        'time',
        'logging',
        'json',
        'pathlib',
        'datetime',
        'subprocess',
        're',
        
        # GUI模块
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.simpledialog',
        
        # 音频模块
        'pygame',
        'pygame.mixer',
        'pygame.sndarray',
        'winsound',
        'numpy',
        
        # Windows API
        'win32gui',
        'win32api',
        'win32con',
        'win32process',
        
        # 其他
        'socket',
        'urllib',
        'urllib.request',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集所有pygame和numpy子模块
a.datas += Tree('C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Lib/site-packages/pygame', prefix='pygame')

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ToolClock_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
