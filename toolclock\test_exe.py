#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试exe文件功能的脚本
"""

import subprocess
import time
import sys
from pathlib import Path

def test_exe_functionality():
    """测试exe文件的功能"""
    print("=" * 60)
    print("ToolClock exe功能测试")
    print("=" * 60)
    
    exe_path = Path("dist/ToolClock_Complete.exe")
    
    if not exe_path.exists():
        print("❌ exe文件不存在")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    print(f"📦 文件大小: {exe_path.stat().st_size / (1024*1024):.2f} MB")
    
    print("\n🚀 启动exe文件测试...")
    
    try:
        # 启动exe文件
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        print("✅ exe文件已启动")
        print("⏱️ 等待5秒检查进程状态...")
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ exe文件正在运行中")
            print("🎯 测试结果: exe文件启动成功，进程正常运行")
            
            # 终止进程
            process.terminate()
            process.wait(timeout=5)
            print("✅ 测试完成，进程已终止")
            return True
        else:
            print("❌ exe文件启动后立即退出")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"标准输出: {stdout}")
            if stderr:
                print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动exe文件失败: {e}")
        return False

def compare_versions():
    """比较不同版本的exe文件"""
    print("\n" + "=" * 60)
    print("版本对比")
    print("=" * 60)
    
    versions = [
        ("ToolClock.exe", "原始版本"),
        ("dist/ToolClock_Clean.exe", "干净版本"),
        ("dist/ToolClock_Complete.exe", "完整版本")
    ]
    
    for exe_file, description in versions:
        exe_path = Path(exe_file)
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024*1024)
            print(f"✅ {description}: {exe_file} ({size_mb:.2f} MB)")
        else:
            print(f"❌ {description}: {exe_file} (不存在)")

def main():
    """主函数"""
    # 切换到正确的目录
    script_dir = Path(__file__).parent
    import os
    os.chdir(script_dir)
    
    # 测试exe功能
    success = test_exe_functionality()
    
    # 比较版本
    compare_versions()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 ToolClock_Complete.exe 测试通过！")
        print("📋 建议使用: dist/ToolClock_Complete.exe")
        print("🔧 该版本包含所有必要的依赖和功能")
    else:
        print("❌ exe测试失败，请检查打包配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()