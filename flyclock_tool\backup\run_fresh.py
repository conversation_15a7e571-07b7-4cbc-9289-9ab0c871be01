#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重新加载运行脚本
避免缓存问题
"""

import sys
import os
import importlib
from pathlib import Path

# 禁用字节码缓存
sys.dont_write_bytecode = True

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 清理已导入的模块
modules_to_remove = []
for module_name in sys.modules:
    if any(module_name.startswith(prefix) for prefix in ['core.', 'gui.', 'remote.']):
        modules_to_remove.append(module_name)

for module_name in modules_to_remove:
    del sys.modules[module_name]

print("已清理模块缓存，强制重新加载...")

# 现在运行主程序
if __name__ == "__main__":
    # 直接执行main.py的内容
    main_file = os.path.join(project_root, 'main.py')
    with open(main_file, 'r', encoding='utf-8') as f:
        main_code = f.read()
    
    # 执行main.py
    exec(main_code)
