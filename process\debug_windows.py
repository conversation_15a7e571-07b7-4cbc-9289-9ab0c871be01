#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试窗口获取问题
"""

import pygetwindow as gw

def debug_all_windows():
    """调试所有窗口信息"""
    print("=== 调试所有窗口信息 ===")
    
    try:
        # 获取所有窗口
        all_windows = gw.getAllWindows()
        print(f"总窗口数: {len(all_windows)}")
        
        current_title = "ALT+TAB 窗口切换工具"
        
        print(f"\n=== 所有窗口详细信息 ===")
        valid_count = 0
        
        for i, window in enumerate(all_windows):
            try:
                title = window.title if window.title else "[无标题]"
                is_minimized = window.isMinimized
                is_visible = window.visible
                width = window.width
                height = window.height
                
                # 检查各个过滤条件
                has_title = window.title and not window.title.isspace()
                not_self = window.title != current_title
                not_minimized = not window.isMinimized
                is_visible_check = window.visible
                size_ok = window.width > 50 and window.height > 50
                not_system = window.title not in ['Program Manager', 'Desktop']
                
                print(f"\n{i+1:2d}. 标题: {title[:60]}")
                print(f"    尺寸: {width}x{height}")
                print(f"    最小化: {is_minimized}, 可见: {is_visible}")
                print(f"    过滤检查:")
                print(f"      有标题: {has_title}")
                print(f"      非自身: {not_self}")
                print(f"      未最小化: {not_minimized}")
                print(f"      可见: {is_visible_check}")
                print(f"      尺寸合适: {size_ok}")
                print(f"      非系统窗口: {not_system}")
                
                # 检查是否通过所有过滤条件
                passes_filter = (has_title and not_self and not_minimized and 
                               is_visible_check and size_ok and not_system)
                
                print(f"    通过过滤: {passes_filter}")
                
                if passes_filter:
                    valid_count += 1
                    
            except Exception as e:
                print(f"{i+1:2d}. [错误] {e}")
        
        print(f"\n=== 总结 ===")
        print(f"总窗口数: {len(all_windows)}")
        print(f"通过过滤的窗口数: {valid_count}")
        
        return valid_count
        
    except Exception as e:
        print(f"调试失败: {e}")
        return 0

def debug_relaxed_filter():
    """使用放宽的过滤条件"""
    print(f"\n=== 使用放宽的过滤条件 ===")
    
    try:
        all_windows = gw.getAllWindows()
        current_title = "ALT+TAB 窗口切换工具"
        
        valid_windows = []
        
        for window in all_windows:
            try:
                # 放宽的过滤条件
                if (window.title and 
                    not window.title.isspace() and 
                    window.title != current_title and
                    window.width > 10 and  # 降低尺寸要求
                    window.height > 10 and
                    window.title not in ['Program Manager', 'Desktop']):
                    
                    valid_windows.append({
                        'title': window.title,
                        'size': f"{window.width}x{window.height}",
                        'minimized': window.isMinimized,
                        'visible': window.visible
                    })
                    
            except Exception:
                continue
        
        print(f"放宽条件后的有效窗口数: {len(valid_windows)}")
        
        for i, win in enumerate(valid_windows[:20]):  # 显示前20个
            status = []
            if win['minimized']:
                status.append("最小化")
            if not win['visible']:
                status.append("不可见")
            status_str = f" [{', '.join(status)}]" if status else ""
            
            print(f"  {i+1:2d}. {win['title'][:50]:<50} | {win['size']:<10}{status_str}")
        
        return len(valid_windows)
        
    except Exception as e:
        print(f"放宽过滤调试失败: {e}")
        return 0

def main():
    debug_all_windows()
    debug_relaxed_filter()

if __name__ == "__main__":
    main()