# ToolClock V1.1.0 标准版本

## 📦 文件信息

**文件名**：ToolClock_V1.1.0_Standard.exe  
**文件大小**：7.2 MB  
**版本**：V1.1.0 标准版本  
**发布日期**：2025-07-31

## 🎯 版本特性

### ✅ 核心功能
- **自动全屏检测**：支持PPT、PDF等演示文件的全屏检测
- **智能计时控制**：自动开始/停止计时，精确到秒
- **文件名清理**：自动移除"润文档"、"WPS Office"等后缀
- **悬浮窗口**：始终置顶显示，可拖拽移动位置

### ✅ 智能负数时间显示
- **基于用户确认的智能判断**：只有用户点击确定按钮后才激活负数显示
- **红色背景提醒**：二次全屏时显示红色背景
- **负数时间递增**：从-00:00开始动态递增（-00:01, -00:02...）
- **不自动退出**：二次全屏时不会自动退出全屏，保持演示状态

### ✅ 用户交互
- **右键菜单**：设置时间、调整文件时间、退出等功能
- **双击操作**：双击计时器可快速开始/暂停
- **音频提醒**：时间结束时播放提醒音（可配置）

## 🚀 使用方法

### 标准使用流程：
1. **启动程序**：双击ToolClock_V1.1.0_Standard.exe
2. **设置时间**：右键计时器窗口 → 选择"设置时间" → 输入时间（如05:00）
3. **开始演示**：打开PPT/PDF → 按F5进入全屏放映
4. **自动计时**：程序检测到全屏后自动开始倒计时
5. **时间结束**：
   - 自动退出全屏
   - 显示"演示时间已到"对话框
   - 播放提醒音
6. **用户确认**：点击对话框的"确定"按钮
7. **二次全屏**：再次按F5进入全屏
8. **负数显示**：
   - 显示红色背景
   - 显示负数时间（-00:00, -00:01, -00:02...）
   - 不会自动退出全屏

### 快捷操作：
- **拖拽移动**：按住计时器窗口拖拽到合适位置
- **双击控制**：双击计时器快速开始/暂停
- **右键菜单**：右键访问所有功能选项

## 📝 右键菜单功能

- **设置时间**：设置倒计时时间（支持MM:SS或HH:MM:SS格式）
- **调整文件时间**：为特定文件设置专用时间
- **退出**：关闭程序

## 🎯 适用场景

- **正式演示**：商务演示、学术报告等正式场合
- **教学培训**：课堂教学、培训讲座等教育场景
- **会议演讲**：会议发言、项目汇报等会议场景
- **时间控制**：需要精确时间控制的任何演示场合

## 🔧 技术特点

- **单文件运行**：无需安装，双击即用
- **无依赖要求**：所有依赖已打包，无需额外安装
- **系统兼容**：支持Windows 10/11系统
- **资源占用**：轻量级设计，占用资源少
- **稳定可靠**：经过充分测试，运行稳定

## 💡 使用提示

1. **首次使用**：建议先设置短时间（如10秒）测试功能
2. **文件识别**：程序会自动识别演示文件，无需手动配置
3. **窗口位置**：可将计时器拖拽到屏幕角落，避免遮挡内容
4. **时间格式**：支持分:秒（05:30）或时:分:秒（01:05:30）格式
5. **退出方式**：右键菜单选择"退出"或直接关闭窗口

## 🆕 V1.1.0 更新内容

- ✅ 修复文件名清理功能，正确处理WPS和Office文件
- ✅ 实现基于用户确认的智能判断逻辑
- ✅ 修复二次全屏时红色背景和负数时间显示问题
- ✅ 优化负数时间显示逻辑，从-00:00开始递增
- ✅ 修复持续蜂鸣音问题，用户确认后自动停止
- ✅ 完善计时器状态管理，提高稳定性
- ✅ 优化线程安全机制，避免界面卡顿

## 📞 常见问题

**Q: 程序无法检测到全屏？**
A: 确保PPT/PDF处于真正的全屏状态，程序会自动检测窗口尺寸变化。

**Q: 负数时间不显示？**
A: 确保在时间结束后点击了"确定"按钮，只有确认后才会激活负数显示。

**Q: 如何停止计时？**
A: 双击计时器窗口或右键选择相应选项可以暂停/停止计时。

**Q: 程序占用资源多吗？**
A: 程序采用轻量级设计，正常运行时占用资源很少。

---

**开发者**：AI Assistant + MCP工具集成  
**技术支持**：基于Claude Sonnet 4 + 多种MCP工具协作开发

如有问题或建议，请参考项目文档。
