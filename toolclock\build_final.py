#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 最终版本打包脚本
基于问题分析的精确打包配置
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")

def check_dependencies():
    """检查所有依赖是否可用"""
    print("检查依赖模块...")
    
    required_modules = [
        'tkinter', 'threading', 'time', 'logging', 'json', 'pathlib',
        'pygame', 'winsound', 'numpy', 
        'win32gui', 'win32api', 'win32con', 'win32process'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"OK {module}")
        except ImportError:
            print(f"MISSING {module}")
            missing.append(module)
    
    if missing:
        print(f"\\n警告: 缺少模块 {missing}")
        print("这可能导致exe功能不完整")
    else:
        print("\\n所有依赖模块都可用")
    
    return len(missing) == 0

def create_spec_file():
    """创建自定义的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('core', 'core'),
        ('assets', 'assets'),
        ('window_detector_fixed.py', '.'),
    ],
    hiddenimports=[
        # 核心模块
        'threading',
        'time',
        'logging',
        'json',
        'pathlib',
        'datetime',
        'subprocess',
        're',
        
        # GUI模块
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.simpledialog',
        
        # 音频模块
        'pygame',
        'pygame.mixer',
        'pygame.sndarray',
        'winsound',
        'numpy',
        
        # Windows API
        'win32gui',
        'win32api',
        'win32con',
        'win32process',
        
        # 其他
        'socket',
        'urllib',
        'urllib.request',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集所有pygame和numpy子模块
a.datas += Tree('C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Lib/site-packages/pygame', prefix='pygame')

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ToolClock_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('ToolClock_Final.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建自定义spec文件: ToolClock_Final.spec")

def build_with_spec():
    """使用spec文件构建"""
    print("\\n使用spec文件构建exe...")
    try:
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'ToolClock_Final.spec']
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("ToolClock_Final.exe构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("ToolClock 最终版本打包工具")
    print("=" * 60)
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 构建exe
    success = build_with_spec()
    
    # 清理临时文件（保留dist目录）
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
    
    # 总结
    print("\\n" + "=" * 60)
    print("构建总结")
    print("=" * 60)
    print(f"依赖检查: {'通过' if deps_ok else '有警告'}")
    print(f"ToolClock_Final: {'成功' if success else '失败'}")
    
    if success:
        print("\\nToolClock_Final.exe构建成功！")
        print("输出文件: dist/ToolClock_Final.exe")
        print("\\n请测试以下功能:")
        print("1. 程序启动")
        print("2. 计时器倒计时功能")
        print("3. 音频播放")
        print("4. 窗口检测")
    else:
        print("\\n构建失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()