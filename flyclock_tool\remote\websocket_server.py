"""
FlyClock 远程控制 WebSocket 服务器
提供文件预览、翻页控制、播放暂停等功能
"""

import asyncio
import json
import logging
import os
import base64
import socket
from pathlib import Path
from typing import Dict, Set, Optional, List
import websockets
import threading
import time

# 导入键盘控制
try:
    import win32api
    import win32con
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RemoteControlServer:
    """远程控制服务器"""
    
    def __init__(self, timer_window=None, port=8081, password=""):
        """
        初始化远程控制服务器

        Args:
            timer_window: 计时器窗口实例
            port: 服务器端口
            password: 访问密码
        """
        self.timer_window = timer_window
        self.port = port
        self.password = password
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.server = None
        self.is_running = False
        
        # 文件管理
        self.current_file = None
        self.current_page = 1
        self.total_pages = 0
        self.files_cache = {}
        
        # 支持的文件类型
        self.supported_extensions = {'.pptx', '.ppt', '.pdf'}
        
        # 扫描文件目录
        self.scan_directories = [
            os.path.expanduser("~/Desktop"),
            os.path.expanduser("~/Documents"),
            "."  # 当前目录
        ]
    
    async def start_server(self):
        """启动WebSocket服务器"""
        try:
            # 监听所有网络接口，允许局域网访问
            self.server = await websockets.serve(
                self.handle_client,
                "0.0.0.0",  # 监听所有接口
                self.port
            )
            self.is_running = True

            # 获取本机IP地址
            local_ip = self.get_local_ip()
            logger.info(f"远程控制服务器已启动")
            logger.info(f"本地访问: http://localhost:{self.port}")
            logger.info(f"局域网访问: http://{local_ip}:{self.port}")

            # 不在启动时扫描文件，只在需要时扫描
            # await self.scan_files()

            return True

        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            return False
    
    async def stop_server(self):
        """停止WebSocket服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            self.is_running = False
            logger.info("远程控制服务器已停止")
    
    async def handle_client(self, websocket):
        """处理客户端连接"""
        client_address = websocket.remote_address
        logger.info(f"客户端连接: {client_address}")
        
        try:
            # 添加到客户端列表
            self.clients.add(websocket)
            
            # 发送欢迎消息和文件列表
            await self.send_welcome(websocket)
            
            # 处理客户端消息
            async for message in websocket:
                await self.handle_message(websocket, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {client_address}")
        except Exception as e:
            logger.error(f"处理客户端连接时出错: {e}")
        finally:
            # 从客户端列表中移除
            self.clients.discard(websocket)
    
    async def handle_message(self, websocket, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'auth':
                await self.handle_auth(websocket, data)
            elif message_type == 'get_files':
                await self.handle_get_files(websocket)
            elif message_type == 'select_file':
                await self.handle_select_file(websocket, data)
            elif message_type == 'control':
                await self.handle_control(websocket, data)
            elif message_type == 'get_preview':
                await self.handle_get_preview(websocket, data)
            else:
                await self.send_error(websocket, f"未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            await self.send_error(websocket, "无效的JSON格式")
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            await self.send_error(websocket, f"服务器错误: {str(e)}")
    
    async def handle_auth(self, websocket, data):
        """处理认证"""
        password = data.get('password', '')
        
        if not self.password or password == self.password:
            await self.send_message(websocket, {
                'type': 'auth_result',
                'success': True,
                'message': '认证成功'
            })
        else:
            await self.send_message(websocket, {
                'type': 'auth_result',
                'success': False,
                'message': '密码错误'
            })
    
    async def handle_get_files(self, websocket):
        """处理获取文件列表请求"""
        file_list = []

        # 首先检查是否有当前加载的文件
        if self.timer_window:
            current_presentation = self.timer_window.get_presentation_status()
            if current_presentation and current_presentation.get('file_name'):
                file_name = current_presentation['file_name']
                # 尝试从文件名中提取实际路径
                actual_path = file_name

                # 如果文件名包含完整路径，使用它
                if os.path.exists(file_name):
                    actual_path = file_name
                else:
                    # 尝试在常见目录中查找文件
                    for scan_dir in self.scan_directories:
                        if os.path.exists(scan_dir):
                            for root, dirs, files in os.walk(scan_dir):
                                for file in files:
                                    if file == file_name or file_name in file:
                                        actual_path = os.path.join(root, file)
                                        break

                # 获取文件信息
                file_size = 0
                file_modified = int(time.time())
                if os.path.exists(actual_path):
                    try:
                        stat = os.stat(actual_path)
                        file_size = stat.st_size
                        file_modified = int(stat.st_mtime)
                    except OSError:
                        pass

                # 添加当前正在演示的文件
                file_list.append({
                    'name': os.path.basename(file_name),
                    'path': actual_path,
                    'size': file_size,
                    'modified': file_modified,
                    'type': 'current',
                    'is_current': True,
                    'is_fullscreen': current_presentation.get('is_fullscreen', False),
                    'source': 'presentation'  # 标识为演示文件
                })

        # 扫描其他可用文件
        scanned_files = await self.get_available_files()

        # 合并文件列表，当前文件在前
        for file_info in scanned_files:
            # 避免重复添加当前文件
            if not any(f.get('path') == file_info['path'] for f in file_list):
                file_list.append(file_info)

        # 如果没有找到任何文件，添加提示信息
        if not file_list:
            file_list.append({
                'name': '未找到支持的文件',
                'path': '',
                'size': 0,
                'modified': int(time.time()),
                'type': 'info',
                'is_current': False,
                'is_fullscreen': False,
                'source': 'system'
            })

        await self.send_message(websocket, {
            'type': 'file_list',
            'files': file_list
        })
    
    async def handle_select_file(self, websocket, data):
        """处理选择文件请求"""
        file_path = data.get('file_path')
        logger.info(f"收到文件选择请求: {file_path}")

        if file_path and (file_path in self.files_cache or os.path.exists(file_path)):
            self.current_file = file_path
            self.current_page = 1

            # 检查文件类型
            file_ext = os.path.splitext(file_path)[1].lower()
            logger.info(f"文件类型: {file_ext}")

            # 获取文件的总页数
            logger.info(f"开始获取文件页数: {file_path}")
            self.total_pages = await self._get_file_page_count(file_path)
            logger.info(f"文件页数: {self.total_pages}")

            await self.send_message(websocket, {
                'type': 'file_selected',
                'file_path': file_path,
                'current_page': self.current_page,
                'total_pages': self.total_pages
            })

            # 生成并发送预览
            logger.info(f"开始生成预览: {file_path} 第{self.current_page}页")
            preview_data = await self.generate_preview(file_path, self.current_page)
            logger.info(f"预览生成完成，数据长度: {len(preview_data) if preview_data else 0}")
            await self.send_message(websocket, {
                'type': 'preview',
                'file_path': file_path,
                'page': self.current_page,
                'image_data': preview_data
            })

            # 广播给所有客户端
            await self.broadcast_status()
        else:
            await self.send_error(websocket, "文件不存在")

    async def handle_get_preview(self, websocket, data):
        """处理获取预览请求"""
        file_path = data.get('file_path')
        page = data.get('page', 1)

        if not file_path:
            await self.send_error(websocket, "缺少文件路径")
            return

        if not os.path.exists(file_path):
            await self.send_error(websocket, "文件不存在")
            return

        try:
            # 生成预览
            preview_data = await self.generate_preview(file_path, page)

            await self.send_message(websocket, {
                'type': 'preview',
                'file_path': file_path,
                'page': page,
                'image_data': preview_data
            })

        except Exception as e:
            logger.error(f"生成预览失败: {e}")
            await self.send_error(websocket, f"生成预览失败: {str(e)}")

    async def handle_control(self, websocket, data):
        """处理控制命令"""
        action = data.get('action')

        if not self.timer_window:
            await self.send_error(websocket, "FlyClock未运行")
            return

        try:
            if action == 'play':
                # 启动计时器 - 在主线程中执行
                if hasattr(self.timer_window, 'start_timer'):
                    self.timer_window.window.after(0, self.timer_window.start_timer)
                    logger.info("远程控制: 启动计时器")
            elif action == 'pause':
                # 暂停计时器 - 在主线程中执行
                if hasattr(self.timer_window, 'pause_timer'):
                    self.timer_window.window.after(0, self.timer_window.pause_timer)
                    logger.info("远程控制: 暂停计时器")
            elif action == 'reset':
                # 重置计时器 - 在主线程中执行
                if hasattr(self.timer_window, 'reset_timer'):
                    self.timer_window.window.after(0, self.timer_window.reset_timer)
                    logger.info("远程控制: 重置计时器")
            elif action == 'next_page':
                # 下一页 (发送键盘事件并更新预览)
                await self.send_key_event('Right')
                # 等待一下让PPT翻页完成，然后重新生成预览
                await asyncio.sleep(0.1)
                if self.current_file and self.current_page < self.total_pages:
                    self.current_page += 1
                    await self._broadcast_current_preview()
                logger.info("远程控制: 下一页")
            elif action == 'prev_page':
                # 上一页 (发送键盘事件并更新预览)
                await self.send_key_event('Left')
                # 等待一下让PPT翻页完成，然后重新生成预览
                await asyncio.sleep(0.1)
                if self.current_file and self.current_page > 1:
                    self.current_page -= 1
                    await self._broadcast_current_preview()
                logger.info("远程控制: 上一页")
            elif action == 'fullscreen':
                # 全屏 (F5键)
                await self.send_key_event('F5')
                logger.info("远程控制: 进入全屏")
            elif action == 'exit_fullscreen':
                # 退出全屏 (ESC键)
                await self.send_key_event('Escape')
                logger.info("远程控制: 退出全屏")
            else:
                await self.send_error(websocket, f"未知控制命令: {action}")
                return

            # 发送状态更新
            await self.send_status_update(websocket)

        except Exception as e:
            logger.error(f"执行控制命令失败: {e}")
            await self.send_error(websocket, f"执行命令失败: {str(e)}")
    
    async def handle_get_preview(self, websocket, data):
        """处理获取预览请求"""
        file_path = data.get('file_path')
        page = data.get('page', 1)
        
        if not file_path or file_path not in self.files_cache:
            await self.send_error(websocket, "文件不存在")
            return
        
        # 这里应该生成文件预览图片
        # 暂时返回占位符
        preview_data = await self.generate_preview(file_path, page)
        
        await self.send_message(websocket, {
            'type': 'preview',
            'file_path': file_path,
            'page': page,
            'image_data': preview_data
        })
    
    async def scan_files(self):
        """扫描支持的文件"""
        self.files_cache.clear()
        
        for directory in self.scan_directories:
            if not os.path.exists(directory):
                continue
                
            try:
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_ext = Path(file).suffix.lower()
                        
                        if file_ext in self.supported_extensions:
                            try:
                                stat = os.stat(file_path)
                                self.files_cache[file_path] = {
                                    'name': file,
                                    'size': stat.st_size,
                                    'modified': stat.st_mtime,
                                    'type': file_ext[1:]  # 去掉点号
                                }
                            except OSError:
                                continue
                                
            except Exception as e:
                logger.error(f"扫描目录 {directory} 时出错: {e}")
        
        logger.info(f"扫描到 {len(self.files_cache)} 个支持的文件")

    async def get_available_files(self):
        """获取可用文件列表"""
        all_files = []

        # 首先获取配置中的文件（优先显示）
        config_files = self._get_config_files()
        all_files.extend(config_files)

        # 扫描目录列表
        scan_dirs = [
            os.getcwd(),  # 当前目录
            os.path.expanduser("~/Desktop"),  # 桌面
            os.path.expanduser("~/Documents"),  # 文档
            os.path.expanduser("~/Downloads"),  # 下载
        ]

        # 记录已添加的文件路径，避免重复
        added_paths = {f['path'] for f in config_files}

        for directory in scan_dirs:
            if not os.path.exists(directory):
                continue

            try:
                # 递归扫描所有子目录
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_ext = Path(file).suffix.lower()

                        if file_ext in self.supported_extensions and file_path not in added_paths:
                            try:
                                stat = os.stat(file_path)
                                all_files.append({
                                    'path': file_path,
                                    'name': file,
                                    'size': stat.st_size,
                                    'modified': int(stat.st_mtime),
                                    'type': file_ext[1:],
                                    'is_current': False,
                                    'source': 'file_system'
                                })
                                added_paths.add(file_path)
                            except OSError:
                                continue

            except Exception as e:
                logger.error(f"扫描目录 {directory} 时出错: {e}")

        # 限制文件数量，避免列表过长
        logger.info(f"扫描到 {len(all_files)} 个支持的文件")
        return all_files[:50]

    def _get_config_files(self):
        """获取配置中的文件列表"""
        config_files = []

        if not self.timer_window or not hasattr(self.timer_window, 'config_manager'):
            return config_files

        try:
            # 获取配置数据
            file_timers = self.timer_window.config_manager.get_setting('files.file_specific_timers', {})
            analysis_cache = self.timer_window.config_manager.get_setting('files.analysis_cache', {})
            file_order = self.timer_window.config_manager.get_setting('files.file_order', [])

            # 创建文件信息字典
            file_info = {}

            # 收集手动设置的文件
            for file_path, duration in file_timers.items():
                filename = os.path.basename(file_path)
                file_info[filename] = {
                    'path': file_path,
                    'name': filename,
                    'duration': duration,
                    'source': 'manual_setting'
                }

            # 收集智能分析的结果
            for file_path, cache_data in analysis_cache.items():
                filename = os.path.basename(file_path)

                # 如果没有手动设置，则使用智能分析结果
                if filename not in file_info:
                    analysis_result = cache_data.get('analysis_result', {})
                    file_type = analysis_result.get('file_type', 'unknown')
                    page_count = analysis_result.get('page_count', 0)

                    file_info[filename] = {
                        'path': file_path,
                        'name': filename,
                        'duration': analysis_result.get('suggested_time', 1800),
                        'source': f'smart_analysis_{page_count}_{file_type}'
                    }

            # 按照保存的顺序添加文件
            for filename in file_order:
                if filename in file_info:
                    info = file_info[filename]
                    if os.path.exists(info['path']):
                        try:
                            stat = os.stat(info['path'])
                            config_files.append({
                                'path': info['path'],
                                'name': info['name'],
                                'size': stat.st_size,
                                'modified': int(stat.st_mtime),
                                'type': Path(info['path']).suffix[1:].lower(),
                                'is_current': False,
                                'source': info['source'],
                                'duration': info['duration']
                            })
                        except OSError:
                            continue

            # 添加未在顺序列表中的文件（新添加的文件）
            for filename, info in file_info.items():
                if filename not in file_order and os.path.exists(info['path']):
                    try:
                        stat = os.stat(info['path'])
                        config_files.append({
                            'path': info['path'],
                            'name': info['name'],
                            'size': stat.st_size,
                            'modified': int(stat.st_mtime),
                            'type': Path(info['path']).suffix[1:].lower(),
                            'is_current': False,
                            'source': info['source'],
                            'duration': info['duration']
                        })
                    except OSError:
                        continue

            logger.info(f"从配置中获取到 {len(config_files)} 个文件")

        except Exception as e:
            logger.error(f"获取配置文件列表失败: {e}")

        return config_files

    async def scan_recent_files(self):
        """扫描最近修改的文件（限制数量）"""
        self.files_cache.clear()
        max_files = 10  # 限制最多10个文件

        all_files = []

        # 只扫描桌面和文档目录
        scan_dirs = [
            os.path.expanduser("~/Desktop"),
            os.path.expanduser("~/Documents")
        ]

        for directory in scan_dirs:
            if not os.path.exists(directory):
                continue

            try:
                for root, dirs, files in os.walk(directory):
                    # 只扫描第一层，不递归
                    if root != directory:
                        continue

                    for file in files:
                        file_path = os.path.join(root, file)
                        file_ext = Path(file).suffix.lower()

                        if file_ext in self.supported_extensions:
                            try:
                                stat = os.stat(file_path)
                                all_files.append({
                                    'path': file_path,
                                    'name': file,
                                    'size': stat.st_size,
                                    'modified': stat.st_mtime,
                                    'type': file_ext[1:]
                                })
                            except OSError:
                                continue

            except Exception as e:
                logger.error(f"扫描目录 {directory} 时出错: {e}")

        # 按修改时间排序，取最新的文件
        all_files.sort(key=lambda x: x['modified'], reverse=True)

        for file_info in all_files[:max_files]:
            self.files_cache[file_info['path']] = {
                'name': file_info['name'],
                'size': file_info['size'],
                'modified': file_info['modified'],
                'type': file_info['type']
            }

        logger.info(f"扫描到 {len(self.files_cache)} 个最近文件")

    async def generate_preview(self, file_path, page):
        """生成文件预览"""
        try:
            file_ext = Path(file_path).suffix.lower()

            if file_ext == '.pdf':
                return await self._generate_pdf_preview(file_path, page)
            elif file_ext in ['.pptx', '.ppt']:
                return await self._generate_ppt_preview(file_path, page)
            else:
                return self._get_placeholder_image()

        except Exception as e:
            logger.error(f"生成预览失败 {file_path}: {e}")
            return self._get_placeholder_image()

    def _get_placeholder_image(self):
        """获取占位符图片"""
        # 返回一个简单的占位符图片的base64编码
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

    async def _generate_pdf_preview(self, file_path, page):
        """生成PDF预览 - 使用PyMuPDF"""
        try:
            import pymupdf  # PyMuPDF
            import io
            import base64

            # 使用PyMuPDF打开PDF文档
            doc = pymupdf.open(file_path)

            if page <= len(doc):
                # 获取指定页面
                pdf_page = doc[page - 1]

                # 渲染页面为图片 (Pixmap)
                mat = pymupdf.Matrix(1.5, 1.5)  # 1.5倍缩放，提高清晰度
                pix = pdf_page.get_pixmap(matrix=mat)

                # 转换为PNG格式的字节数据
                img_data = pix.tobytes("png")

                # 转换为base64
                img_str = base64.b64encode(img_data).decode()

                # 清理资源
                pix = None
                doc.close()

                logger.info(f"成功生成PDF预览: {file_path} 第{page}页")
                return img_str
            else:
                doc.close()
                logger.warning(f"PDF页面不存在: {file_path} 第{page}页")
                return self._get_placeholder_image()

        except ImportError:
            logger.error("PyMuPDF库未安装，尝试备用方案")
            # 备用方案：使用PyPDF2生成文本预览
            try:
                import PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    if page <= len(pdf_reader.pages):
                        page_obj = pdf_reader.pages[page - 1]
                        text = page_obj.extract_text()
                        return await self._create_text_preview_image(text, f"PDF第{page}页")
                    else:
                        return self._get_placeholder_image()
            except Exception as e:
                logger.error(f"备用PDF预览方案失败: {e}")
                return self._get_placeholder_image()
        except Exception as e:
            logger.error(f"生成PDF预览失败: {e}")
            return self._get_placeholder_image()

    async def _create_text_preview_image(self, text, title):
        """创建文本预览图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import io
            import base64

            # 创建图片
            width, height = 800, 600
            image = Image.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(image)

            # 尝试使用系统字体
            try:
                font_title = ImageFont.truetype("arial.ttf", 24)
                font_text = ImageFont.truetype("arial.ttf", 16)
            except:
                font_title = ImageFont.load_default()
                font_text = ImageFont.load_default()

            # 绘制标题
            draw.text((20, 20), title, fill='black', font=font_title)

            # 绘制文本内容
            y_offset = 60
            lines = text.split('\n')[:20]  # 限制行数
            for line in lines:
                if y_offset > height - 40:
                    break
                # 限制每行长度
                if len(line) > 80:
                    line = line[:80] + "..."
                draw.text((20, y_offset), line, fill='black', font=font_text)
                y_offset += 25

            # 转换为base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return img_str

        except Exception as e:
            logger.error(f"创建文本预览图片失败: {e}")
            return self._get_placeholder_image()

    async def _create_enhanced_text_preview_image(self, text, title):
        """创建增强的文本预览图片 - 更好的排版和样式"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import io
            import base64
            import textwrap

            # 创建更大的图片以容纳更多内容
            width, height = 900, 700
            image = Image.new('RGB', (width, height), color='#f8f9fa')
            draw = ImageDraw.Draw(image)

            # 尝试使用支持中文的字体
            try:
                # Windows系统中文字体
                font_title = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 28)  # 微软雅黑
                font_subtitle = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 18)
                font_text = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 14)
                logger.info("使用微软雅黑字体")
            except:
                try:
                    # 备用字体
                    font_title = ImageFont.truetype("C:/Windows/Fonts/simsun.ttc", 28)  # 宋体
                    font_subtitle = ImageFont.truetype("C:/Windows/Fonts/simsun.ttc", 18)
                    font_text = ImageFont.truetype("C:/Windows/Fonts/simsun.ttc", 14)
                    logger.info("使用宋体字体")
                except:
                    font_title = ImageFont.load_default()
                    font_subtitle = ImageFont.load_default()
                    font_text = ImageFont.load_default()
                    logger.warning("使用默认字体，可能不支持中文")

            # 绘制背景边框
            draw.rectangle([(10, 10), (width-10, height-10)], outline='#dee2e6', width=2)

            # 绘制标题背景
            draw.rectangle([(15, 15), (width-15, 60)], fill='#007bff', outline='#0056b3')
            draw.text((25, 25), title, fill='white', font=font_title)

            # 处理文本内容
            y_offset = 80
            lines = text.split('\n')

            for line in lines:
                if y_offset > height - 50:
                    # 如果空间不够，显示省略号
                    draw.text((25, y_offset), "... (内容过长，已截断)", fill='#6c757d', font=font_text)
                    break

                if line.strip():
                    # 自动换行处理
                    wrapped_lines = textwrap.wrap(line, width=100)
                    for wrapped_line in wrapped_lines:
                        if y_offset > height - 50:
                            break
                        draw.text((25, y_offset), wrapped_line, fill='#212529', font=font_text)
                        y_offset += 20
                else:
                    # 空行
                    y_offset += 10

            # 添加底部信息
            draw.text((25, height-35), "FlyClock 文件预览", fill='#6c757d', font=font_subtitle)

            # 转换为base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG', quality=95)
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return img_str

        except Exception as e:
            logger.error(f"创建增强文本预览图片失败: {e}")
            return await self._create_text_preview_image(text, title)

    async def _generate_ppt_preview(self, file_path, page):
        """生成PPT预览 - 使用多种方案确保兼容性"""
        logger.info(f"开始生成PPT预览: {file_path} 第{page}页")

        # 首先尝试使用python-pptx（更稳定）
        try:
            return await self._generate_ppt_preview_with_pptx(file_path, page)
        except Exception as e:
            logger.warning(f"python-pptx方案失败: {e}")

        # 备用方案：使用Spire.Presentation
        try:
            return await self._generate_ppt_preview_with_spire(file_path, page)
        except Exception as e:
            logger.warning(f"Spire.Presentation方案失败: {e}")

        # 最后备用方案：生成文本预览
        return await self._create_text_preview_image(
            f"PPT文件预览不可用\n文件: {os.path.basename(file_path)}\n页面: {page}\n\n请确保文件未被占用",
            "PPT预览"
        )

    async def _generate_ppt_preview_with_pptx(self, file_path, page):
        """使用python-pptx生成PPT预览（文本内容）"""
        try:
            from pptx import Presentation
            import base64

            logger.info(f"使用python-pptx打开PPT文件: {file_path}")

            # 打开PPT文件
            prs = Presentation(file_path)
            total_slides = len(prs.slides)

            if page > total_slides:
                return await self._create_text_preview_image(
                    f"页码超出范围\n文件: {os.path.basename(file_path)}\n请求页面: {page}\n总页数: {total_slides}",
                    "PPT预览错误"
                )

            # 获取指定页面的幻灯片
            slide = prs.slides[page - 1]

            # 提取文本内容
            text_content = []
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    text_content.append(shape.text.strip())

            # 生成文本预览图片
            preview_text = "\n\n".join(text_content) if text_content else "此页面无文本内容"
            title = f"PPT第{page}页 (共{total_slides}页)"

            return await self._create_text_preview_image(preview_text, title)

        except ImportError:
            logger.error("python-pptx库未安装")
            raise
        except Exception as e:
            logger.error(f"python-pptx生成预览失败: {e}")
            raise

    async def _generate_ppt_preview_with_spire(self, file_path, page):
        """使用Spire.Presentation生成PPT预览（图片截图）"""
        try:
            # 使用Spire.Presentation生成页面截图
            from spire.presentation.common import Stream
            from spire.presentation import Presentation
            import io
            import base64
            import tempfile
            import os
            import shutil

            logger.info(f"使用Spire.Presentation打开PPT文件: {file_path}")

            # 检查文件是否存在且可访问
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 检查文件是否被占用
            try:
                with open(file_path, 'rb') as f:
                    f.read(1)  # 尝试读取一个字节
            except PermissionError:
                raise PermissionError(f"文件被占用或权限不足: {file_path}")

            # 如果文件路径包含中文，复制到临时目录
            temp_file_path = file_path
            temp_file_created = False

            try:
                # 检查路径是否包含非ASCII字符
                file_path.encode('ascii')
            except UnicodeEncodeError:
                # 路径包含中文，复制到临时目录
                temp_dir = tempfile.gettempdir()
                temp_filename = f"temp_ppt_{hash(file_path) % 10000}.pptx"
                temp_file_path = os.path.join(temp_dir, temp_filename)

                logger.info(f"文件路径包含中文，复制到临时目录: {temp_file_path}")
                shutil.copy2(file_path, temp_file_path)
                temp_file_created = True

            # 创建Presentation对象
            presentation = Presentation()
            presentation.LoadFromFile(temp_file_path)

            try:
                total_slides = len(presentation.Slides)
                logger.info(f"PPT文件打开成功，共{total_slides}页")

                if page > total_slides:
                    logger.warning(f"页码超出范围: {page} > {total_slides}")
                    presentation.Dispose()
                    return await self._create_text_preview_image(
                        f"页码超出范围\n文件: {os.path.basename(file_path)}\n请求页面: {page}\n总页数: {total_slides}",
                        "PPT预览错误"
                    )

                # 获取指定页面的幻灯片
                slide = presentation.Slides[page - 1]  # 页码从1开始，索引从0开始

                # 生成页面截图
                logger.info("开始生成PPT页面截图...")
                image = slide.SaveAsImage()

                # 将图片转换为base64
                import uuid
                temp_path = os.path.join(tempfile.gettempdir(), f"ppt_preview_{uuid.uuid4().hex}.png")

                try:
                    image.Save(temp_path)
                    image.Dispose()

                    # 等待文件写入完成
                    import time
                    time.sleep(0.1)

                    # 读取图片文件并转换为base64
                    with open(temp_path, 'rb') as img_file:
                        img_data = img_file.read()
                        preview_data = base64.b64encode(img_data).decode('utf-8')

                finally:
                    # 删除临时文件
                    try:
                        if os.path.exists(temp_path):
                            os.unlink(temp_path)
                    except:
                        pass  # 忽略删除失败

                # 释放资源
                presentation.Dispose()

                logger.info(f"成功生成PPT预览: {file_path} 第{page}页，图片大小: {len(preview_data)} 字符")
                return preview_data

            finally:
                # 清理临时文件
                if temp_file_created and os.path.exists(temp_file_path):
                    try:
                        os.unlink(temp_file_path)
                        logger.info(f"已删除临时文件: {temp_file_path}")
                    except:
                        pass

        except ImportError as e:
            logger.error(f"Spire.Presentation库未安装: {e}")
            raise
        except Exception as e:
            logger.error(f"Spire.Presentation生成预览失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise



    async def _get_file_page_count(self, file_path):
        """获取文件页数"""
        try:
            file_ext = Path(file_path).suffix.lower()

            if file_ext == '.pdf':
                return await self._get_pdf_page_count(file_path)
            elif file_ext in ['.pptx', '.ppt']:
                return await self._get_ppt_page_count(file_path)
            else:
                return 1

        except Exception as e:
            logger.error(f"获取文件页数失败 {file_path}: {e}")
            return 1

    async def _get_pdf_page_count(self, file_path):
        """获取PDF页数"""
        try:
            import pymupdf  # PyMuPDF

            # 使用PyMuPDF打开PDF文档
            doc = pymupdf.open(file_path)
            page_count = len(doc)
            doc.close()

            logger.info(f"PDF页数: {page_count} - {file_path}")
            return page_count

        except ImportError:
            logger.error("PyMuPDF库未安装，尝试备用方案")
            # 备用方案：使用PyPDF2
            try:
                import PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    return len(pdf_reader.pages)
            except Exception as e:
                logger.error(f"使用PyPDF2获取PDF页数失败: {e}")
                return 1
        except Exception as e:
            logger.error(f"获取PDF页数失败: {e}")
            return 1

    async def _get_ppt_page_count(self, file_path):
        """获取PPT页数"""
        try:
            # 首先尝试使用python-pptx
            try:
                from pptx import Presentation
                prs = Presentation(file_path)
                return len(prs.slides)
            except ImportError:
                logger.info("python-pptx库未安装，尝试使用COM组件")
            except Exception as e:
                logger.error(f"使用python-pptx获取PPT页数失败: {e}")

            # 如果python-pptx不可用，尝试COM组件
            import win32com.client

            ppt_app = win32com.client.Dispatch("PowerPoint.Application")
            ppt_app.Visible = False

            try:
                presentation = ppt_app.Presentations.Open(file_path, ReadOnly=True)
                page_count = presentation.Slides.Count
                presentation.Close()
                return page_count
            finally:
                ppt_app.Quit()

        except Exception as e:
            logger.error(f"获取PPT页数失败: {e}")
            return 1

    async def next_page(self):
        """下一页"""
        if self.current_file and self.current_page < self.total_pages:
            self.current_page += 1
            await self.broadcast_status()
            logger.info(f"翻到下一页: {self.current_page}")

            # 重新生成当前页的预览
            await self._broadcast_current_preview()

    async def prev_page(self):
        """上一页"""
        if self.current_file and self.current_page > 1:
            self.current_page -= 1
            await self.broadcast_status()
            logger.info(f"翻到上一页: {self.current_page}")

            # 重新生成当前页的预览
            await self._broadcast_current_preview()

    async def _broadcast_current_preview(self):
        """广播当前页面的预览给所有客户端"""
        if self.current_file and self.clients:
            try:
                logger.info(f"广播当前预览: {self.current_file} 第{self.current_page}页")
                preview_data = await self.generate_preview(self.current_file, self.current_page)

                # 广播给所有连接的客户端
                for client in self.clients.copy():
                    try:
                        await self.send_message(client, {
                            'type': 'preview',
                            'file_path': self.current_file,
                            'page': self.current_page,
                            'image_data': preview_data
                        })
                    except Exception as e:
                        logger.error(f"广播预览失败: {e}")
                        self.clients.discard(client)

            except Exception as e:
                logger.error(f"生成预览失败: {e}")

    async def play_presentation(self):
        """播放演示"""
        if self.timer_window:
            self.timer_window.start_timer()
            await self.broadcast_status()
            logger.info("开始播放演示")
    
    async def pause_presentation(self):
        """暂停演示"""
        if self.timer_window:
            self.timer_window.pause_timer()
            await self.broadcast_status()
            logger.info("暂停演示")
    
    async def reset_timer(self):
        """重置计时器"""
        if self.timer_window:
            self.timer_window.reset_timer()
            await self.broadcast_status()
            logger.info("重置计时器")

    async def enter_fullscreen(self):
        """进入全屏模式"""
        try:
            # 发送F5键进入全屏
            import win32api
            import win32con

            win32api.keybd_event(win32con.VK_F5, 0, 0, 0)
            win32api.keybd_event(win32con.VK_F5, 0, win32con.KEYEVENTF_KEYUP, 0)

            await self.broadcast_status()
            logger.info("进入全屏模式")

        except ImportError:
            try:
                import subprocess
                subprocess.run([
                    'powershell', '-Command',
                    'Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait("{F5}")'
                ], capture_output=True)
                logger.info("通过PowerShell进入全屏模式")
            except Exception as e:
                logger.error(f"进入全屏失败: {e}")
        except Exception as e:
            logger.error(f"进入全屏失败: {e}")

    async def exit_fullscreen(self):
        """退出全屏模式"""
        try:
            # 发送ESC键退出全屏
            import win32api
            import win32con

            win32api.keybd_event(win32con.VK_ESCAPE, 0, 0, 0)
            win32api.keybd_event(win32con.VK_ESCAPE, 0, win32con.KEYEVENTF_KEYUP, 0)

            await self.broadcast_status()
            logger.info("退出全屏模式")

        except ImportError:
            try:
                import subprocess
                subprocess.run([
                    'powershell', '-Command',
                    'Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait("{ESC}")'
                ], capture_output=True)
                logger.info("通过PowerShell退出全屏模式")
            except Exception as e:
                logger.error(f"退出全屏失败: {e}")
        except Exception as e:
            logger.error(f"退出全屏失败: {e}")

    async def toggle_fullscreen(self):
        """切换全屏模式"""
        # 简单实现：发送F5键
        await self.enter_fullscreen()
    
    async def send_welcome(self, websocket):
        """发送欢迎消息"""
        await self.send_message(websocket, {
            'type': 'welcome',
            'message': 'FlyClock 远程控制服务器',
            'version': '1.0.0',
            'requires_auth': bool(self.password)
        })
    
    async def send_message(self, websocket, data):
        """发送消息给客户端"""
        try:
            message = json.dumps(data, ensure_ascii=False)
            await websocket.send(message)
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
    
    async def send_error(self, websocket, error_message):
        """发送错误消息"""
        await self.send_message(websocket, {
            'type': 'error',
            'message': error_message
        })

    async def send_key_event(self, key):
        """发送键盘事件"""
        if not HAS_WIN32:
            logger.warning("无法发送键盘事件: 未安装pywin32")
            return

        try:
            # 键盘映射
            key_map = {
                'Right': win32con.VK_RIGHT,
                'Left': win32con.VK_LEFT,
                'Up': win32con.VK_UP,
                'Down': win32con.VK_DOWN,
                'F5': win32con.VK_F5,
                'Escape': win32con.VK_ESCAPE,
                'Space': win32con.VK_SPACE,
                'Enter': win32con.VK_RETURN
            }

            if key in key_map:
                vk_code = key_map[key]
                # 按下键
                win32api.keybd_event(vk_code, 0, 0, 0)
                # 释放键
                win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                logger.info(f"发送键盘事件: {key}")
            else:
                logger.warning(f"未知键盘事件: {key}")

        except Exception as e:
            logger.error(f"发送键盘事件失败: {e}")

    async def send_status_update(self, websocket):
        """发送状态更新"""
        try:
            status = {
                'type': 'status',
                'timer_running': False,
                'timer_paused': False,
                'current_time': 0,
                'total_time': 0,
                'current_file': '未选择',
                'current_page': 1,
                'total_pages': 1
            }

            if self.timer_window:
                # 获取计时器状态
                if hasattr(self.timer_window, 'is_running'):
                    status['timer_running'] = self.timer_window.is_running
                if hasattr(self.timer_window, 'is_paused'):
                    status['timer_paused'] = self.timer_window.is_paused
                if hasattr(self.timer_window, 'current_time'):
                    status['current_time'] = self.timer_window.current_time
                if hasattr(self.timer_window, 'total_time'):
                    status['total_time'] = self.timer_window.total_time

                # 获取当前演示信息
                presentation_info = self.timer_window.get_presentation_status()
                if presentation_info:
                    status['current_file'] = presentation_info['file_name']
                    status['is_fullscreen'] = presentation_info['is_fullscreen']

            await self.send_message(websocket, status)

        except Exception as e:
            logger.error(f"发送状态更新失败: {e}")
    
    async def broadcast_status(self):
        """广播当前状态给所有客户端"""
        if not self.clients:
            return
        
        status = {
            'type': 'status',
            'current_file': self.current_file,
            'current_page': self.current_page,
            'total_pages': self.total_pages,
            'timer_running': self.timer_window.is_running if self.timer_window else False,
            'timer_paused': self.timer_window.is_paused if self.timer_window else False,
            'current_time': self.timer_window.current_time if self.timer_window else 0
        }
        
        # 发送给所有连接的客户端
        disconnected_clients = set()
        for client in self.clients:
            try:
                await self.send_message(client, status)
            except:
                disconnected_clients.add(client)
        
        # 移除断开连接的客户端
        self.clients -= disconnected_clients

    def get_local_ip(self):
        """获取本机IP地址"""
        try:
            # 创建一个UDP socket连接到外部地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                return local_ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                return local_ip
            except Exception:
                return "127.0.0.1"


# 全局服务器实例
remote_server = None


def get_remote_server(timer_window=None, port=8081, password=""):
    """获取远程控制服务器实例"""
    global remote_server
    if remote_server is None:
        remote_server = RemoteControlServer(timer_window, port, password)
    return remote_server


async def start_remote_server(timer_window=None, port=8081, password=""):
    """启动远程控制服务器"""
    server = get_remote_server(timer_window, port, password)
    return await server.start_server()


async def stop_remote_server():
    """停止远程控制服务器"""
    global remote_server
    if remote_server:
        await remote_server.stop_server()
        remote_server = None
