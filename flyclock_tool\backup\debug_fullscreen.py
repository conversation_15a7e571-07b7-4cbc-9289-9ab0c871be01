#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全屏检测调试脚本
用于测试PPT/PDF全屏检测功能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_fullscreen_detection():
    """测试全屏检测功能"""
    print("=" * 50)
    print("ToolClock 全屏检测调试工具")
    print("=" * 50)
    
    try:
        # 导入修复版窗口检测器
        from window_detector_fixed import WindowDetectorFixed
        print("✓ 成功导入修复版窗口检测器")
        
        # 创建检测器实例
        detector = WindowDetectorFixed()
        print("✓ 创建检测器实例成功")
        
        # 启动检测
        if detector.start_detection():
            print("✓ 全屏检测已启动")
        else:
            print("✗ 全屏检测启动失败")
            return
        
        print("\n请打开PPT或PDF并进入全屏模式...")
        print("按 Ctrl+C 停止检测")
        
        try:
            # 持续检测
            while True:
                time.sleep(1)
                
                # 手动触发检测
                detector.force_detect_current_window()
                
                # 获取当前演示信息
                info = detector.get_current_presentation_info()
                if info:
                    print(f"✓ 检测到全屏演示: {info['file_name']}")
                else:
                    print("○ 未检测到全屏演示")
                    
        except KeyboardInterrupt:
            print("\n✓ 停止检测")
            detector.stop_detection()
            
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        print("尝试使用原始窗口检测器...")
        
        try:
            from core.window_detector import WindowDetector
            print("✓ 成功导入原始窗口检测器")
            
            # 创建检测器实例
            detector = WindowDetector()
            print("✓ 创建检测器实例成功")
            
            # 启动检测
            if detector.start_detection():
                print("✓ 全屏检测已启动")
            else:
                print("✗ 全屏检测启动失败")
                return
            
            print("\n请打开PPT或PDF并进入全屏模式...")
            print("按 Ctrl+C 停止检测")
            
            try:
                # 持续检测
                while True:
                    time.sleep(1)
                    
                    # 手动触发检测
                    detector.force_detect_current_window()
                    
                    # 获取当前演示信息
                    info = detector.get_current_presentation_info()
                    if info:
                        print(f"✓ 检测到全屏演示: {info['file_name']}")
                    else:
                        print("○ 未检测到全屏演示")
                        
            except KeyboardInterrupt:
                print("\n✓ 停止检测")
                detector.stop_detection()
                
        except ImportError as e2:
            print(f"✗ 原始检测器也导入失败: {e2}")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_window_info():
    """测试当前窗口信息"""
    print("=" * 50)
    print("当前窗口信息测试")
    print("=" * 50)
    
    try:
        import win32gui
        import win32process
        import win32api
        import win32con
        
        # 获取前台窗口
        foreground_window = win32gui.GetForegroundWindow()
        print(f"前台窗口句柄: {foreground_window}")
        
        if foreground_window:
            # 获取窗口信息
            window_class = win32gui.GetClassName(foreground_window)
            window_title = win32gui.GetWindowText(foreground_window)
            window_rect = win32gui.GetWindowRect(foreground_window)
            
            print(f"窗口类名: {window_class}")
            print(f"窗口标题: {window_title}")
            print(f"窗口位置: {window_rect}")
            
            # 获取进程信息
            _, process_id = win32process.GetWindowThreadProcessId(foreground_window)
            process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, process_id)
            process_name = win32process.GetModuleFileNameEx(process_handle, 0)
            process_name = os.path.basename(process_name).upper()
            win32api.CloseHandle(process_handle)
            
            print(f"进程名: {process_name}")
            
            # 获取屏幕分辨率
            screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
            screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
            print(f"屏幕分辨率: {screen_width}x{screen_height}")
            
            # 计算窗口大小
            left, top, right, bottom = window_rect
            window_width = right - left
            window_height = bottom - top
            print(f"窗口大小: {window_width}x{window_height}")
            
            # 判断是否全屏
            width_diff = abs(window_width - screen_width)
            height_diff = abs(window_height - screen_height)
            left_pos = abs(left)
            top_pos = abs(top)
            
            print(f"大小差异: 宽度={width_diff}, 高度={height_diff}")
            print(f"位置偏移: 左={left_pos}, 上={top_pos}")
            
            is_fullscreen = width_diff <= 20 and height_diff <= 20 and left_pos <= 20 and top_pos <= 20
            print(f"是否全屏: {is_fullscreen}")
            
    except ImportError:
        print("✗ 缺少win32gui模块")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 全屏检测测试")
    print("2. 当前窗口信息")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        test_fullscreen_detection()
    elif choice == "2":
        test_window_info()
    else:
        print("无效选择")
