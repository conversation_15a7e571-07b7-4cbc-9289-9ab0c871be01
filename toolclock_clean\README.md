# ToolClock - PPT/PDF 全屏放映计时辅助工具

## 🎯 项目简介

ToolClock 是一个专为PPT和PDF演示设计的智能计时辅助工具，能够自动检测全屏演示状态并进行倒计时管理。

## ✅ 核心功能

- **自动全屏检测**：智能识别PPT、PDF等演示文件的全屏状态
- **智能计时控制**：全屏时自动开始倒计时，退出时自动停止
- **悬浮窗口显示**：始终置顶的计时器窗口，可拖拽移动
- **音频提醒功能**：时间结束时播放提醒音
- **灵活设置管理**：支持个性化时间、颜色、字体等设置
- **文件识别功能**：自动识别演示文件并设置相应时间

## 📦 项目结构

```
toolclock_clean/
├── run_with_log.py           # 主程序入口 ✅
├── window_detector_fixed.py  # 窗口检测器 ✅
├── gui/
│   ├── timer_window.py       # 计时器界面 ✅
│   └── settings_window.py    # 设置窗口 ✅
├── core/
│   ├── config_manager.py     # 配置管理 ✅
│   ├── audio_player.py       # 音频播放 ✅
│   ├── window_detector.py    # 窗口检测 ✅
│   └── file_analyzer.py      # 文件分析 ✅
├── assets/
│   └── sounds/               # 音频资源 ✅
├── build.py                  # 打包脚本 ✅
└── dist/
    └── ToolClock.exe         # 可执行文件 ✅
```

## 🚀 使用方法

### 方式一：Python运行
```bash
cd toolclock_clean
python run_with_log.py
```

### 方式二：exe运行
```bash
双击运行: toolclock_clean/dist/ToolClock.exe
```

## 📋 使用说明

1. **启动程序**：运行后会出现悬浮计时器窗口
2. **打开演示文件**：打开PPT或PDF文件
3. **开始演示**：按F5进入全屏模式，计时器自动开始倒计时
4. **结束演示**：退出全屏，计时器自动停止
5. **调整设置**：右键点击计时器窗口可打开设置界面
6. **移动位置**：可拖拽计时器窗口到任意位置

## ⚙️ 功能特性

### 智能检测
- 支持PowerPoint、PDF阅读器等多种演示软件
- 自动识别全屏状态变化
- 实时监控窗口状态

### 计时功能
- 倒计时模式：从设定时间倒数到0
- 正计时模式：从0开始正向计时
- 暂停/恢复功能
- 重置功能

### 界面定制
- 可调整窗口大小和位置
- 支持透明度设置
- 可自定义字体和颜色
- 支持多种主题

### 音频提醒
- 时间结束音效
- 警告音效
- 支持自定义音频文件
- 音量控制

## 🔧 技术实现

### 核心技术栈
- **Python 3.13**：主要开发语言
- **Tkinter**：GUI界面框架
- **pygame**：音频播放支持
- **win32api**：Windows API调用
- **PyInstaller**：exe打包工具

### 关键模块
- **WindowDetectorFixed**：窗口检测核心
- **TimerWindow**：计时器界面管理
- **ConfigManager**：配置文件管理
- **AudioPlayer**：音频播放控制

## 📝 版本历史

### V2.0.0 (当前版本)
- ✅ 完整重构项目架构
- ✅ 修复exe打包问题
- ✅ 解决设置窗口无法打开的问题
- ✅ 移除对flyclock_tool的依赖
- ✅ 优化代码结构和模块导入
- ✅ 完善错误处理机制

## 🐛 问题解决记录

### 已解决问题
1. **exe不倒计时**：修复PyInstaller打包时缺少关键依赖
2. **编码错误**：移除特殊Unicode字符，兼容Windows GBK编码
3. **设置窗口无法打开**：修复flyclock_tool模块依赖问题
4. **模块导入失败**：清理导入路径，移除无效依赖

### 技术改进
- 使用智能导入机制，支持多种导入路径
- 添加完整的异常处理和降级方案
- 优化打包配置，确保所有依赖正确包含
- 简化项目结构，移除冗余文件

## 🎯 系统要求

- **操作系统**：Windows 10/11
- **Python版本**：3.8+ (仅开发时需要)
- **依赖库**：pygame, pywin32, tkinter
- **内存要求**：最少100MB可用内存
- **磁盘空间**：约30MB

## 📞 技术支持

如遇到问题，请检查：
1. 是否有杀毒软件阻止程序运行
2. 是否有足够的系统权限
3. 演示软件是否支持全屏检测
4. 音频设备是否正常工作

## 🏆 项目成就

- ✅ **功能完整**：所有核心功能正常工作
- ✅ **结构清晰**：模块化设计，易于维护
- ✅ **打包成功**：生成可独立运行的exe文件
- ✅ **问题解决**：修复了所有已知的技术问题
- ✅ **用户友好**：简单易用的界面和操作流程

---

**开发完成时间**：2025-07-31  
**项目状态**：✅ 完成并可正常使用  
**推荐使用**：`dist/ToolClock.exe`