# ToolClock V1.1.0 发布说明

## 📦 发布内容

本次发布包含两个版本的ToolClock exe文件，分别位于不同目录：

### 📁 目录结构
```
flyclock_tool/release/
├── null_release/                    # 标准版本目录
│   ├── ToolClock_V1.1.0_Standard.exe   (7.2 MB)
│   └── README.txt                   # 标准版本使用说明
├── red_release/                     # 红色蜂鸣版本目录
│   ├── ToolClock_V1.1.0_RedBeep.exe    (10.15 MB)
│   └── README.txt                   # 红色版本使用说明
└── 发布说明.md                      # 本文件
```

## 🎯 版本对比

| 特性 | 标准版本 | 红色蜂鸣版本 |
|------|----------|--------------|
| **文件大小** | 7.2 MB | 10.15 MB |
| **基础功能** | ✅ 全部支持 | ✅ 全部支持 |
| **智能负数时间** | ✅ 基于用户确认 | ✅ 基于用户确认 |
| **红色背景** | ✅ 二次全屏时显示 | 🔴 持续显示 |
| **蜂鸣音** | ✅ 时间结束时播放 | 🔊 持续播放（每2秒） |
| **手动控制** | ❌ 无 | ✅ 可手动激活/停止 |
| **适用场景** | 正式演示 | 强烈提醒场合 |

## 🚀 快速开始

### 标准版本（推荐）
1. 进入 `null_release` 目录
2. 双击 `ToolClock_V1.1.0_Standard.exe` 启动
3. 右键设置时间，开始使用

### 红色蜂鸣版本
1. 进入 `red_release` 目录  
2. 双击 `ToolClock_V1.1.0_RedBeep.exe` 启动
3. 可手动激活红色模式或等待自动激活

## ✨ 核心功能

### 🎯 智能计时控制
- **自动全屏检测**：支持PPT、PDF等演示文件
- **精确时间控制**：秒级精度的倒计时
- **智能启停**：全屏时自动开始，退出时自动停止

### 🔧 用户体验优化
- **文件名清理**：自动移除"润文档"等后缀
- **悬浮窗口**：始终置顶，可拖拽移动
- **右键菜单**：便捷的功能访问

### 🎨 智能负数时间显示
- **基于用户确认**：只有用户点击确定后才激活
- **红色背景提醒**：视觉上的强烈提醒
- **负数时间递增**：-00:00, -00:01, -00:02...
- **不自动退出**：二次全屏时保持演示状态

## 🔴 红色蜂鸣版本特色

### 🎯 强烈提醒功能
- **持续红色背景**：一旦激活，始终保持红色背景
- **持续蜂鸣音**：每2秒播放一次，强烈提醒
- **手动控制**：可随时激活或停止红色模式

### 🎪 适用场景
- **嘈杂环境**：会场、展厅等噪音大的场所
- **重要时刻**：关键演示、重要汇报
- **远距离提醒**：演讲者距离计时器较远
- **多人协作**：需要提醒多人注意时间

## 🔧 技术特点

### ✅ 单文件部署
- **无需安装**：双击即可运行
- **无依赖要求**：所有依赖已打包
- **绿色软件**：不写注册表，不留垃圾文件

### ✅ 系统兼容
- **Windows 10/11**：完全兼容
- **轻量级**：占用资源少
- **稳定可靠**：经过充分测试

## 📋 使用建议

### 🎯 选择建议
- **正式场合**：推荐使用标准版本
- **需要强烈提醒**：使用红色蜂鸣版本
- **安静环境**：使用标准版本
- **嘈杂环境**：使用红色蜂鸣版本

### 💡 使用技巧
1. **首次使用**：建议先用短时间测试功能
2. **窗口位置**：拖拽到屏幕角落避免遮挡
3. **音量控制**：蜂鸣音使用系统音量
4. **快速操作**：双击计时器快速开始/暂停

## 🆕 V1.1.0 更新内容

### ✅ 问题修复
- 修复文件名清理功能
- 修复持续蜂鸣音问题
- 修复二次全屏显示问题
- 修复计时器状态管理问题

### ✅ 功能增强
- 实现基于用户确认的智能判断逻辑
- 优化负数时间显示逻辑
- 完善线程安全机制
- 新增红色背景持续蜂鸣版本

### ✅ 用户体验
- 改进界面响应速度
- 优化错误处理机制
- 增加详细的调试信息
- 完善使用说明文档

## 📞 技术支持

### 🔧 开发信息
- **版本**：V1.1.0
- **发布日期**：2025-07-31
- **开发者**：AI Assistant + MCP工具集成
- **技术栈**：Python + Tkinter + PyInstaller

### 📝 文档资源
- `null_release/README.txt` - 标准版本详细说明
- `red_release/README.txt` - 红色版本详细说明
- `版本信息.txt` - 版本历史信息
- `V1.1.0_修复说明.md` - 详细修复说明

### 🐛 问题反馈
如遇到问题，请参考对应目录下的README.txt文件，或查看项目文档。

---

## 🎉 总结

ToolClock V1.1.0 提供了两个版本以满足不同场景的需求：

- **标准版本**：适合正式演示场合，智能的负数时间显示
- **红色蜂鸣版本**：适合需要强烈提醒的场合，持续的视觉和听觉提醒

两个版本都是单文件exe，无需安装，双击即用。选择适合您场景的版本，享受智能的演示时间管理体验！

**祝您演示成功！** 🚀
