#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试颜色修复效果
验证两个问题是否已解决：
1. 蓝色背景显示负数倒计时
2. 文件切换时颜色逻辑错误
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from gui.timer_window import TimerWindow
from core.config_manager import ConfigManager

def test_color_fix():
    """测试颜色修复"""
    print("=" * 60)
    print("测试颜色修复效果")
    print("=" * 60)

    # 创建计时器窗口（不需要传递config_manager参数）
    timer_window = TimerWindow()
    
    print("✓ 计时器窗口已创建")
    
    # 测试场景1：设置短时间（10秒）模拟李超文件
    print("\n测试场景1：设置10秒计时（模拟李超文件）")
    timer_window.set_duration("00:10")
    print(f"当前时间: {timer_window.current_time}秒")
    print(f"警告状态: {timer_window.is_warning}")
    print(f"确认状态: {timer_window.timer_finished_confirmed}")
    
    # 启动计时器并等待进入警告状态
    print("启动计时器...")
    timer_window.start_timer()
    
    # 等待5秒让计时器进入警告状态
    time.sleep(6)
    print(f"6秒后 - 当前时间: {timer_window.current_time}秒")
    print(f"警告状态: {timer_window.is_warning}")
    
    # 测试场景2：切换到长时间（8分钟）模拟黎快人文件
    print("\n测试场景2：切换到8分钟计时（模拟黎快人文件）")
    timer_window.set_duration("08:00")
    print(f"切换后 - 当前时间: {timer_window.current_time}秒")
    print(f"警告状态: {timer_window.is_warning}")
    print(f"确认状态: {timer_window.timer_finished_confirmed}")
    
    # 验证颜色是否正确重置
    if not timer_window.is_warning and not timer_window.timer_finished_confirmed:
        print("✅ 修复成功：状态已正确重置为正常模式")
    else:
        print("❌ 修复失败：状态未正确重置")
    
    # 测试场景3：模拟负数倒计时情况
    print("\n测试场景3：模拟负数倒计时情况")
    timer_window.set_duration("00:01")
    timer_window.start_timer()
    
    # 等待计时器结束并进入负数
    time.sleep(2)
    print(f"计时结束后 - 当前时间: {timer_window.current_time}秒")
    print(f"确认状态: {timer_window.timer_finished_confirmed}")
    
    # 现在切换到新文件
    print("切换到新文件（5分钟）...")
    timer_window.set_duration("05:00")
    print(f"切换后 - 当前时间: {timer_window.current_time}秒")
    print(f"警告状态: {timer_window.is_warning}")
    print(f"确认状态: {timer_window.timer_finished_confirmed}")
    
    if not timer_window.is_warning and not timer_window.timer_finished_confirmed:
        print("✅ 负数倒计时修复成功：状态已正确重置")
    else:
        print("❌ 负数倒计时修复失败：状态未正确重置")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    # 关闭窗口
    timer_window.window.after(3000, timer_window.window.quit)
    timer_window.window.mainloop()

if __name__ == "__main__":
    test_color_fix()
