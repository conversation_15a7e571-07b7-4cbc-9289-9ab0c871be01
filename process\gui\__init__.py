#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Process Monitor - GUI Module
图形用户界面模块

包含：
- 主窗口界面 (main_window)
- 进程列表组件 (process_list)
"""

# 模块版本
__version__ = "1.0.0"

# 导出的主要类
__all__ = [
    'MainWindow',
    'ProcessList',
    'create_main_window',
    'get_main_window'
]

# 导入已实现的类
try:
    from .main_window import MainWindow, create_main_window, get_main_window
except ImportError:
    pass

try:
    from .process_list import ProcessList, create_process_list
    __all__.extend(['create_process_list'])
except ImportError:
    pass