{"mcpServers": {"memory-service": {"type": "stdio", "command": "python", "args": ["scripts/run_memory_server.py"], "env": {"MCP_MEMORY_CHROMA_PATH": "/home/<USER>/.mcp_memory_chroma", "LOG_LEVEL": "INFO", "MCP_TIMEOUT": "30000"}}, "memory": {"command": "uv", "args": ["--directory", "/home/<USER>/repositories/mcp-memory-service", "run", "memory"], "env": {"MCP_MEMORY_STORAGE_BACKEND": "sqlite_vec", "MCP_MEMORY_SQLITE_PRAGMAS": "busy_timeout=15000,cache_size=20000", "LOG_LEVEL": "INFO"}}}}