#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Process Monitor - Core Module
核心功能模块

包含：
- 进程监控 (process_monitor)
- 窗口控制 (window_controller)
- 配置管理 (config_manager)
"""

# 模块版本
__version__ = "1.0.0"

# 导出的主要类
__all__ = [
    'ProcessMonitor',
    'WindowController', 
    'ProcessConfigManager'
]

# 导入已实现的类
try:
    from .process_monitor import ProcessMonitor, get_process_monitor
    __all__.extend(['get_process_monitor'])
except ImportError:
    pass

try:
    from .window_controller import WindowController, get_window_controller
    __all__.extend(['get_window_controller'])
except ImportError:
    pass

try:
    from .config_manager import ProcessConfigManager, get_config_manager
    __all__.extend(['get_config_manager'])
except ImportError:
    pass