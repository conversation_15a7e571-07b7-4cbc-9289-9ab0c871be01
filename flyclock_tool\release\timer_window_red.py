#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 红色背景持续蜂鸣版本
基于timer_window.py修改，二次全屏时始终显示红色背景并持续蜂鸣
"""

# 导入timer_window.py的所有内容
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 复制timer_window.py的内容并修改关键逻辑
import tkinter as tk
from tkinter import messagebox, Menu
import threading
import time
import json
import logging
from pathlib import Path

# 导入其他模块
try:
    from core.config_manager import config_manager
    from core.audio_player import get_audio_player
    from window_detector_fixed import get_window_detector
except ImportError:
    # 如果在release目录中运行，尝试从上级目录导入
    sys.path.append('..')
    try:
        from core.config_manager import config_manager
        from core.audio_player import get_audio_player
        from window_detector_fixed import get_window_detector
    except ImportError:
        print("❌ 无法导入必要的模块，请检查文件结构")
        sys.exit(1)

# 设置日志
logger = logging.getLogger(__name__)

class TimerWindowRed:
    """红色背景持续蜂鸣版本的计时器窗口类"""
    
    def __init__(self):
        """初始化计时器窗口"""
        self.config_manager = config_manager
        self.is_running = False
        self.is_paused = False
        self.current_time = 0  # 当前时间（秒）
        self.total_time = 0   # 总时间（秒）
        self.timer_mode = 'countdown'  # 计时模式：countdown 或 countup
        self.timer_thread = None
        self.is_warning = False
        self.warning_active = False  # 持续警告音状态
        self.timer_finished_confirmed = False  # 用户是否已确认计时结束对话框
        
        # 红色版本特殊标志
        self.red_version_active = False  # 红色版本激活标志
        self.continuous_beep_thread = None  # 持续蜂鸣线程

        # 窗口检测器
        self.window_detector = get_window_detector(self)

        # 音频播放器
        self.audio_player = get_audio_player()
        
        # 创建窗口
        self.window = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_config()
        
        # 绑定事件
        self.bind_events()
        
        # 创建右键菜单
        self.create_context_menu()

        # 启动窗口检测
        self.start_window_detection()
    
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("ToolClock V1.1.0 Red")
        self.window.geometry("200x80")
        self.window.attributes("-topmost", True)
        self.window.attributes("-alpha", 0.9)
        self.window.resizable(False, False)
        
        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置图标
            pass
        except:
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = tk.Frame(self.window, bg="#4A90E2", padx=10, pady=5)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 时间显示标签
        self.time_label = tk.Label(
            self.main_frame,
            text="00:00:00",
            font=("Arial", 16, "bold"),
            fg="#FFFFFF",
            bg="#4A90E2"
        )
        self.time_label.pack(pady=5)
        
        # 按钮框架
        button_frame = tk.Frame(self.main_frame, bg="#4A90E2")
        button_frame.pack(fill=tk.X, pady=2)
        
        # 开始/暂停按钮
        self.start_button = tk.Button(
            button_frame,
            text="开始",
            command=self.toggle_timer,
            width=6,
            font=("Arial", 8)
        )
        self.start_button.pack(side=tk.LEFT, padx=2)
        
        # 重置按钮
        self.reset_button = tk.Button(
            button_frame,
            text="重置",
            command=self.reset_timer,
            width=6,
            font=("Arial", 8)
        )
        self.reset_button.pack(side=tk.RIGHT, padx=2)
    
    def update_display(self):
        """更新时间显示 - 红色版本修改"""
        mode = self.config_manager.get_setting('timer.mode', 'countdown')

        if mode == 'countdown':
            display_time = self.current_time
        else:
            display_time = self.total_time - self.current_time

        # 红色版本：如果已确认或红色版本激活，始终显示负数格式和红色背景
        if self.timer_finished_confirmed or self.red_version_active:
            if self.current_time <= 0:
                if self.current_time < 0:
                    abs_time = abs(self.current_time)
                    time_str = "-" + self.format_time(abs_time)
                else:
                    time_str = "-" + self.format_time(0)
            else:
                time_str = self.format_time(display_time)
            
            # 激活红色版本
            if not self.red_version_active:
                self.red_version_active = True
                self.start_continuous_red_beep()
        else:
            time_str = self.format_time(display_time)

        self.time_label.configure(text=time_str)

        # 红色版本：始终显示红色背景
        if self.red_version_active:
            if not self.is_warning:
                self.is_warning = True
                self.update_colors(warning=True, time_up=True)
                print("🔴 红色版本：显示红色背景并开始持续蜂鸣")
        elif self.timer_finished_confirmed:
            if not self.is_warning:
                self.is_warning = True
                self.update_colors(warning=True, time_up=True)
                print("🔴 显示红色背景和负数时间")
        elif self.current_time <= self.config_manager.get_setting('timer.warning_time', 60):
            if not self.is_warning:
                self.is_warning = True
                self.update_colors(warning=True)
                if self.config_manager.get_setting('audio.warning_enabled', True):
                    self.audio_player.play_warning_sound()
        else:
            if self.is_warning:
                self.is_warning = False
                self.update_colors(warning=False)
    
    def start_continuous_red_beep(self):
        """启动红色版本的持续蜂鸣"""
        if self.continuous_beep_thread and self.continuous_beep_thread.is_alive():
            return
        
        self.continuous_beep_thread = threading.Thread(target=self._continuous_beep_loop, daemon=True)
        self.continuous_beep_thread.start()
        print("🔴 红色版本：启动持续蜂鸣线程")
    
    def _continuous_beep_loop(self):
        """持续蜂鸣循环 - 红色版本专用"""
        while self.red_version_active:
            try:
                # 播放系统蜂鸣音
                import winsound
                winsound.Beep(800, 500)  # 800Hz, 500ms
                time.sleep(2)  # 每2秒蜂鸣一次
            except Exception as e:
                print(f"蜂鸣音播放失败: {e}")
                time.sleep(2)
    
    def format_time(self, seconds):
        """格式化时间显示"""
        if seconds < 0:
            seconds = 0
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def update_colors(self, warning=False, time_up=False):
        """更新颜色"""
        if time_up or self.red_version_active:
            # 红色版本或时间结束状态颜色（红色背景）
            fg_color = '#FFFFFF'  # 白色文字
            bg_color = '#FF0000'  # 纯红色
        elif warning:
            # 警告状态颜色
            fg_color = self.config_manager.get_setting('display.colors.warning.foreground', '#FFFFFF')
            bg_color = self.config_manager.get_setting('display.colors.warning.background', '#FF6B6B')
        else:
            # 正常状态颜色
            fg_color = self.config_manager.get_setting('display.colors.normal.foreground', '#FFFFFF')
            bg_color = self.config_manager.get_setting('display.colors.normal.background', '#4A90E2')

        self.time_label.configure(fg=fg_color, bg=bg_color)
        self.main_frame.configure(bg=bg_color)
        self.window.configure(bg=bg_color)
    
    # 其他方法保持与原版相同，这里省略以节省空间
    # 包括：load_config, bind_events, create_context_menu等
    
    def run(self):
        """运行主循环"""
        print("🔴 ToolClock 红色背景持续蜂鸣版本已启动")
        self.window.mainloop()

    def load_config(self):
        """加载配置"""
        try:
            # 设置默认时间为10秒（用于测试）
            self.total_time = self.config_manager.get_setting('timer.default_time', 10)
            self.current_time = self.total_time
            self.update_display()
        except Exception as e:
            print(f"加载配置失败: {e}")

    def bind_events(self):
        """绑定事件"""
        self.window.bind("<Button-1>", self.on_click)
        self.window.bind("<B1-Motion>", self.on_drag)
        self.window.bind("<ButtonPress-1>", self.on_button_press)
        self.window.bind("<Double-Button-1>", self.on_double_click)

        # 绑定右键菜单
        self.window.bind("<Button-3>", self.show_context_menu)
        self.time_label.bind("<Button-3>", self.show_context_menu)
        self.main_frame.bind("<Button-3>", self.show_context_menu)

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="设置时间", command=self.set_time)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="激活红色版本", command=self.activate_red_version)
        self.context_menu.add_command(label="停止蜂鸣", command=self.stop_red_version)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="退出", command=self.quit_app)

    def activate_red_version(self):
        """手动激活红色版本"""
        self.red_version_active = True
        self.timer_finished_confirmed = True
        self.current_time = 0
        self.update_display()
        print("🔴 手动激活红色版本")

    def stop_red_version(self):
        """停止红色版本"""
        self.red_version_active = False
        self.timer_finished_confirmed = False
        self.is_warning = False
        self.update_colors(warning=False)
        print("✓ 已停止红色版本")

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def set_time(self):
        """设置时间"""
        from tkinter import simpledialog
        time_str = simpledialog.askstring("设置时间", "请输入时间（格式：MM:SS 或 HH:MM:SS）:")
        if time_str:
            try:
                parts = time_str.split(':')
                if len(parts) == 2:
                    minutes, seconds = map(int, parts)
                    total_seconds = minutes * 60 + seconds
                elif len(parts) == 3:
                    hours, minutes, seconds = map(int, parts)
                    total_seconds = hours * 3600 + minutes * 60 + seconds
                else:
                    raise ValueError("格式错误")

                self.total_time = total_seconds
                self.current_time = total_seconds
                self.update_display()
                print(f"✓ 时间已设置为: {time_str}")
            except ValueError:
                messagebox.showerror("错误", "时间格式错误，请使用 MM:SS 或 HH:MM:SS 格式")

    def toggle_timer(self):
        """切换计时器状态"""
        if self.is_running:
            self.pause_timer()
        else:
            self.start_timer()

    def start_timer(self):
        """启动计时器"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            self.start_button.configure(text="暂停")

            # 启动计时器线程
            self.timer_thread = threading.Thread(target=self.timer_loop, daemon=True)
            self.timer_thread.start()
            print("✓ 计时器线程已启动")

    def pause_timer(self):
        """暂停计时器"""
        self.is_paused = True
        self.start_button.configure(text="继续")
        print("⏸️ 计时器已暂停")

    def reset_timer(self):
        """重置计时器"""
        self.is_running = False
        self.is_paused = False
        self.current_time = self.total_time
        self.timer_finished_confirmed = False
        self.red_version_active = False
        self.is_warning = False
        self.start_button.configure(text="开始")
        self.update_colors(warning=False)
        self.update_display()
        print("🔄 计时器已重置")

    def timer_loop(self):
        """计时器循环"""
        timer_finished_triggered = False

        while self.is_running:
            if not self.is_paused:
                # 红色版本：在时间结束后立即激活红色模式
                if self.current_time <= 0 and not self.red_version_active:
                    self.red_version_active = True
                    self.timer_finished_confirmed = True
                    print("🔴 红色版本：时间结束，激活红色模式")

                self.current_time -= 1
                print(f"⏱️ 倒计时: {self.current_time}秒")

                # 安全地更新UI
                self._safe_ui_update(self.update_display)

            time.sleep(1)

        print("✓ 计时器线程已结束")

    def _safe_ui_update(self, func, *args, **kwargs):
        """线程安全的UI更新"""
        try:
            self.window.after(0, func, *args, **kwargs)
        except Exception as e:
            print(f"UI更新失败: {e}")

    def start_window_detection(self):
        """启动窗口检测"""
        if self.window_detector:
            self.window_detector.start()
            print("✓ 窗口检测已启动")

    def on_click(self, event):
        """鼠标点击事件"""
        self.start_x = event.x
        self.start_y = event.y

    def on_drag(self, event):
        """鼠标拖拽事件"""
        x = self.window.winfo_x() + (event.x - self.start_x)
        y = self.window.winfo_y() + (event.y - self.start_y)
        self.window.geometry(f"+{x}+{y}")

    def on_button_press(self, event):
        """鼠标按下事件"""
        self.start_x = event.x
        self.start_y = event.y

    def on_double_click(self, event):
        """双击事件"""
        self.toggle_timer()

    def quit_app(self):
        """退出应用"""
        self.red_version_active = False
        self.is_running = False
        if self.window_detector:
            self.window_detector.stop()
        self.window.quit()
        self.window.destroy()

def main():
    """主函数"""
    try:
        app = TimerWindowRed()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
