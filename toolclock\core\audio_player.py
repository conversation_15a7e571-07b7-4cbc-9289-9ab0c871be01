"""
音频播放模块
处理警告音和结束音的播放
"""

import sys
import os
import threading
import time
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import pygame
    HAS_PYGAME = True
except ImportError:
    HAS_PYGAME = False
    print("警告: 未安装pygame，音频播放功能将不可用")

try:
    import winsound
    HAS_WINSOUND = True
except ImportError:
    HAS_WINSOUND = False

from core.config_manager import config_manager

logger = logging.getLogger(__name__)


class AudioPlayer:
    """音频播放器类"""
    
    def __init__(self):
        """初始化音频播放器"""
        self.config_manager = config_manager
        self.is_initialized = False
        self.current_sound = None
        
        # 初始化pygame音频
        self._init_pygame()
    
    def _init_pygame(self):
        """初始化pygame音频系统"""
        if not HAS_PYGAME:
            logger.warning("pygame未安装，将使用系统默认音频播放")
            return
        
        try:
            pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
            pygame.mixer.init()
            self.is_initialized = True
            logger.info("pygame音频系统初始化成功")
        except Exception as e:
            logger.error(f"pygame音频系统初始化失败: {e}")
            self.is_initialized = False
    
    def play_warning_sound(self):
        """播放警告音"""
        if not self.config_manager.get_setting('audio.warning_enabled', True):
            return
        
        sound_file = self.config_manager.get_setting('audio.warning_sound', '')
        if sound_file and os.path.exists(sound_file):
            self._play_sound_file(sound_file)
        else:
            # 播放系统默认警告音
            self._play_system_beep(frequency=800, duration=500)
    
    def play_end_sound(self):
        """播放结束音"""
        if not self.config_manager.get_setting('audio.enabled', True):
            return
        
        sound_file = self.config_manager.get_setting('audio.end_sound', '')
        if sound_file and os.path.exists(sound_file):
            self._play_sound_file(sound_file)
        else:
            # 播放系统默认结束音
            self._play_system_beep(frequency=1000, duration=1000)
    
    def test_sound(self, sound_file):
        """测试播放指定音频文件"""
        if sound_file and os.path.exists(sound_file):
            return self._play_sound_file(sound_file)
        else:
            # 播放测试音
            return self._play_system_beep(frequency=600, duration=300)
    
    def _play_sound_file(self, sound_file):
        """播放音频文件"""
        try:
            # 优先使用pygame播放
            if self.is_initialized and HAS_PYGAME:
                return self._play_with_pygame(sound_file)
            
            # 备用方案：使用winsound播放
            elif HAS_WINSOUND:
                return self._play_with_winsound(sound_file)
            
            # 最后备用方案：使用系统命令播放
            else:
                return self._play_with_system(sound_file)
                
        except Exception as e:
            logger.error(f"播放音频文件失败 {sound_file}: {e}")
            return False
    
    def _play_with_pygame(self, sound_file):
        """使用pygame播放音频"""
        try:
            # 停止当前播放的音频
            if self.current_sound:
                self.current_sound.stop()
            
            # 加载并播放新音频
            self.current_sound = pygame.mixer.Sound(sound_file)
            self.current_sound.play()
            
            logger.info(f"pygame播放音频: {os.path.basename(sound_file)}")
            return True
            
        except Exception as e:
            logger.error(f"pygame播放失败: {e}")
            return False
    
    def _play_with_winsound(self, sound_file):
        """使用winsound播放音频"""
        try:
            # 在新线程中播放，避免阻塞
            def play_thread():
                winsound.PlaySound(sound_file, winsound.SND_FILENAME | winsound.SND_ASYNC)
            
            threading.Thread(target=play_thread, daemon=True).start()
            logger.info(f"winsound播放音频: {os.path.basename(sound_file)}")
            return True
            
        except Exception as e:
            logger.error(f"winsound播放失败: {e}")
            return False
    
    def _play_with_system(self, sound_file):
        """使用系统命令播放音频"""
        try:
            if sys.platform == "win32":
                # Windows系统
                os.system(f'start /min "" "{sound_file}"')
            elif sys.platform == "darwin":
                # macOS系统
                os.system(f'afplay "{sound_file}" &')
            else:
                # Linux系统
                os.system(f'aplay "{sound_file}" &')
            
            logger.info(f"系统命令播放音频: {os.path.basename(sound_file)}")
            return True
            
        except Exception as e:
            logger.error(f"系统命令播放失败: {e}")
            return False
    
    def _play_system_beep(self, frequency=1000, duration=500):
        """播放系统蜂鸣音"""
        try:
            if HAS_WINSOUND:
                # Windows系统蜂鸣
                def beep_thread():
                    winsound.Beep(frequency, duration)
                
                threading.Thread(target=beep_thread, daemon=True).start()
                logger.info(f"播放系统蜂鸣音: {frequency}Hz, {duration}ms")
                return True
            
            elif self.is_initialized and HAS_PYGAME:
                # 使用pygame生成蜂鸣音
                return self._generate_beep_with_pygame(frequency, duration)
            
            else:
                # 使用系统默认蜂鸣
                print('\a')  # ASCII蜂鸣字符
                return True
                
        except Exception as e:
            logger.error(f"播放系统蜂鸣音失败: {e}")
            return False
    
    def _generate_beep_with_pygame(self, frequency, duration):
        """使用pygame生成蜂鸣音"""
        try:
            import numpy as np
            
            # 生成正弦波
            sample_rate = 22050
            frames = int(duration * sample_rate / 1000)
            arr = np.zeros((frames, 2))
            
            for i in range(frames):
                time_val = float(i) / sample_rate
                wave = np.sin(frequency * 2 * np.pi * time_val)
                arr[i][0] = wave * 32767
                arr[i][1] = wave * 32767
            
            # 播放生成的音频
            sound = pygame.sndarray.make_sound(arr.astype(np.int16))
            sound.play()
            
            logger.info(f"pygame生成蜂鸣音: {frequency}Hz, {duration}ms")
            return True
            
        except Exception as e:
            logger.error(f"pygame生成蜂鸣音失败: {e}")
            return False
    
    def stop_all_sounds(self):
        """停止所有音频播放"""
        try:
            if self.is_initialized and HAS_PYGAME:
                pygame.mixer.stop()
            
            if self.current_sound:
                self.current_sound = None
            
            logger.info("已停止所有音频播放")
            
        except Exception as e:
            logger.error(f"停止音频播放失败: {e}")
    
    def set_volume(self, volume):
        """设置音量 (0.0 - 1.0)"""
        try:
            if self.is_initialized and HAS_PYGAME:
                pygame.mixer.music.set_volume(volume)
                logger.info(f"设置音量: {volume}")
                return True
        except Exception as e:
            logger.error(f"设置音量失败: {e}")
        return False
    
    def get_supported_formats(self):
        """获取支持的音频格式"""
        formats = ['.wav']  # 基本支持WAV格式
        
        if HAS_PYGAME:
            # pygame支持的格式
            formats.extend(['.ogg', '.mp3'])
        
        return formats
    
    def is_audio_available(self):
        """检查音频功能是否可用"""
        return HAS_PYGAME or HAS_WINSOUND
    
    def cleanup(self):
        """清理音频资源"""
        try:
            self.stop_all_sounds()
            if self.is_initialized and HAS_PYGAME:
                pygame.mixer.quit()
            logger.info("音频资源清理完成")
        except Exception as e:
            logger.error(f"音频资源清理失败: {e}")


# 全局音频播放器实例
audio_player = AudioPlayer()


def get_audio_player():
    """获取音频播放器实例"""
    return audio_player
