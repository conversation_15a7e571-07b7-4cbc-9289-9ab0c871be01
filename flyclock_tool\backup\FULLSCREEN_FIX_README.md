# ToolClock 全屏逻辑修复说明

## 🎯 问题描述

之前的问题：**没有全屏也进入倒计时了**

用户期望的逻辑：
1. 导入A用户文件，设置倒计时时间
2. WPS或OFFICE打开A用户文件，显示A用户的倒计时时间（但不启动）
3. 只有按F5或进入全屏放映时，才开始倒计时
4. 切换到B文件时，显示B文件的倒计时时间（但不启动）

## ✅ 修复内容

### 1. 提高全屏检测阈值
- **修改前**：窗口占屏幕80%就被认为是全屏
- **修改后**：窗口必须占屏幕95%以上才被认为是全屏
- **效果**：避免大窗口被误判为全屏

```python
# 修改前
size_match = width_ratio >= 0.8 and height_ratio >= 0.8

# 修改后  
size_match = width_ratio >= 0.95 and height_ratio >= 0.95
```

### 2. 添加文件变化检测
- **新增功能**：检测文件变化时，只更新时间设置，不启动计时器
- **触发条件**：非全屏状态下，检测到PPT/PDF文件变化
- **行为**：更新倒计时时间，但确保计时器不会自动启动

```python
def _check_file_change(self, hwnd):
    """检查文件变化（非全屏状态下）"""
    # 只设置时间，不启动计时器
    self.timer_window.set_duration(file_duration)
    # 确保计时器不会自动启动
    if self.timer_window.is_running:
        self.timer_window.reset_timer()
```

### 3. 修复全屏退出逻辑
- **修改前**：全屏退出时暂停计时器
- **修改后**：全屏退出时完全停止并重置计时器
- **效果**：确保退出全屏后计时器完全停止

```python
# 修改前
self.timer_window.pause_timer()

# 修改后
self.timer_window.reset_timer()  # 完全停止并重置
```

### 4. 修复方法名冲突
- **问题**：两个`_is_presentation_window`方法参数不同
- **解决**：重命名为`_is_presentation_file`避免冲突

## 🔧 现在的工作流程

### 📄 文件识别阶段
```
用户打开PPT/PDF文件
    ↓
ToolClock检测到文件变化
    ↓
查找该文件的特定倒计时时间
    ↓
更新计时器时间设置（但不启动）
    ↓
显示准备就绪状态
```

### 🖥️ 全屏启动阶段
```
用户按F5或进入全屏放映
    ↓
ToolClock检测到真正的全屏状态
    ↓
自动启动倒计时器
    ↓
开始倒计时
```

### 🚪 退出全屏阶段
```
用户退出全屏放映
    ↓
ToolClock检测到全屏退出
    ↓
自动停止并重置计时器
    ↓
返回准备就绪状态
```

### 🔄 文件切换阶段
```
用户切换到其他PPT/PDF文件
    ↓
ToolClock检测到文件变化
    ↓
更新为新文件的倒计时时间
    ↓
保持准备就绪状态（不启动）
```

## 📊 测试验证

运行测试脚本验证修复效果：

```bash
python test_fix_verification.py
```

测试结果：
- ✅ 文件变化时计时器未自动启动
- ✅ 全屏时计时器自动启动  
- ✅ 全屏退出时计时器停止

## 🎯 使用说明

### 正确的使用流程：

1. **设置阶段**
   - 在ToolClock中为每个PPT/PDF文件设置专属的倒计时时间
   - 例如：`演讲A.pptx` → `00:05:00`，`演讲B.pptx` → `00:08:00`

2. **准备阶段**
   - 用WPS或Office打开PPT/PDF文件
   - ToolClock会自动识别文件并显示对应的倒计时时间
   - **此时计时器不会启动**，只是准备就绪

3. **演示阶段**
   - 按F5键或点击"从头开始"进入全屏放映
   - ToolClock检测到全屏状态，**自动开始倒计时**
   - 倒计时结束时会有提醒

4. **结束阶段**
   - 按ESC键或退出全屏放映
   - ToolClock自动停止倒计时
   - 可以重新进入全屏继续使用

5. **切换文件**
   - 打开其他PPT/PDF文件时
   - ToolClock会自动切换到对应文件的倒计时时间
   - 同样需要全屏才会启动

## 🔍 技术细节

### 全屏检测条件
- 窗口大小必须占屏幕95%以上
- 窗口位置必须接近屏幕边缘（误差10像素内）
- 窗口必须是PPT/PDF相关应用

### 文件识别关键词
- PowerPoint、PPT、PPTX
- PDF、Acrobat、Reader  
- WPS、演示、幻灯片
- Presentation、Slide

### 配置文件位置
- 用户设置：`config/user_settings.json`
- 文件特定时间：`files.file_specific_timers`
- 自动启动设置：`timer.auto_start`

## 🎉 修复总结

现在ToolClock的行为完全符合您的需求：

- 📄 **打开文件** → 识别并准备时间（不启动）
- 🖥️ **全屏放映** → 开始倒计时  
- 🚪 **退出全屏** → 停止倒计时
- 🔄 **切换文件** → 更新时间（不启动）

**不再会出现"没有全屏也进入倒计时"的问题！**
