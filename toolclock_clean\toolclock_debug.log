2025-07-31 18:32:11,124 - INFO - ============================================================
2025-07-31 18:32:11,124 - INFO -  Tool<PERSON>lock 启动中...
2025-07-31 18:32:11,124 - INFO - ============================================================
2025-07-31 18:32:11,124 - INFO -  导入模块...
2025-07-31 18:32:11,162 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:32:12,409 - INFO - pygame音频系统初始化成功
2025-07-31 18:32:12,409 - INFO -  TimerWindow 导入成功
2025-07-31 18:32:12,409 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 18:32:12,409 - INFO -  创建计时器窗口...
2025-07-31 18:32:12,533 - INFO - 窗口检测已启动
2025-07-31 18:32:12,533 - INFO -  计时器窗口创建成功
2025-07-31 18:32:12,534 - INFO -  创建窗口检测器...
2025-07-31 18:32:12,534 - INFO -  窗口检测器创建成功
2025-07-31 18:32:12,534 - INFO -  ToolClock已启动！
2025-07-31 18:32:12,535 - INFO -  修复后的逻辑:
2025-07-31 18:32:12,535 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 18:32:12,535 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 18:32:12,535 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 18:32:12,535 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 18:32:12,535 - INFO - ============================================================
2025-07-31 18:32:12,535 - INFO -  实时监控开始（请进行测试）:
2025-07-31 18:32:12,535 - INFO - ============================================================
2025-07-31 18:32:12,535 - INFO -  启动GUI主循环...
2025-07-31 18:32:24,953 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:32:24,956 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:04,578 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:33:04,578 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:04,579 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:33:06,583 - INFO - 全屏演示已退出
2025-07-31 18:33:14,590 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:33:14,590 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:14,590 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:33:18,594 - INFO - 全屏演示已退出
2025-07-31 18:33:21,598 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [年中工作总结-李宇锜（2025年）.pptx] (WPP.EXE)
2025-07-31 18:33:21,599 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:21,599 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:33:22,601 - INFO - 全屏演示已退出
2025-07-31 18:33:38,613 - INFO - 窗口检测已停止
2025-07-31 18:33:38,613 - INFO - 已停止所有音频播放
2025-07-31 18:33:38,682 - INFO - 音频资源清理完成
2025-07-31 18:39:51,240 - INFO - ============================================================
2025-07-31 18:39:51,241 - INFO -  ToolClock 启动中...
2025-07-31 18:39:51,241 - INFO - ============================================================
2025-07-31 18:39:51,241 - INFO -  导入模块...
2025-07-31 18:39:51,245 - ERROR -  ToolClock启动失败: invalid syntax (timer_window.py, line 19)
2025-07-31 18:39:51,249 - ERROR - Traceback (most recent call last):
  File "D:\trae\toolclock_clean\run_with_log.py", line 37, in main
    from gui.timer_window import TimerWindow
  File "D:\trae\toolclock_clean\gui\timer_window.py", line 19
    except ImportError:
    ^^^^^^
SyntaxError: invalid syntax

2025-07-31 18:41:03,729 - INFO - ============================================================
2025-07-31 18:41:03,729 - INFO -  ToolClock 启动中...
2025-07-31 18:41:03,729 - INFO - ============================================================
2025-07-31 18:41:03,729 - INFO -  导入模块...
2025-07-31 18:41:03,777 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:41:05,113 - INFO - pygame音频系统初始化成功
2025-07-31 18:41:05,114 - INFO -  TimerWindow 导入成功
2025-07-31 18:41:05,114 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 18:41:05,114 - INFO -  创建计时器窗口...
2025-07-31 18:41:05,287 - INFO - 窗口检测已启动
2025-07-31 18:41:05,287 - INFO -  计时器窗口创建成功
2025-07-31 18:41:05,287 - INFO -  创建窗口检测器...
2025-07-31 18:41:05,287 - INFO -  窗口检测器创建成功
2025-07-31 18:41:05,287 - INFO -  ToolClock已启动！
2025-07-31 18:41:05,287 - INFO -  修复后的逻辑:
2025-07-31 18:41:05,287 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 18:41:05,287 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 18:41:05,287 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 18:41:05,287 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 18:41:05,287 - INFO - ============================================================
2025-07-31 18:41:05,287 - INFO -  实时监控开始（请进行测试）:
2025-07-31 18:41:05,287 - INFO - ============================================================
2025-07-31 18:41:05,287 - INFO -  启动GUI主循环...
2025-07-31 18:42:42,407 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:42:42,408 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:42:42,408 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:42:44,414 - INFO - 全屏演示已退出
2025-07-31 18:42:58,428 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:42:58,428 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:42:58,428 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:43:04,438 - INFO - 全屏演示已退出
2025-07-31 18:43:54,481 - INFO - 窗口检测已停止
2025-07-31 18:43:54,481 - INFO - 已停止所有音频播放
2025-07-31 18:43:54,571 - INFO - 音频资源清理完成
2025-07-31 18:50:17,299 - INFO - ============================================================
2025-07-31 18:50:17,299 - INFO -  ToolClock 启动中...
2025-07-31 18:50:17,299 - INFO - ============================================================
2025-07-31 18:50:17,299 - INFO -  导入模块...
2025-07-31 18:50:17,329 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:18,726 - INFO - pygame音频系统初始化成功
2025-07-31 18:50:18,726 - INFO -  TimerWindow 导入成功
2025-07-31 18:50:18,726 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 18:50:18,726 - INFO -  创建计时器窗口...
2025-07-31 18:50:19,243 - INFO - 窗口检测已启动
2025-07-31 18:50:19,243 - INFO -  计时器窗口创建成功
2025-07-31 18:50:19,243 - INFO -  创建窗口检测器...
2025-07-31 18:50:19,243 - INFO -  窗口检测器创建成功
2025-07-31 18:50:19,243 - INFO -  启动窗口检测...
2025-07-31 18:50:19,244 - INFO - 窗口检测已启动
2025-07-31 18:50:19,244 - INFO -  窗口检测启动成功
2025-07-31 18:50:19,244 - INFO -  ToolClock已启动！
2025-07-31 18:50:19,244 - INFO -  修复后的逻辑:
2025-07-31 18:50:19,244 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 18:50:19,244 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 18:50:19,244 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 18:50:19,245 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 18:50:19,245 - INFO - ============================================================
2025-07-31 18:50:19,245 - INFO -  实时监控开始（请进行测试）:
2025-07-31 18:50:19,245 - INFO - ============================================================
2025-07-31 18:50:19,245 - INFO -  启动GUI主循环...
2025-07-31 18:50:31,258 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:50:31,258 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:50:31,259 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:31,259 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:31,260 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:31,260 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:35,271 - INFO - 全屏演示已退出
2025-07-31 18:50:35,271 - INFO - 全屏演示已退出
2025-07-31 18:50:35,274 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:40,972 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 18:50:47,282 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:50:47,284 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:47,284 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:47,289 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:50:47,290 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:47,290 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:53,291 - INFO - 全屏演示已退出
2025-07-31 18:50:53,293 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:53,298 - INFO - 全屏演示已退出
2025-07-31 18:50:59,305 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:50:59,306 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:50:59,308 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:59,308 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:50:59,309 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:50:59,309 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:00,310 - INFO - 全屏演示已退出
2025-07-31 18:51:00,312 - INFO - 全屏演示已退出
2025-07-31 18:51:01,311 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:51:01,313 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:51:01,320 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:01,320 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:01,321 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:01,321 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:03,325 - INFO - 全屏演示已退出
2025-07-31 18:51:03,325 - INFO - 全屏演示已退出
2025-07-31 18:51:03,327 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:09,333 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:51:09,334 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 18:51:09,334 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:09,335 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:09,335 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:09,335 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:10,336 - INFO - 全屏演示已退出
2025-07-31 18:51:10,336 - INFO - 全屏演示已退出
2025-07-31 18:51:34,641 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:34,643 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:49,368 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:51:49,369 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:49,369 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:49,372 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:51:49,373 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:51:49,373 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:51:50,370 - INFO - 全屏演示已退出
2025-07-31 18:51:50,374 - INFO - 全屏演示已退出
2025-07-31 18:52:54,440 - INFO - 检测到全屏演示:  (WPP.EXE)
2025-07-31 18:52:54,443 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:52:54,443 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:52:55,430 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:52:55,431 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:52:55,431 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:52:58,434 - INFO - 全屏演示已退出
2025-07-31 18:52:58,449 - INFO - 全屏演示已退出
2025-07-31 18:53:17,450 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:53:17,451 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:53:17,451 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:53:17,464 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:53:17,465 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:53:17,465 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:53:19,453 - INFO - 全屏演示已退出
2025-07-31 18:53:19,468 - INFO - 全屏演示已退出
2025-07-31 18:53:22,456 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:53:22,457 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:53:22,457 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:53:22,471 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:53:22,472 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:53:22,472 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:53:23,459 - INFO - 全屏演示已退出
2025-07-31 18:53:23,473 - INFO - 全屏演示已退出
2025-07-31 18:53:30,465 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:53:30,466 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:53:30,466 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:53:30,481 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:53:30,484 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:53:30,484 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:53:31,468 - INFO - 全屏演示已退出
2025-07-31 18:53:31,486 - INFO - 全屏演示已退出
2025-07-31 18:55:05,566 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:55:05,567 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:55:05,567 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:55:05,599 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 18:55:05,600 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:55:05,600 - ERROR - 检查全屏窗口出错: 'gbk' codec can't encode character '\U0001f504' in position 0: illegal multibyte sequence
2025-07-31 18:55:07,570 - INFO - 全屏演示已退出
2025-07-31 18:55:07,602 - INFO - 全屏演示已退出
2025-07-31 18:55:35,647 - INFO - 窗口检测已停止
2025-07-31 18:55:35,648 - INFO - 已停止所有音频播放
2025-07-31 18:55:35,737 - INFO - 音频资源清理完成
2025-07-31 19:18:58,427 - INFO - ============================================================
2025-07-31 19:18:58,428 - INFO -  ToolClock 启动中...
2025-07-31 19:18:58,428 - INFO - ============================================================
2025-07-31 19:18:58,428 - INFO -  导入模块...
2025-07-31 19:18:58,483 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:18:59,739 - INFO - pygame音频系统初始化成功
2025-07-31 19:18:59,739 - INFO -  TimerWindow 导入成功
2025-07-31 19:18:59,739 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 19:18:59,740 - INFO -  创建计时器窗口...
2025-07-31 19:19:00,689 - INFO - 窗口检测已启动
2025-07-31 19:19:00,689 - INFO -  计时器窗口创建成功
2025-07-31 19:19:00,689 - INFO -  创建窗口检测器...
2025-07-31 19:19:00,689 - INFO -  窗口检测器创建成功
2025-07-31 19:19:00,689 - INFO -  启动窗口检测...
2025-07-31 19:19:00,690 - INFO - 窗口检测已启动
2025-07-31 19:19:00,690 - INFO -  窗口检测启动成功
2025-07-31 19:19:00,691 - INFO -  ToolClock已启动！
2025-07-31 19:19:00,691 - INFO -  修复后的逻辑:
2025-07-31 19:19:00,691 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 19:19:00,691 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 19:19:00,691 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 19:19:00,691 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 19:19:00,691 - INFO - ============================================================
2025-07-31 19:19:00,692 - INFO -  实时监控开始（请进行测试）:
2025-07-31 19:19:00,692 - INFO - ============================================================
2025-07-31 19:19:00,692 - INFO -  启动GUI主循环...
2025-07-31 19:19:56,742 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:56,743 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:56,747 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:56,747 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:58,752 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:58,753 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:58,754 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:19:58,755 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:00,758 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:20:00,758 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:20:00,762 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:00,762 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:00,764 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:00,764 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:00,765 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:20:00,765 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:20:04,781 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:09,796 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:10,777 - INFO - 全屏演示已退出
2025-07-31 19:20:10,781 - INFO - 全屏演示已退出
2025-07-31 19:20:12,780 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:20:12,782 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:12,784 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:20:12,785 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:12,789 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:12,790 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:20:12,790 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:12,795 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:20:14,805 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:17,809 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:17,822 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:18,801 - INFO - 全屏演示已退出
2025-07-31 19:20:18,804 - INFO - 全屏演示已退出
2025-07-31 19:20:21,810 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:21,810 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:21,811 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:21,811 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:23,814 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:20:23,815 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:20:23,815 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:23,816 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:23,818 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:23,820 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:23,820 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:20:23,825 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:20:27,835 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:32,850 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:33,834 - INFO - 全屏演示已退出
2025-07-31 19:20:33,835 - INFO - 全屏演示已退出
2025-07-31 19:20:39,845 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:39,848 - ERROR - 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:20:39,848 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:39,850 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:20:46,856 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:20:46,858 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:21:10,880 - INFO - 窗口检测已停止
2025-07-31 19:21:10,880 - INFO - 已停止所有音频播放
2025-07-31 19:21:10,972 - INFO - 音频资源清理完成
2025-07-31 19:53:00,578 - INFO - ============================================================
2025-07-31 19:53:00,578 - INFO -  ToolClock 启动中...
2025-07-31 19:53:00,578 - INFO - ============================================================
2025-07-31 19:53:00,578 - INFO -  导入模块...
2025-07-31 19:53:00,671 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:02,146 - INFO - pygame音频系统初始化成功
2025-07-31 19:53:02,146 - INFO -  TimerWindow 导入成功
2025-07-31 19:53:02,146 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 19:53:02,146 - INFO -  创建计时器窗口...
2025-07-31 19:53:02,519 - INFO - 窗口检测已启动
2025-07-31 19:53:02,520 - INFO -  计时器窗口创建成功
2025-07-31 19:53:02,520 - INFO -  创建窗口检测器...
2025-07-31 19:53:02,520 - INFO -  窗口检测器创建成功
2025-07-31 19:53:02,520 - INFO -  启动窗口检测...
2025-07-31 19:53:02,520 - INFO - 窗口检测已启动
2025-07-31 19:53:02,520 - INFO -  窗口检测启动成功
2025-07-31 19:53:02,520 - INFO -  ToolClock已启动！
2025-07-31 19:53:02,520 - INFO -  修复后的逻辑:
2025-07-31 19:53:02,521 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 19:53:02,521 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 19:53:02,521 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 19:53:02,521 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 19:53:02,521 - INFO - ============================================================
2025-07-31 19:53:02,521 - INFO -  实时监控开始（请进行测试）:
2025-07-31 19:53:02,521 - INFO - ============================================================
2025-07-31 19:53:02,521 - INFO -  启动GUI主循环...
2025-07-31 19:53:03,524 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:03,524 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:05,529 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:05,530 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:09,533 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:09,534 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:14,546 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:14,550 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:15,550 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:15,552 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:25,605 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:53:25,606 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:05,824 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:15,688 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:15,690 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:15,691 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:15,693 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:17,695 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:17,696 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:17,697 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:17,698 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:18,354 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:18,356 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:23,724 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:23,725 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:23,727 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:23,728 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:25,730 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:54:25,732 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:54:25,732 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:25,735 - ERROR - 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:54:25,735 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:54:25,735 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:25,755 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:54:25,759 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:54:25,759 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:54:30,769 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:54:30,801 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:54:31,761 - INFO - 全屏演示已退出
2025-07-31 19:54:31,765 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:54:31,766 - INFO - 全屏演示已退出
2025-07-31 19:54:34,774 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:34,774 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:39,782 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:39,783 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:51,796 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:54:51,797 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:14,393 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:17,822 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:17,823 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:17,825 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:17,825 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:19,828 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:55:19,829 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:55:19,830 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:19,832 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:19,833 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:19,833 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:19,835 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:55:19,849 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:55:23,868 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:55:28,877 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:55:29,860 - INFO - 全屏演示已退出
2025-07-31 19:55:29,867 - INFO - 全屏演示已退出
2025-07-31 19:55:31,866 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:55:31,870 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:55:31,871 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:31,873 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:31,875 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:31,876 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:31,877 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:55:31,877 - INFO - DEBUG: timer_finished_confirmed=True, current_time=0
2025-07-31 19:55:35,884 - INFO - 全屏演示已退出
2025-07-31 19:55:35,885 - INFO - 全屏演示已退出
2025-07-31 19:55:36,886 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:36,887 - ERROR - 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:55:36,887 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:37,888 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:55:37,888 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:37,889 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:55:37,889 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:37,890 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:37,891 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:37,894 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:55:37,898 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:55:39,899 - INFO - 全屏演示已退出
2025-07-31 19:55:39,901 - INFO - 全屏演示已退出
2025-07-31 19:55:42,904 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:42,908 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:42,908 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:42,909 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:46,915 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:55:46,915 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:55:46,917 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:46,918 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:46,920 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:46,920 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:55:46,921 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:55:46,924 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:55:50,934 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:55:55,945 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:55:56,940 - INFO - 全屏演示已退出
2025-07-31 19:55:56,940 - INFO - 全屏演示已退出
2025-07-31 19:56:50,027 - INFO - 窗口检测已停止
2025-07-31 19:56:50,027 - INFO - 已停止所有音频播放
2025-07-31 19:56:50,110 - INFO - 音频资源清理完成
2025-07-31 20:04:20,509 - INFO - ============================================================
2025-07-31 20:04:20,509 - INFO -  ToolClock 启动中...
2025-07-31 20:04:20,509 - INFO - ============================================================
2025-07-31 20:04:20,509 - INFO -  导入模块...
2025-07-31 20:04:20,550 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:21,679 - INFO - pygame音频系统初始化成功
2025-07-31 20:04:21,679 - INFO -  TimerWindow 导入成功
2025-07-31 20:04:21,679 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 20:04:21,679 - INFO -  创建计时器窗口...
2025-07-31 20:04:21,826 - INFO - 窗口检测已启动
2025-07-31 20:04:21,826 - INFO -  计时器窗口创建成功
2025-07-31 20:04:21,826 - INFO -  创建窗口检测器...
2025-07-31 20:04:21,826 - INFO -  窗口检测器创建成功
2025-07-31 20:04:21,826 - INFO -  启动窗口检测...
2025-07-31 20:04:21,827 - INFO - 窗口检测已启动
2025-07-31 20:04:21,827 - INFO -  窗口检测启动成功
2025-07-31 20:04:21,827 - INFO -  ToolClock已启动！
2025-07-31 20:04:21,827 - INFO -  修复后的逻辑:
2025-07-31 20:04:21,827 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 20:04:21,827 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 20:04:21,827 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 20:04:21,827 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 20:04:21,827 - INFO - ============================================================
2025-07-31 20:04:21,827 - INFO -  实时监控开始（请进行测试）:
2025-07-31 20:04:21,828 - INFO - ============================================================
2025-07-31 20:04:21,828 - INFO -  启动GUI主循环...
2025-07-31 20:04:28,331 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:28,333 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:31,841 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:31,843 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:31,843 - ERROR - 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-07-31 20:04:31,846 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:04:38,850 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:38,852 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:38,855 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:38,855 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:44,867 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:46,916 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:48,907 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:48,908 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:48,909 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:48,910 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:53,953 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:04:53,953 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:04:53,955 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:53,956 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:53,957 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:53,958 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:04:53,960 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:04:53,961 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:04:57,977 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:02,990 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:03,972 - INFO - 全屏演示已退出
2025-07-31 20:05:03,977 - INFO - 全屏演示已退出
2025-07-31 20:05:05,980 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:05,980 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:05,981 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:05,981 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:06,985 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:06,986 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:06,989 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:06,990 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:06,992 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:06,993 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:06,994 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:06,997 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:08,998 - INFO - 全屏演示已退出
2025-07-31 20:05:09,002 - INFO - 全屏演示已退出
2025-07-31 20:05:10,000 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:10,001 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:10,002 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:10,002 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:10,004 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:10,004 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:10,004 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:10,006 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:12,012 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:15,020 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:15,021 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:16,012 - INFO - 全屏演示已退出
2025-07-31 20:05:16,013 - INFO - 全屏演示已退出
2025-07-31 20:05:19,017 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:19,018 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:19,018 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:19,020 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:20,023 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:20,023 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:20,026 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:20,028 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:20,029 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:20,030 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:20,033 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:20,035 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:21,035 - INFO - 全屏演示已退出
2025-07-31 20:05:21,037 - INFO - 全屏演示已退出
2025-07-31 20:05:22,347 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:22,348 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:24,739 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:27,046 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:27,046 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:27,047 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:27,048 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:27,049 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:27,049 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:27,051 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:27,052 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:31,064 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:36,081 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:05:37,062 - INFO - 全屏演示已退出
2025-07-31 20:05:37,063 - INFO - 全屏演示已退出
2025-07-31 20:05:39,065 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:39,066 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:39,067 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:39,070 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:39,070 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:39,071 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 20:05:39,071 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:39,073 - INFO - DEBUG: timer_finished_confirmed=True, current_time=-1
2025-07-31 20:05:43,078 - INFO - 全屏演示已退出
2025-07-31 20:05:43,078 - INFO - 全屏演示已退出
2025-07-31 20:05:46,082 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:46,082 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:46,084 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:46,084 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:46,085 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:46,085 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:46,087 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 20:05:46,087 - INFO - DEBUG: timer_finished_confirmed=True, current_time=0
2025-07-31 20:05:48,090 - INFO - 全屏演示已退出
2025-07-31 20:05:48,090 - INFO - 全屏演示已退出
2025-07-31 20:05:49,092 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:49,093 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:49,093 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:49,093 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:51,099 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:51,100 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:05:51,101 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:51,102 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:51,104 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:51,105 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:05:51,106 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:51,113 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 20:05:55,124 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:06:00,138 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 20:06:01,117 - INFO - 全屏演示已退出
2025-07-31 20:06:01,125 - INFO - 全屏演示已退出
2025-07-31 20:06:03,119 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:06:03,120 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:03,122 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:03,125 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 20:06:03,127 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 20:06:03,128 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:03,129 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:03,130 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 20:06:05,132 - INFO - 全屏演示已退出
2025-07-31 20:06:05,133 - INFO - 全屏演示已退出
2025-07-31 20:06:06,136 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:06,136 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:06,137 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:06,137 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 20:06:28,156 - INFO - 窗口检测已停止
2025-07-31 20:06:28,156 - INFO - 已停止所有音频播放
2025-07-31 20:06:28,243 - INFO - 音频资源清理完成
