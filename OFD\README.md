# OFD转PDF转换器

这是一个功能完整的OFD格式转PDF格式的图形界面工具，支持批量转换、预览和打印功能。

## 功能特点

1. **OFD转PDF转换**：将OFD格式文件转换为PDF格式
2. **批量处理**：支持添加单个或多个OFD文件，也可添加整个文件夹进行批量转换
3. **图形界面**：直观易用的图形用户界面
4. **进度显示**：实时显示转换进度和状态
5. **自定义输出目录**：可选择转换后PDF文件的保存位置
6. **错误处理**：对转换失败的文件进行提示
7. **PDF打印**：支持批量打印转换后的PDF文件
8. **文件预览**：支持预览OFD和PDF文件内容

## 安装依赖

在使用之前，请确保安装了所需的依赖库：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install PyQt5 easyofd pywin32
```

## 使用方法

1. 运行程序：
   ```bash
   python ofd_converter.py
   ```

2. 点击"添加OFD文件"按钮选择需要转换的OFD文件，或点击"添加文件夹"选择包含OFD文件的文件夹

3. 点击"选择输出目录"按钮选择转换后PDF文件的保存位置

4. 点击"开始转换"按钮执行转换

5. 转换完成后，可以选择PDF文件并点击"打印选中PDF"进行打印

## 技术说明

- 使用`easyofd`库进行OFD文件解析和PDF转换
- 使用`PyQt5`创建图形用户界面
- 使用`pywin32`实现Windows平台的打印功能
- 支持多线程处理，避免界面卡顿

## 注意事项

1. 确保系统已安装Python 3.7或更高版本
2. 某些OFD文件可能因为格式特殊而转换失败
3. 打印功能仅在Windows系统上可用
4. 转换过程中请勿关闭程序