#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 简化版 - 纯本地计时功能
移除了所有远程控制功能，只保留核心计时功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def main():
    """启动ToolClock本地计时器"""
    print("=" * 50)
    print("ToolClock - PPT/PDF 全屏放映计时辅助工具")
    print("版本: V1.0.0 (简化版)")
    print("=" * 50)
    print("✓ 已移除远程控制功能")
    print("✓ 只保留本地计时功能")
    print("=" * 50)
    
    try:
        # 导入并启动计时器窗口
        from flyclock_tool.gui.timer_window import TimerWindow
        
        print("🚀 启动ToolClock计时器...")

        # 创建并运行计时器窗口
        timer_window = TimerWindow()
        timer_window.run()

    except KeyboardInterrupt:
        print("\n✓ ToolClock已停止")
    except Exception as e:
        print(f"✗ ToolClock启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
