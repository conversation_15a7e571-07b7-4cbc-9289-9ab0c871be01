#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock远程控制诊断和修复工具
"""

import sys
import os
import socket
import subprocess
import threading
import asyncio
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_port_listening(port):
    """检查端口是否在监听"""
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        return f":{port}" in result.stdout and "LISTENING" in result.stdout
    except:
        return False

def check_firewall_rule(port):
    """检查防火墙规则"""
    try:
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
            f'name=FlyClock-{port}', 'dir=in'
        ], capture_output=True, text=True)
        return "FlyClock" in result.stdout
    except:
        return False

def add_firewall_rule(port):
    """添加防火墙规则"""
    try:
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'add', 'rule',
            f'name=FlyClock-{port}', 'dir=in', 'action=allow',
            'protocol=TCP', f'localport={port}'
        ], capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def get_local_ip():
    """获取本机IP地址"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "127.0.0.1"

def test_http_server(port=8080):
    """测试HTTP服务器"""
    web_dir = Path(__file__).parent / "assets" / "web"
    
    print(f"🔍 诊断信息:")
    print(f"  Web目录: {web_dir}")
    print(f"  Web目录存在: {web_dir.exists()}")
    
    if web_dir.exists():
        files = list(web_dir.glob("*"))
        print(f"  Web文件: {[f.name for f in files]}")
    
    print(f"  本机IP: {get_local_ip()}")
    print(f"  端口{port}监听状态: {check_port_listening(port)}")
    print(f"  防火墙规则存在: {check_firewall_rule(port)}")
    
    class DiagnosticHTTPRequestHandler(SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory=str(web_dir), **kwargs)
        
        def log_message(self, format, *args):
            print(f"📡 HTTP请求: {format % args}")
        
        def do_GET(self):
            print(f"📡 收到GET请求: {self.path}")
            print(f"📡 客户端地址: {self.client_address}")
            super().do_GET()
    
    try:
        print(f"\n🚀 启动诊断HTTP服务器 (端口: {port})...")
        httpd = HTTPServer(('0.0.0.0', port), DiagnosticHTTPRequestHandler)
        
        def server_thread():
            print(f"✅ HTTP服务器启动成功")
            print(f"📱 本地访问: http://localhost:{port}/remote_client.html")
            print(f"📱 局域网访问: http://{get_local_ip()}:{port}/remote_client.html")
            print(f"⏰ 服务器将运行60秒进行测试...")
            httpd.serve_forever()
        
        thread = threading.Thread(target=server_thread, daemon=True)
        thread.start()
        
        # 运行60秒后停止
        time.sleep(60)
        httpd.shutdown()
        print("🛑 测试服务器已停止")
        
    except Exception as e:
        print(f"❌ HTTP服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 FlyClock远程控制诊断工具")
    print("=" * 50)
    
    # 检查基本环境
    print("\n📋 环境检查:")
    web_dir = Path(__file__).parent / "assets" / "web"
    print(f"  项目目录: {Path(__file__).parent}")
    print(f"  Web目录: {web_dir}")
    print(f"  Web目录存在: {web_dir.exists()}")
    
    if not web_dir.exists():
        print("❌ Web目录不存在，无法启动HTTP服务器")
        return
    
    # 检查网络配置
    print(f"\n🌐 网络配置:")
    local_ip = get_local_ip()
    print(f"  本机IP地址: {local_ip}")
    
    # 检查端口状态
    ports_to_check = [8080, 8081, 8082]
    print(f"\n🔌 端口状态检查:")
    for port in ports_to_check:
        listening = check_port_listening(port)
        firewall = check_firewall_rule(port)
        print(f"  端口{port}: 监听={listening}, 防火墙规则={firewall}")
    
    # 询问用户是否要添加防火墙规则
    print(f"\n🛡️ 防火墙配置:")
    print("  如果远程访问失败，可能需要添加防火墙规则")
    print("  请以管理员身份运行以下命令:")
    for port in [8080, 8081]:
        print(f"  netsh advfirewall firewall add rule name=\"FlyClock-{port}\" dir=in action=allow protocol=TCP localport={port}")
    
    # 启动测试服务器
    print(f"\n🧪 启动测试服务器:")
    test_http_server(8080)

if __name__ == "__main__":
    main()
