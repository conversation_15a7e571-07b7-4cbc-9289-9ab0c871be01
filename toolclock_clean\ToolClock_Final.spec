# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run_with_log.py'],
    pathex=[],
    binaries=[],
    datas=[('gui', 'gui'), ('core', 'core'), ('assets', 'assets'), ('window_detector_fixed.py', '.')],
    hiddenimports=['threading', '_thread', 'queue', 'time', 'datetime', 'win32gui', 'win32api', 'win32con', 'win32process', 'pywintypes', 'pythoncom', 'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.simpledialog', 'pygame', 'pygame.mixer', 'winsound', 'logging', 'json', 'pathlib', 'subprocess', 're'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ToolClock_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
