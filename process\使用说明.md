# 🚀 现代化ALT+TAB窗口切换工具 使用说明

## 🎯 功能简介

这是一个现代化的ALT+TAB窗口切换工具，具有以下特色：
- 🎨 **现代化界面** - 使用ttkbootstrap主题，美观大方
- 📋 **智能进程管理** - 自动去重，按程序分组显示
- ⚡ **高效切换** - 精确的ALT+TAB窗口切换
- 📊 **实时状态** - 显示切换次数、选中进程等信息
- 🛡️ **稳定可靠** - 异常处理完善，运行稳定

## 🚀 启动程序

```bash
# 确保在虚拟环境中
cd d:\trae
.venv\Scripts\activate

# 启动现代化版本
python process\main.py
```

## ✨ 新版本改进

### 🎨 界面升级
- **现代主题** - 使用superhero主题，深色风格更护眼
- **分组显示** - 相同程序的多个实例自动分组
- **树形结构** - 清晰的层级关系，易于理解
- **状态图标** - 📄单实例、📁多实例、🔄运行状态

### 🔧 功能改进
- **进程去重** - 解决了进程重复显示的问题
- **智能分组** - 同名进程自动归类，显示实例数量
- **实时统计** - 显示选中进程数、切换次数等
- **错误处理** - 完善的异常处理，提升稳定性

## 📋 使用步骤

### 1. 选择进程 🎯
- **左侧进程面板** - 树形结构显示所有exe进程
- **分组显示** - 📁多实例进程可展开查看详情
- **多选支持** - 按住Ctrl点击选择多个进程/组
- **刷新功能** - 🔄点击刷新按钮更新进程列表

### 2. 设置切换间隔 ⏰
- **手动输入** - 在输入框中输入间隔时间（秒）
- **快速设置** - 点击1s、3s、5s、10s快速按钮
- **实时反馈** - 设置后状态栏显示确认信息

### 3. 开始切换 🎮
- **开始按钮** - ▶️点击"开始切换"启动自动切换
- **停止按钮** - ⏹️点击"停止切换"停止自动切换
- **状态显示** - 🔄实时显示切换状态和次数

### 4. 测试功能 🧪
- **测试按钮** - 🧪点击"测试ALT+TAB"验证功能
- **状态反馈** - 🟢成功或🔴失败状态提示

## ⚙️ 界面说明

### 📋 左侧进程面板
- **标题区域** - 🚀显示工具名称和说明
- **进程树** - 分层显示所有exe进程
  - 📄 单实例进程直接显示
  - 📁 多实例进程分组显示，可展开查看
  - 显示进程名、PID、实例数
- **刷新按钮** - 🔄更新进程列表

### ⚙️ 右侧控制面板
- **⏰ 切换间隔设置**
  - 手动输入框
  - 快速设置按钮（1s/3s/5s/10s）
- **🎮 切换控制**
  - ▶️开始切换按钮
  - 🧪测试ALT+TAB按钮
- **📊 状态信息**
  - 🟢运行状态显示
  - 切换次数统计
  - 选中进程数量

## 🔧 注意事项

### ⚠️ 使用须知
1. **选择进程** - 必须先选择至少一个进程才能开始切换
2. **时间间隔** - 建议设置1-10秒，太快可能影响正常使用
3. **停止切换** - 可以随时点击⏹️"停止切换"按钮停止
4. **窗口焦点** - ALT+TAB会在所有窗口间切换，不限于选中的进程

### 🎯 最佳实践
- **进程选择** - 选择分组进程会包含所有实例
- **间隔设置** - 根据使用场景选择合适间隔
- **状态监控** - 关注状态栏的实时反馈

## 🎮 快捷操作

- **⚡ 快速设置间隔** - 点击1s、3s、5s、10s彩色按钮
- **🧪 测试功能** - 点击"测试ALT+TAB"验证功能
- **🔄 刷新列表** - 点击"刷新进程列表"更新
- **📁 展开分组** - 点击分组进程查看详细实例

## 🛠️ 故障排除

### ❌ 程序无法启动
```bash
# 安装所需依赖
pip install psutil pyautogui ttkbootstrap

# 检查Python版本（需要3.7+）
python --version

# 在虚拟环境中运行
.venv\Scripts\python.exe process\main.py
```

### ❌ ALT+TAB不工作
- 🔍 检查是否有其他程序占用ALT+TAB
- 🧪 尝试点击"测试ALT+TAB"按钮验证
- 🛡️ 确认程序有足够权限（以管理员身份运行）
- 🖥️ 检查是否在虚拟机或远程桌面环境

### ❌ 进程列表问题
- 🔄 点击"刷新进程列表"按钮
- 📋 检查是否有exe程序在运行
- 🔐 确认程序有权限访问进程信息
- 📁 查看是否有进程被分组显示

### ❌ 界面显示问题
- 🎨 确认ttkbootstrap已正确安装
- 🖼️ 检查系统是否支持现代主题
- 📱 调整窗口大小适应屏幕分辨率

## 📝 使用示例

1. **游戏切换**：选择游戏进程，设置5秒间隔，自动在游戏窗口间切换
2. **工作切换**：选择办公软件，设置10秒间隔，定期切换工作窗口
3. **演示切换**：选择演示软件，设置3秒间隔，自动展示不同窗口

## 🔒 安全说明

- 程序只发送ALT+TAB按键，不会修改或访问其他程序数据
- 进程列表仅用于显示，不会对进程进行任何操作
- 可以随时停止切换功能

## 🆕 版本更新

### V2.0.0 (2025-07-30) - 现代化重构
- ✨ **全新界面** - 采用ttkbootstrap现代主题
- 🔧 **进程去重** - 解决重复进程显示问题
- 📊 **智能分组** - 同名进程自动分组管理
- 🎯 **状态优化** - 实时显示详细状态信息
- 🛡️ **稳定性提升** - 完善异常处理机制

### V1.0.0 (2025-07-30) - 初始版本
- 基础ALT+TAB切换功能
- 简单tkinter界面
- 基本进程列表显示

---

**当前版本**：V2.0.0
**作者**：AI Assistant
**更新时间**：2025-07-30
**技术栈**：Python + ttkbootstrap + psutil + pyautogui
