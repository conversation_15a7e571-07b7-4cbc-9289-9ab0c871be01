#!/usr/bin/env python3
"""
测试exe版本的计时时长修复
"""

import subprocess
import time
import json
from pathlib import Path

def check_config_before_and_after():
    """检查exe运行前后的配置变化"""
    config_path = Path.home() / ".flyclock" / "config.json"
    
    print("=" * 60)
    print("测试exe版本的计时时长修复")
    print("=" * 60)
    
    # 1. 检查运行前的配置
    print("\n1. 检查运行前的配置:")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config_before = json.load(f)
        
        timer_config = config_before.get('timer', {})
        print(f"   timer.duration: {timer_config.get('duration', 'Not found')}")
        print(f"   timer.default_duration: {timer_config.get('default_duration', 'Not found')}")
        
        files_config = config_before.get('files', {})
        file_timers = files_config.get('file_specific_timers', {})
        print(f"   文件特定计时器数量: {len(file_timers)}")
        for file_path, duration in file_timers.items():
            print(f"     {file_path}: {duration}")
    else:
        print("   配置文件不存在")
        return
    
    # 2. 提示用户操作
    print("\n2. 请按以下步骤操作:")
    print("   a) 运行 ToolClock_Fixed.exe")
    print("   b) 打开一个PPT文件（确保文件名包含在file_specific_timers中）")
    print("   c) 观察计时器显示的时间是否正确")
    print("   d) 关闭exe")
    print("   e) 按Enter键继续检查配置...")
    
    input("\n按Enter键继续...")
    
    # 3. 检查运行后的配置
    print("\n3. 检查运行后的配置:")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config_after = json.load(f)
        
        timer_config = config_after.get('timer', {})
        print(f"   timer.duration: {timer_config.get('duration', 'Not found')}")
        print(f"   timer.default_duration: {timer_config.get('default_duration', 'Not found')}")
        
        # 4. 比较变化
        print("\n4. 配置变化分析:")
        duration_before = config_before.get('timer', {}).get('duration', 'Not found')
        duration_after = config_after.get('timer', {}).get('duration', 'Not found')
        
        if duration_before != duration_after:
            print(f"   ✓ timer.duration已更新: {duration_before} -> {duration_after}")
            print("   ✓ 修复生效：exe现在会根据PPT文件正确设置并保存计时时长")
        else:
            print(f"   ⚠ timer.duration未变化: {duration_before}")
            print("   可能原因：")
            print("     - 没有打开PPT文件")
            print("     - PPT文件名不在file_specific_timers中")
            print("     - 窗口检测器未正确识别PPT")
    else:
        print("   配置文件不存在")

def create_test_instructions():
    """创建测试说明"""
    print("\n" + "=" * 60)
    print("测试说明")
    print("=" * 60)
    print("""
修复内容：
- 修复了set_duration方法，现在会同时更新配置文件中的timer.duration
- 确保exe重启后能保持正确的计时时长

测试步骤：
1. 运行 ToolClock_Fixed.exe
2. 打开PowerPoint，选择以下文件之一：
   - 2025上半年工作总结-黎快人.pptx (应显示 00:08:00)
   - 2025年上半年11工作总结与展望-李超.pptx (应显示 00:00:10)
3. 观察计时器窗口显示的时间是否与文件设置匹配
4. 关闭exe，重新运行，时间应该保持一致

预期结果：
- 计时器显示正确的时间（不再总是显示00:10）
- 重启exe后时间保持一致
- 配置文件中的timer.duration被正确更新
""")

if __name__ == "__main__":
    create_test_instructions()
    check_config_before_and_after()
