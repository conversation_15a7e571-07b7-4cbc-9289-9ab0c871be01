#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
复用flyclock_tool的ConfigManager设计模式，实现配置的保存和加载
支持时间间隔、选中进程等配置的管理
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import threading

# 配置日志
logger = logging.getLogger(__name__)


class ProcessConfigManager:
    """进程监控配置管理类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "timer": {
            "interval_seconds": 5,  # 默认5秒间隔
            "auto_start": False,    # 自动开始监控
            "max_interval": 300,    # 最大间隔时间（5分钟）
            "min_interval": 1       # 最小间隔时间（1秒）
        },
        "processes": {
            "selected_pids": [],    # 选中的进程PID列表
            "selected_names": [],   # 选中的进程名称列表（用于持久化）
            "auto_refresh": True,   # 自动刷新进程列表
            "refresh_interval": 2,  # 进程列表刷新间隔（秒）
            "filter_text": "",     # 进程过滤文本
            "remember_selection": True  # 记住选择的进程
        },
        "display": {
            "window_position": {"x": 100, "y": 100},  # 窗口位置
            "window_size": {"width": 800, "height": 600},  # 窗口大小
            "font": {
                "family": "Microsoft YaHei",  # 字体
                "size": 10                     # 字体大小
            },
            "colors": {
                "background": "#F0F0F0",      # 背景色
                "foreground": "#000000",      # 前景色
                "selected": "#0078D4",        # 选中色
                "button": "#E1E1E1"           # 按钮色
            },
            "always_on_top": False,           # 总是置顶
            "show_status_bar": True,          # 显示状态栏
            "show_process_details": True      # 显示进程详细信息
        },
        "control": {
            "hotkeys": {
                "start_stop": "F5",           # 开始/停止快捷键
                "refresh": "F6"               # 刷新快捷键
            },
            "confirm_exit": True,             # 退出时确认
            "minimize_to_tray": False,        # 最小化到托盘
            "auto_save_interval": 30          # 自动保存间隔（秒）
        },
        "window_controller": {
            "max_workers": 3,                 # 最大工作线程数
            "operation_timeout": 10,          # 操作超时时间（秒）
            "retry_count": 3,                 # 重试次数
            "fail_safe": True                 # 启用安全机制
        },
        "logging": {
            "level": "INFO",                  # 日志级别
            "file_enabled": True,             # 启用文件日志
            "max_file_size": 10,              # 最大日志文件大小（MB）
            "backup_count": 5                 # 日志文件备份数量
        }
    }
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置目录路径，默认为用户目录下的.process_monitor
        """
        if config_dir is None:
            self.config_dir = Path.home() / ".process_monitor"
        else:
            self.config_dir = Path(config_dir)
        
        self.config_file = self.config_dir / "config.json"
        self.backup_file = self.config_dir / "config_backup.json"
        
        # 创建配置目录
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置数据和线程锁
        self._config = {}
        self._lock = threading.Lock()
        
        # 加载配置
        try:
            self.load_config()
            logger.info("ProcessConfigManager 初始化完成")
        except Exception as e:
            logger.error(f"ProcessConfigManager 初始化失败: {e}")
            self._config = self.DEFAULT_CONFIG.copy()    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        with self._lock:
            try:
                if self.config_file.exists():
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        user_config = json.load(f)
                    
                    # 合并默认配置和用户配置
                    self._config = self._merge_config(self.DEFAULT_CONFIG.copy(), user_config)
                    logger.info(f"配置文件加载成功: {self.config_file}")
                else:
                    # 使用默认配置
                    self._config = self.DEFAULT_CONFIG.copy()
                    self.save_config()  # 创建默认配置文件
                    logger.info("使用默认配置并创建配置文件")
                    
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                # 尝试加载备份文件
                if self._load_backup():
                    logger.info("成功从备份文件恢复配置")
                else:
                    self._config = self.DEFAULT_CONFIG.copy()
                    logger.info("使用默认配置")
            
            return self._config.copy()
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            保存是否成功
        """
        with self._lock:
            try:
                # 创建备份
                if self.config_file.exists():
                    import shutil
                    shutil.copy2(self.config_file, self.backup_file)
                
                # 保存配置
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                
                logger.info(f"配置文件保存成功: {self.config_file}")
                return True
                
            except Exception as e:
                logger.error(f"保存配置文件失败: {e}")
                return False
    
    def _load_backup(self) -> bool:
        """
        从备份文件加载配置
        
        Returns:
            加载是否成功
        """
        try:
            if self.backup_file.exists():
                with open(self.backup_file, 'r', encoding='utf-8') as f:
                    backup_config = json.load(f)
                
                self._config = self._merge_config(self.DEFAULT_CONFIG.copy(), backup_config)
                return True
        except Exception as e:
            logger.error(f"加载备份配置失败: {e}")
        
        return False    
    def get_setting(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key_path: 配置项路径，使用点号分隔，如 'timer.interval_seconds'
            default: 默认值
            
        Returns:
            配置值
        """
        with self._lock:
            try:
                keys = key_path.split('.')
                value = self._config
                
                for key in keys:
                    if isinstance(value, dict) and key in value:
                        value = value[key]
                    else:
                        return default
                
                return value
            except Exception as e:
                logger.error(f"获取配置项失败 {key_path}: {e}")
                return default
    
    def set_setting(self, key_path: str, value: Any, save: bool = True) -> bool:
        """
        设置配置项
        
        Args:
            key_path: 配置项路径，使用点号分隔
            value: 配置值
            save: 是否立即保存到文件
            
        Returns:
            设置是否成功
        """
        with self._lock:
            try:
                keys = key_path.split('.')
                config = self._config
                
                # 导航到目标位置
                for key in keys[:-1]:
                    if key not in config:
                        config[key] = {}
                    config = config[key]
                
                # 设置值
                config[keys[-1]] = value
                
                if save:
                    return self.save_config()
                
                return True
            except Exception as e:
                logger.error(f"设置配置项失败 {key_path}: {e}")
                return False
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置（线程安全）
        
        Returns:
            配置字典的副本
        """
        with self._lock:
            return self._config.copy()    
    def update_config(self, config_dict: Dict[str, Any], save: bool = True) -> bool:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
            save: 是否立即保存
            
        Returns:
            更新是否成功
        """
        with self._lock:
            try:
                self._config = self._merge_config(self._config, config_dict)
                
                if save:
                    return self.save_config()
                
                return True
            except Exception as e:
                logger.error(f"批量更新配置失败: {e}")
                return False
    
    def reset_to_default(self, save: bool = True) -> bool:
        """
        重置为默认配置
        
        Args:
            save: 是否立即保存
            
        Returns:
            重置是否成功
        """
        with self._lock:
            try:
                self._config = self.DEFAULT_CONFIG.copy()
                
                if save:
                    return self.save_config()
                
                return True
            except Exception as e:
                logger.error(f"重置配置失败: {e}")
                return False
    
    def _merge_config(self, base_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置字典（递归合并）
        
        Args:
            base_config: 基础配置
            user_config: 用户配置
            
        Returns:
            合并后的配置
        """
        result = base_config.copy()
        
        for key, value in user_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result    
    # 进程相关的专用方法
    def get_selected_pids(self) -> List[int]:
        """
        获取选中的进程PID列表
        
        Returns:
            PID列表
        """
        return self.get_setting("processes.selected_pids", [])
    
    def set_selected_pids(self, pids: List[int], save: bool = True) -> bool:
        """
        设置选中的进程PID列表
        
        Args:
            pids: PID列表
            save: 是否立即保存
            
        Returns:
            设置是否成功
        """
        return self.set_setting("processes.selected_pids", pids, save)
    
    def get_selected_names(self) -> List[str]:
        """
        获取选中的进程名称列表
        
        Returns:
            进程名称列表
        """
        return self.get_setting("processes.selected_names", [])
    
    def set_selected_names(self, names: List[str], save: bool = True) -> bool:
        """
        设置选中的进程名称列表
        
        Args:
            names: 进程名称列表
            save: 是否立即保存
            
        Returns:
            设置是否成功
        """
        return self.set_setting("processes.selected_names", names, save)
    
    def get_timer_interval(self) -> int:
        """
        获取定时器间隔
        
        Returns:
            间隔秒数
        """
        return self.get_setting("timer.interval_seconds", 5)
    
    def set_timer_interval(self, seconds: int, save: bool = True) -> bool:
        """
        设置定时器间隔
        
        Args:
            seconds: 间隔秒数
            save: 是否立即保存
            
        Returns:
            设置是否成功
        """
        # 验证间隔范围
        min_interval = self.get_setting("timer.min_interval", 1)
        max_interval = self.get_setting("timer.max_interval", 300)
        
        if seconds < min_interval:
            seconds = min_interval
        elif seconds > max_interval:
            seconds = max_interval
        
        return self.set_setting("timer.interval_seconds", seconds, save)    
    def get_window_position(self) -> Dict[str, int]:
        """
        获取窗口位置
        
        Returns:
            位置字典 {"x": x, "y": y}
        """
        return self.get_setting("display.window_position", {"x": 100, "y": 100})
    
    def set_window_position(self, x: int, y: int, save: bool = True) -> bool:
        """
        设置窗口位置
        
        Args:
            x: X坐标
            y: Y坐标
            save: 是否立即保存
            
        Returns:
            设置是否成功
        """
        return self.set_setting("display.window_position", {"x": x, "y": y}, save)
    
    def get_window_size(self) -> Dict[str, int]:
        """
        获取窗口大小
        
        Returns:
            大小字典 {"width": width, "height": height}
        """
        return self.get_setting("display.window_size", {"width": 800, "height": 600})
    
    def set_window_size(self, width: int, height: int, save: bool = True) -> bool:
        """
        设置窗口大小
        
        Args:
            width: 宽度
            height: 高度
            save: 是否立即保存
            
        Returns:
            设置是否成功
        """
        return self.set_setting("display.window_size", {"width": width, "height": height}, save)
    
    def get_auto_start(self) -> bool:
        """
        获取自动开始设置
        
        Returns:
            是否自动开始
        """
        return self.get_setting("timer.auto_start", False)
    
    def set_auto_start(self, auto_start: bool, save: bool = True) -> bool:
        """
        设置自动开始
        
        Args:
            auto_start: 是否自动开始
            save: 是否立即保存
            
        Returns:
            设置是否成功
        """
        return self.set_setting("timer.auto_start", auto_start, save)    
    def export_config(self, file_path: str) -> bool:
        """
        导出配置到文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            with self._lock:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置导出成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str, save: bool = True) -> bool:
        """
        从文件导入配置
        
        Args:
            file_path: 导入文件路径
            save: 是否立即保存
            
        Returns:
            导入是否成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            with self._lock:
                # 合并导入的配置
                self._config = self._merge_config(self.DEFAULT_CONFIG.copy(), imported_config)
                
                if save:
                    success = self.save_config()
                    if success:
                        logger.info(f"配置导入成功: {file_path}")
                    return success
            
            return True
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def validate_config(self) -> List[str]:
        """
        验证配置的有效性
        
        Returns:
            错误信息列表，空列表表示配置有效
        """
        errors = []
        
        try:
            # 验证定时器间隔
            interval = self.get_timer_interval()
            min_interval = self.get_setting("timer.min_interval", 1)
            max_interval = self.get_setting("timer.max_interval", 300)
            
            if interval < min_interval or interval > max_interval:
                errors.append(f"定时器间隔 {interval} 超出有效范围 [{min_interval}, {max_interval}]")
            
            # 验证窗口大小
            size = self.get_window_size()
            if size["width"] < 400 or size["height"] < 300:
                errors.append(f"窗口大小 {size['width']}x{size['height']} 过小")
            
            # 验证进程PID列表
            pids = self.get_selected_pids()
            if not isinstance(pids, list):
                errors.append("选中的进程PID列表格式错误")
            
        except Exception as e:
            errors.append(f"配置验证时出错: {e}")
        
        return errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            配置摘要字典
        """
        return {
            "timer_interval": self.get_timer_interval(),
            "auto_start": self.get_auto_start(),
            "selected_process_count": len(self.get_selected_pids()),
            "window_size": self.get_window_size(),
            "window_position": self.get_window_position(),
            "config_file": str(self.config_file),
            "config_exists": self.config_file.exists()
        }


# 全局配置管理器实例
_config_manager = None


def get_config_manager(config_dir: Optional[str] = None) -> ProcessConfigManager:
    """
    获取配置管理器实例（单例模式）
    
    Args:
        config_dir: 配置目录路径
        
    Returns:
        ProcessConfigManager: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ProcessConfigManager(config_dir)
    return _config_manager