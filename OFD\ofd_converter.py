from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QTextBrowser
from PyQt5.QtCore import Qt
import os

class PreviewWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件预览")
        self.setGeometry(200, 200, 600, 600)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 文件名标签
        self.file_name_label = QLabel("未选择文件")
        self.file_name_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.file_name_label)
        
        # 内容预览
        self.content_viewer = QTextBrowser()
        self.content_viewer.setReadOnly(True)
        layout.addWidget(self.content_viewer)
    
    def load_file(self, file_path):
        """加载并显示文件内容"""
        self.file_name_label.setText(f"预览文件: {os.path.basename(file_path)}")
        
        if file_path.lower().endswith('.ofd'):
            self._load_ofd(file_path)
        elif file_path.lower().endswith('.pdf'):
            self._load_pdf(file_path)
        else:
            self.content_viewer.setText("不支持的文件类型")
    
    def _load_ofd(self, file_path):
        """加载OFD文件内容（占位方法）"""
        try:
            with open(file_path, "rb") as f:
                content = f.read(1024)  # 只读取前1024字节作为预览
                self.content_viewer.setText(f"OFD文件预览:\n\n{content}")
        except Exception as e:
            raise Exception(f"无法读取OFD文件: {str(e)}")
    
    def _load_pdf(self, file_path):
        """加载PDF文件内容（占位方法）"""
        try:
            with open(file_path, "rb") as f:
                content = f.read(1024)  # 只读取前1024字节作为预览
                self.content_viewer.setText(f"PDF文件预览:\n\n{content}")
        except Exception as e:
            raise Exception(f"无法读取PDF文件: {str(e)}")
import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QListWidget, QListWidgetItem, QFileDialog, 
                             QMessageBox, QProgressBar, QLabel, QGroupBox, QSplitter, 
                             QTextEdit, QSizePolicy, QMenuBar, QAction)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap

# 导入预览窗口
try:
    from preview_window import PreviewWindow
    PREVIEW_AVAILABLE = True
except ImportError:
    PREVIEW_AVAILABLE = False

try:
    from easyofd import OFD
    EASYOFD_AVAILABLE = True
except ImportError:
    EASYOFD_AVAILABLE = False

try:
    import win32api
    import win32print
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

class ConvertWorker(QThread):
    progress_updated = pyqtSignal(int)
    file_converted = pyqtSignal(str, bool)
    conversion_finished = pyqtSignal()
    
    def __init__(self, files, output_dir):
        super().__init__()
        self.files = files
        self.output_dir = output_dir
        
    def run(self):
        if not EASYOFD_AVAILABLE:
            QMessageBox.critical(None, "错误", "缺少必要的库，请安装 easyofd")
            self.conversion_finished.emit()
            return
            
        try:
            total_files = len(self.files)
            for i, file_path in enumerate(self.files):
                try:
                    progress = int((i / total_files) * 100)
                    self.progress_updated.emit(progress)
                    
                    with open(file_path, "rb") as f:
                        ofd = OFD()
                        ofd.read(f.read())
                        pdf_bytes = ofd.to_pdf()
                        
                        base_name = os.path.splitext(os.path.basename(file_path))[0]
                        output_path = os.path.join(self.output_dir, f"{base_name}.pdf")
                        
                        with open(output_path, "wb") as pdf_file:
                            pdf_file.write(pdf_bytes)
                            
                    self.file_converted.emit(file_path, True)
                except Exception as e:
                    print(f"转换文件 {file_path} 时出错: {str(e)}")
                    self.file_converted.emit(file_path, False)
                    
                progress = int(((i + 1) / total_files) * 100)
                self.progress_updated.emit(progress)
                
            self.conversion_finished.emit()
        except Exception as e:
            QMessageBox.critical(None, "错误", f"转换过程中发生错误: {str(e)}")
            self.conversion_finished.emit()

class PrintWorker(QThread):
    progress_updated = pyqtSignal(int)
    file_printed = pyqtSignal(str, bool)
    print_finished = pyqtSignal()
    
    def __init__(self, files):
        super().__init__()
        self.files = files
        
    def run(self):
        if not WIN32_AVAILABLE:
            QMessageBox.critical(None, "错误", "缺少必要的库，请安装 pywin32")
            self.print_finished.emit()
            return
            
        try:
            total_files = len(self.files)
            for i, file_path in enumerate(self.files):
                try:
                    progress = int((i / total_files) * 100)
                    self.progress_updated.emit(progress)
                    
                    # 使用默认打印机打印PDF文件
                    win32api.ShellExecute(
                        0,
                        "print",
                        file_path,
                        None,
                        ".",
                        0
                    )
                    
                    self.file_printed.emit(file_path, True)
                except Exception as e:
                    print(f"打印文件 {file_path} 时出错: {str(e)}")
                    self.file_printed.emit(file_path, False)
                    
                progress = int(((i + 1) / total_files) * 100)
                self.progress_updated.emit(progress)
                
            self.print_finished.emit()
        except Exception as e:
            QMessageBox.critical(None, "错误", f"打印过程中发生错误: {str(e)}")
            self.print_finished.emit()

class OFDConverterWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OFD转PDF转换器")
        self.setGeometry(100, 100, 1200, 800)
        
        self.file_list = []
        self.output_dir = ""
        self.preview_window = None
        
        self.init_ui()
        
    def init_ui(self):
        # 创建菜单栏
        self.create_menu()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建文件操作区域
        file_group = QGroupBox("文件操作")
        file_layout = QHBoxLayout()
        file_group.setLayout(file_layout)
        
        self.add_file_btn = QPushButton("添加OFD文件")
        self.add_file_btn.clicked.connect(self.add_files)
        file_layout.addWidget(self.add_file_btn)
        
        self.add_folder_btn = QPushButton("添加文件夹")
        self.add_folder_btn.clicked.connect(self.add_folder)
        file_layout.addWidget(self.add_folder_btn)
        
        self.clear_btn = QPushButton("清空列表")
        self.clear_btn.clicked.connect(self.clear_list)
        file_layout.addWidget(self.clear_btn)
        
        self.output_dir_btn = QPushButton("选择输出目录")
        self.output_dir_btn.clicked.connect(self.select_output_dir)
        file_layout.addWidget(self.output_dir_btn)
        
        self.convert_btn = QPushButton("开始转换")
        self.convert_btn.clicked.connect(self.start_conversion)
        self.convert_btn.setEnabled(False)
        file_layout.addWidget(self.convert_btn)
        
        self.preview_btn = QPushButton("预览选中文件")
        self.preview_btn.clicked.connect(self.preview_file)
        self.preview_btn.setEnabled(PREVIEW_AVAILABLE)
        file_layout.addWidget(self.preview_btn)
        
        self.print_btn = QPushButton("打印选中PDF")
        self.print_btn.clicked.connect(self.print_files)
        self.print_btn.setEnabled(False)
        file_layout.addWidget(self.print_btn)
        
        main_layout.addWidget(file_group)
        
        # 创建分割器用于文件列表和日志区域
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：文件列表区域
        list_group = QGroupBox("文件列表")
        list_layout = QVBoxLayout()
        list_group.setLayout(list_layout)
        
        self.file_list_widget = QListWidget()
        self.file_list_widget.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.file_list_widget)
        
        splitter.addWidget(list_group)
        
        # 右侧：日志和信息区域
        info_group = QGroupBox("日志和信息")
        info_layout = QVBoxLayout()
        info_group.setLayout(info_layout)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        info_layout.addWidget(self.log_text)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        
        splitter.addWidget(info_group)
        splitter.setSizes([600, 600])
        
        main_layout.addWidget(splitter)
        
        # 进度和状态区域
        status_group = QGroupBox("状态")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("请添加需要转换的OFD文件")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        main_layout.addWidget(status_group)
        
    def create_menu(self):
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        add_files_action = QAction('添加文件', self)
        add_files_action.triggered.connect(self.add_files)
        file_menu.addAction(add_files_action)
        
        add_folder_action = QAction('添加文件夹', self)
        add_folder_action.triggered.connect(self.add_folder)
        file_menu.addAction(add_folder_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        
        convert_action = QAction('转换OFD到PDF', self)
        convert_action.triggered.connect(self.start_conversion)
        tools_menu.addAction(convert_action)
        
        print_action = QAction('打印PDF文件', self)
        print_action.triggered.connect(self.print_files)
        tools_menu.addAction(print_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def add_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择OFD文件", "", "OFD文件 (*.ofd)")
        
        if files:
            for file_path in files:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
                    item = QListWidgetItem(os.path.basename(file_path))
                    item.setToolTip(file_path)
                    self.file_list_widget.addItem(item)
            
            self.update_ui_state()
            self.log_message(f"添加了 {len(files)} 个文件")
    
    def add_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择包含OFD文件的文件夹")
        
        if folder:
            ofd_files = []
            for file in os.listdir(folder):
                if file.lower().endswith(".ofd"):
                    file_path = os.path.join(folder, file)
                    ofd_files.append(file_path)
            
            for file_path in ofd_files:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
                    item = QListWidgetItem(os.path.basename(file_path))
                    item.setToolTip(file_path)
                    self.file_list_widget.addItem(item)
            
            self.update_ui_state()
            self.log_message(f"从文件夹添加了 {len(ofd_files)} 个文件: {folder}")
    
    def clear_list(self):
        self.file_list.clear()
        self.file_list_widget.clear()
        self.update_ui_state()
        self.log_message("清空文件列表")
    
    def select_output_dir(self):
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_dir = directory
            self.status_label.setText(f"输出目录: {directory}")
            self.update_ui_state()
            self.log_message(f"选择输出目录: {directory}")
    
    def update_ui_state(self):
        has_files = len(self.file_list) > 0
        has_output_dir = bool(self.output_dir)
        
        self.convert_btn.setEnabled(has_files and has_output_dir)
        
        if has_files and not has_output_dir:
            self.status_label.setText("请选择输出目录")
        elif has_files and has_output_dir:
            self.status_label.setText(f"已选择 {len(self.file_list)} 个文件，准备转换")
        elif not has_files and has_output_dir:
            self.status_label.setText("请添加需要转换的OFD文件")
    
    def on_selection_changed(self):
        selected_items = self.file_list_widget.selectedItems()
        
        # 检查选中的是否是PDF文件（转换后的）
        is_pdf_selected = False
        if selected_items:
            row = self.file_list_widget.row(selected_items[0])
            file_path = self.file_list[row]
            if file_path.lower().endswith('.pdf') or (self.output_dir and os.path.exists(os.path.join(self.output_dir, os.path.splitext(os.path.basename(file_path))[0] + '.pdf'))):
                is_pdf_selected = True
        
        self.print_btn.setEnabled(is_pdf_selected and WIN32_AVAILABLE)
        
        # 显示选中文件的信息
        if selected_items:
            row = self.file_list_widget.row(selected_items[0])
            file_path = self.file_list[row]
            self.show_file_info(file_path)
            
            # 自动预览选中的文件
            self.auto_preview_file(file_path)
    
    def auto_preview_file(self, file_path):
        """自动预览文件"""
        # 创建或显示预览窗口
        if self.preview_window is None:
            self.preview_window = PreviewWindow()
            self.preview_window.show()
        
        # 激活窗口
        self.preview_window.activateWindow()
        
        # 加载文件
        try:
            self.preview_window.load_file(file_path)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法预览文件:\n{str(e)}")
    
    def preview_file(self):
        """手动预览文件（保留原有功能）"""
        selected_items = self.file_list_widget.selectedItems()
        if selected_items:
            row = self.file_list_widget.row(selected_items[0])
            file_path = self.file_list[row]
            
            # 创建或显示预览窗口
            if self.preview_window is None:
                self.preview_window = PreviewWindow()
                self.preview_window.show()
            
            # 激活窗口
            self.preview_window.activateWindow()
            
            # 加载文件
            try:
                self.preview_window.load_file(file_path)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法预览文件:\n{str(e)}")
    
    def show_file_info(self, file_path):
        try:
            info_text = f"文件路径: {file_path}\n"
            if os.path.exists(file_path):
                info_text += f"文件大小: {os.path.getsize(file_path)} 字节\n"
                info_text += f"修改时间: {os.path.getmtime(file_path)}\n"
            else:
                info_text += "文件不存在\n"
            self.info_text.setText(info_text)
        except Exception as e:
            self.info_text.setText(f"无法获取文件信息: {str(e)}")
    
    def start_conversion(self):
        if not self.file_list:
            QMessageBox.warning(self, "警告", "请先添加OFD文件")
            return
            
        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录")
            return
            
        # 禁用按钮
        self.add_file_btn.setEnabled(False)
        self.add_folder_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        self.output_dir_btn.setEnabled(False)
        self.convert_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        
        self.log_message(f"开始转换 {len(self.file_list)} 个文件到 {self.output_dir}")
        
        # 创建并启动转换线程
        self.worker = ConvertWorker(self.file_list, self.output_dir)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.file_converted.connect(self.on_file_converted)
        self.worker.conversion_finished.connect(self.on_conversion_finished)
        self.worker.start()
        
        self.status_label.setText("正在转换...")
    
    def update_progress(self, value):
        self.progress_bar.setValue(value)
    
    def on_file_converted(self, file_path, success):
        file_name = os.path.basename(file_path)
        if success:
            self.status_label.setText(f"已转换: {file_name}")
            self.log_message(f"✓ 转换成功: {file_name}")
        else:
            self.status_label.setText(f"转换失败: {file_name}")
            self.log_message(f"✗ 转换失败: {file_name}")
    
    def on_conversion_finished(self):
        # 恢复按钮
        self.add_file_btn.setEnabled(True)
        self.add_folder_btn.setEnabled(True)
        self.clear_btn.setEnabled(True)
        self.output_dir_btn.setEnabled(True)
        self.convert_btn.setEnabled(True)
        self.preview_btn.setEnabled(PREVIEW_AVAILABLE)
        self.update_ui_state()
        
        self.status_label.setText("转换完成")
        self.log_message("所有文件转换完成")
        QMessageBox.information(self, "完成", "所有文件转换完成")
    
    def print_files(self):
        selected_items = self.file_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择要打印的PDF文件")
            return
            
        if not WIN32_AVAILABLE:
            QMessageBox.critical(self, "错误", "缺少必要的库，请安装 pywin32")
            return
            
        # 获取选中的PDF文件路径
        pdf_files = []
        for item in selected_items:
            row = self.file_list_widget.row(item)
            ofd_file_path = self.file_list[row]
            
            # 构造PDF文件路径
            if ofd_file_path.lower().endswith('.pdf'):
                pdf_file_path = ofd_file_path
            else:
                base_name = os.path.splitext(os.path.basename(ofd_file_path))[0]
                pdf_file_path = os.path.join(self.output_dir, f"{base_name}.pdf")
                
            if os.path.exists(pdf_file_path):
                pdf_files.append(pdf_file_path)
            else:
                QMessageBox.warning(self, "警告", f"文件不存在: {pdf_file_path}")
                return
        
        # 禁用按钮
        self.print_btn.setEnabled(False)
        self.add_file_btn.setEnabled(False)
        self.add_folder_btn.setEnabled(False)
        self.convert_btn.setEnabled(False)
        
        self.log_message(f"开始打印 {len(pdf_files)} 个文件")
        
        # 创建并启动打印线程
        self.print_worker = PrintWorker(pdf_files)
        self.print_worker.progress_updated.connect(self.update_progress)
        self.print_worker.file_printed.connect(self.on_file_printed)
        self.print_worker.print_finished.connect(self.on_print_finished)
        self.print_worker.start()
        
        self.status_label.setText("正在打印...")
    
    def on_file_printed(self, file_path, success):
        file_name = os.path.basename(file_path)
        if success:
            self.status_label.setText(f"已打印: {file_name}")
            self.log_message(f"✓ 打印成功: {file_name}")
        else:
            self.status_label.setText(f"打印失败: {file_name}")
            self.log_message(f"✗ 打印失败: {file_name}")
    
    def on_print_finished(self):
        # 恢复按钮
        self.print_btn.setEnabled(True)
        self.add_file_btn.setEnabled(True)
        self.add_folder_btn.setEnabled(True)
        self.convert_btn.setEnabled(True)
        self.update_ui_state()
        
        self.status_label.setText("打印完成")
        self.log_message("所有文件打印完成")
        QMessageBox.information(self, "完成", "所有文件打印完成")
    
    def show_about(self):
        about_text = """
OFD转PDF转换器
版本: 1.0

功能:
1. OFD文件转PDF
2. 批量处理
3. 文件预览
4. PDF打印

技术栈:
- Python
- PyQt5 (图形界面)
- easyofd (OFD处理)
- pywin32 (打印功能)
        """.strip()
        QMessageBox.information(self, "关于", about_text)
    
    def log_message(self, message):
        self.log_text.append(message)

def main():
    app = QApplication(sys.argv)
    window = OFDConverterWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()