#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设置界面UI修改
验证智能分析按钮是否已被移除
"""

import sys
import os
import tkinter as tk
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from gui.settings_window import SettingsWindow
from core.config_manager import ConfigManager

def test_settings_ui():
    """测试设置界面UI"""
    print("=" * 60)
    print("测试设置界面UI修改")
    print("=" * 60)
    
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建设置窗口（不需要传递config_manager参数）
    settings_window = SettingsWindow(root)
    
    print("✓ 设置窗口已创建")
    print("📋 请检查设置界面中的按钮：")
    print("  - ✅ 应该有：新增、修改、删除、上移、下移")
    print("  - ❌ 不应该有：智能分析")
    
    # 显示窗口
    settings_window.show()
    
    print("\\n🔍 UI验证要点：")
    print("1. 文件计时器设置区域右侧的按钮组")
    print("2. 确认没有'智能分析'按钮")
    print("3. 其他功能按钮正常显示")
    
    print("\\n⏰ 窗口将在10秒后自动关闭...")
    
    # 10秒后自动关闭
    def auto_close():
        print("🔄 自动关闭设置窗口")
        settings_window.window.destroy()
        root.quit()
    
    root.after(10000, auto_close)
    
    # 运行主循环
    root.mainloop()
    
    print("\\n✅ 设置界面UI测试完成")

if __name__ == "__main__":
    test_settings_ui()
