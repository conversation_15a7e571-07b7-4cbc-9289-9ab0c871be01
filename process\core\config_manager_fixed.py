#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版配置管理模块
简化初始化流程，避免卡顿问题
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import threading

class ProcessConfigManager:
    """进程监控配置管理类 - 修复版"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "timer": {
            "interval_seconds": 5,
            "auto_start": False,
            "max_interval": 300,
            "min_interval": 1
        },
        "processes": {
            "selected_pids": [],
            "selected_names": [],
            "auto_refresh": True,
            "refresh_interval": 2
        },
        "display": {
            "window_position": {"x": 100, "y": 100},
            "window_size": {"width": 800, "height": 600}
        }
    }
    
    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器 - 简化版"""
        try:
            if config_dir is None:
                # 使用临时目录避免权限问题
                import tempfile
                self.config_dir = Path(tempfile.gettempdir()) / "process_monitor"
            else:
                self.config_dir = Path(config_dir)
            
            self.config_file = self.config_dir / "config.json"
            
            # 创建配置目录
            self.config_dir.mkdir(exist_ok=True)
            
            # 配置数据和线程锁
            self._config = self.DEFAULT_CONFIG.copy()
            self._lock = threading.Lock()
            
            # 尝试加载配置，失败则使用默认配置
            self._safe_load_config()
            
        except Exception:
            # 如果初始化失败，使用默认配置
            self._config = self.DEFAULT_CONFIG.copy()
            self._lock = threading.Lock()
    
    def _safe_load_config(self):
        """安全加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 简单合并配置
                for key, value in user_config.items():
                    if key in self._config:
                        if isinstance(value, dict) and isinstance(self._config[key], dict):
                            self._config[key].update(value)
                        else:
                            self._config[key] = value
        except Exception:
            # 加载失败则使用默认配置
            pass
    
    def get_timer_interval(self) -> int:
        """获取定时器间隔"""
        return self._config.get("timer", {}).get("interval_seconds", 5)
    
    def set_timer_interval(self, interval: int, save: bool = True) -> bool:
        """设置定时器间隔"""
        try:
            with self._lock:
                if "timer" not in self._config:
                    self._config["timer"] = {}
                self._config["timer"]["interval_seconds"] = max(1, min(300, interval))
                
                if save:
                    return self.save_config()
                return True
        except Exception:
            return False
    
    def get_selected_pids(self) -> List[int]:
        """获取选中的进程PID列表"""
        return self._config.get("processes", {}).get("selected_pids", [])
    
    def set_selected_pids(self, pids: List[int], save: bool = True) -> bool:
        """设置选中的进程PID列表"""
        try:
            with self._lock:
                if "processes" not in self._config:
                    self._config["processes"] = {}
                self._config["processes"]["selected_pids"] = pids
                
                if save:
                    return self.save_config()
                return True
        except Exception:
            return False
    
    def get_window_size(self) -> Dict[str, int]:
        """获取窗口大小"""
        return self._config.get("display", {}).get("window_size", {"width": 800, "height": 600})
    
    def set_window_size(self, width: int, height: int, save: bool = True) -> bool:
        """设置窗口大小"""
        try:
            with self._lock:
                if "display" not in self._config:
                    self._config["display"] = {}
                self._config["display"]["window_size"] = {"width": width, "height": height}
                
                if save:
                    return self.save_config()
                return True
        except Exception:
            return False
    
    def get_window_position(self) -> Dict[str, int]:
        """获取窗口位置"""
        return self._config.get("display", {}).get("window_position", {"x": 100, "y": 100})
    
    def set_window_position(self, x: int, y: int, save: bool = True) -> bool:
        """设置窗口位置"""
        try:
            with self._lock:
                if "display" not in self._config:
                    self._config["display"] = {}
                self._config["display"]["window_position"] = {"x": x, "y": y}
                
                if save:
                    return self.save_config()
                return True
        except Exception:
            return False
    
    def save_config(self) -> bool:
        """保存配置"""
        try:
            with self._lock:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                return True
        except Exception:
            return False
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        with self._lock:
            return self._config.copy()


# 全局配置管理器实例
_config_manager = None

def get_config_manager(config_dir: Optional[str] = None) -> ProcessConfigManager:
    """获取配置管理器实例（单例模式）"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ProcessConfigManager(config_dir)
    return _config_manager