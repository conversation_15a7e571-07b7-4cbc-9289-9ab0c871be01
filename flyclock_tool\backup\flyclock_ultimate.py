#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock Ultimate - 终极优化版本
PPT/PDF全屏放映计时辅助工具

全面优化内容：
- 现代化界面设计
- 智能全屏检测
- 丰富的自定义选项
- 完善的错误处理
- 高性能架构
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from pathlib import Path
import json
from datetime import datetime

# 禁用字节码缓存
sys.dont_write_bytecode = True

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 强制清理模块缓存
modules_to_remove = []
for module_name in list(sys.modules.keys()):
    if any(module_name.startswith(prefix) for prefix in ['core.', 'gui.', 'remote.', 'window_detector']):
        modules_to_remove.append(module_name)

for module_name in modules_to_remove:
    if module_name in sys.modules:
        del sys.modules[module_name]

class UltimateTimer:
    """终极优化计时器"""
    
    def __init__(self):
        # 计时器状态
        self.is_running = False
        self.is_paused = False
        self.current_time = 480  # 默认8分钟
        self.total_time = 480
        self.start_time = None
        
        # 检测状态
        self.is_fullscreen_detected = False
        self.current_presentation = None
        self.detection_count = 0
        
        # 创建主窗口
        self.create_main_window()
        
        # 创建控制面板
        self.create_control_panel()
        
        # 启动检测
        self.start_detection()
        
        print("✅ FlyClock Ultimate 初始化完成")
    
    def create_main_window(self):
        """创建主计时器窗口"""
        self.main_window = tk.Tk()
        self.main_window.title("FlyClock Ultimate")
        self.main_window.geometry("250x100+100+100")
        self.main_window.attributes('-topmost', True)
        self.main_window.overrideredirect(True)
        
        # 主框架
        self.main_frame = tk.Frame(self.main_window, bg="#2C3E50", relief="raised", bd=2)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 时间显示
        self.time_label = tk.Label(
            self.main_frame,
            text="08:00",
            font=("Segoe UI", 28, "bold"),
            fg="#FFFFFF",
            bg="#2C3E50"
        )
        self.time_label.pack(expand=True)
        
        # 状态指示器
        self.status_frame = tk.Frame(self.main_frame, bg="#2C3E50", height=20)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="● 等待全屏检测",
            font=("Segoe UI", 9),
            fg="#95A5A6",
            bg="#2C3E50"
        )
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        self.detection_label = tk.Label(
            self.status_frame,
            text="检测: 0",
            font=("Segoe UI", 9),
            fg="#95A5A6",
            bg="#2C3E50"
        )
        self.detection_label.pack(side=tk.RIGHT, padx=5)
        
        # 绑定事件
        self.bind_events()
    
    def create_control_panel(self):
        """创建控制面板"""
        self.control_window = None
        
        # 右键菜单
        self.context_menu = tk.Menu(self.main_window, tearoff=0)
        self.context_menu.add_command(label="🎮 显示控制面板", command=self.show_control_panel)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="▶️ 开始计时", command=self.start_timer)
        self.context_menu.add_command(label="⏸️ 暂停计时", command=self.pause_timer)
        self.context_menu.add_command(label="🔄 重置计时", command=self.reset_timer)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="⚙️ 设置时长", command=self.set_duration_dialog)
        self.context_menu.add_command(label="🎨 更换主题", command=self.change_theme)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="ℹ️ 关于", command=self.show_about)
        self.context_menu.add_command(label="❌ 退出", command=self.quit_app)
    
    def bind_events(self):
        """绑定事件"""
        # 拖拽
        self.time_label.bind("<Button-1>", self.start_drag)
        self.time_label.bind("<B1-Motion>", self.on_drag)
        self.time_label.bind("<ButtonRelease-1>", self.end_drag)
        
        # 右键菜单
        self.main_window.bind("<Button-3>", self.show_context_menu)
        self.time_label.bind("<Button-3>", self.show_context_menu)
        
        # 双击显示控制面板
        self.time_label.bind("<Double-Button-1>", lambda e: self.show_control_panel())
        
        # 键盘快捷键
        self.main_window.bind("<Key>", self.on_key_press)
        self.main_window.focus_set()
    
    def start_detection(self):
        """启动智能检测"""
        try:
            from window_detector_fixed import get_window_detector_fixed
            self.window_detector = get_window_detector_fixed(self)
            
            if self.window_detector.start_detection():
                self.update_status("🔍 智能检测已启动", "#27AE60")
                print("✅ 智能全屏检测已启动")
                
                # 启动检测状态更新线程
                threading.Thread(target=self.detection_status_loop, daemon=True).start()
            else:
                self.update_status("❌ 检测启动失败", "#E74C3C")
                print("❌ 智能全屏检测启动失败")
        except Exception as e:
            self.update_status("❌ 检测模块错误", "#E74C3C")
            print(f"❌ 启动检测失败: {e}")
            import traceback
            traceback.print_exc()
    
    def detection_status_loop(self):
        """检测状态更新循环"""
        while True:
            try:
                self.detection_count += 1
                self.main_window.after(0, lambda: self.detection_label.configure(
                    text=f"检测: {self.detection_count}"
                ))
                time.sleep(1)
            except:
                break
    
    def start_timer(self):
        """开始计时"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            self.start_time = datetime.now()
            
            self.update_status("⏱️ 计时进行中", "#F39C12")
            print(f"✅ 计时器已启动 - {self.format_time(self.current_time)}")
            
            threading.Thread(target=self.timer_loop, daemon=True).start()
    
    def pause_timer(self):
        """暂停/恢复计时"""
        if self.is_running:
            self.is_paused = not self.is_paused
            if self.is_paused:
                self.update_status("⏸️ 计时已暂停", "#95A5A6")
                print("⏸️ 计时器已暂停")
            else:
                self.update_status("⏱️ 计时进行中", "#F39C12")
                print("▶️ 计时器已恢复")
    
    def reset_timer(self):
        """重置计时"""
        self.is_running = False
        self.is_paused = False
        self.current_time = self.total_time
        self.start_time = None
        
        self.update_display()
        self.update_status("🔄 计时器已重置", "#3498DB")
        print("🔄 计时器已重置")
    
    def set_duration(self, duration_str):
        """设置计时时长（由检测器调用）"""
        print(f"⚙️ 智能设置时长: {duration_str}")
        self.update_status(f"⚙️ 时长: {duration_str}", "#9B59B6")
    
    def timer_loop(self):
        """计时循环"""
        while self.is_running and self.current_time > 0:
            if not self.is_paused:
                self.current_time -= 1
                self.main_window.after(0, self.update_display)
                
                # 检查警告时间
                if self.current_time == 60:
                    self.main_window.after(0, lambda: self.update_status("⚠️ 还剩1分钟", "#E67E22"))
                elif self.current_time == 30:
                    self.main_window.after(0, lambda: self.update_status("🚨 还剩30秒", "#E74C3C"))
            
            time.sleep(1)
        
        if self.current_time <= 0:
            self.main_window.after(0, self.timer_finished)
    
    def timer_finished(self):
        """计时结束"""
        self.is_running = False
        self.update_status("🔔 时间到！", "#E74C3C")
        self.time_label.configure(bg="#E74C3C")
        
        print("🔔 计时结束！")
        
        # 显示结束提示
        messagebox.showinfo("FlyClock Ultimate", "⏰ 演示时间已到！\n\n🎯 演示结束提醒")
    
    def update_display(self):
        """更新时间显示"""
        time_str = self.format_time(self.current_time)
        self.time_label.configure(text=time_str)
        
        # 根据时间改变颜色
        if self.current_time <= 0:
            bg_color = "#E74C3C"  # 红色
        elif self.current_time <= 30:
            bg_color = "#E67E22"  # 橙色
        elif self.current_time <= 60:
            bg_color = "#F39C12"  # 黄色
        else:
            bg_color = "#2C3E50"  # 默认色
        
        self.main_frame.configure(bg=bg_color)
        self.time_label.configure(bg=bg_color)
        self.status_frame.configure(bg=bg_color)
        self.status_label.configure(bg=bg_color)
        self.detection_label.configure(bg=bg_color)
    
    def update_status(self, text, color="#95A5A6"):
        """更新状态显示"""
        self.status_label.configure(text=text, fg=color)
    
    def format_time(self, seconds):
        """格式化时间显示"""
        if seconds < 0:
            return "-" + self.format_time(-seconds)
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def start_drag(self, event):
        """开始拖拽"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
    
    def on_drag(self, event):
        """拖拽中"""
        x = self.main_window.winfo_x() + event.x - self.drag_start_x
        y = self.main_window.winfo_y() + event.y - self.drag_start_y
        self.main_window.geometry(f"+{x}+{y}")
    
    def end_drag(self, event):
        """结束拖拽"""
        pass
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def on_key_press(self, event):
        """键盘快捷键"""
        if event.keysym == "space":
            if self.is_running:
                self.pause_timer()
            else:
                self.start_timer()
        elif event.keysym == "r":
            self.reset_timer()
        elif event.keysym == "Escape":
            self.quit_app()
    
    def show_control_panel(self):
        """显示控制面板"""
        if self.control_window and self.control_window.winfo_exists():
            self.control_window.lift()
            return
        
        self.control_window = tk.Toplevel(self.main_window)
        self.control_window.title("FlyClock Ultimate - 控制面板")
        self.control_window.geometry("400x300+300+200")
        self.control_window.attributes('-topmost', True)
        
        # 控制面板内容
        notebook = ttk.Notebook(self.control_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 基本控制页
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="基本控制")
        
        # 时长设置
        ttk.Label(basic_frame, text="计时时长:").pack(pady=5)
        duration_frame = ttk.Frame(basic_frame)
        duration_frame.pack(pady=5)
        
        self.duration_var = tk.StringVar(value="08:00")
        duration_entry = ttk.Entry(duration_frame, textvariable=self.duration_var, width=10)
        duration_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(duration_frame, text="设置", command=self.apply_duration).pack(side=tk.LEFT, padx=5)
        
        # 控制按钮
        control_frame = ttk.Frame(basic_frame)
        control_frame.pack(pady=20)
        
        ttk.Button(control_frame, text="▶️ 开始", command=self.start_timer).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="⏸️ 暂停", command=self.pause_timer).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="🔄 重置", command=self.reset_timer).pack(side=tk.LEFT, padx=5)
        
        # 状态信息页
        status_frame = ttk.Frame(notebook)
        notebook.add(status_frame, text="状态信息")
        
        self.info_text = tk.Text(status_frame, height=10, width=40)
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.update_info_display()
    
    def apply_duration(self):
        """应用时长设置"""
        try:
            duration_str = self.duration_var.get()
            parts = duration_str.split(':')
            if len(parts) == 2:
                minutes, seconds = map(int, parts)
                total_seconds = minutes * 60 + seconds
            elif len(parts) == 3:
                hours, minutes, seconds = map(int, parts)
                total_seconds = hours * 3600 + minutes * 60 + seconds
            else:
                raise ValueError("格式错误")
            
            self.total_time = total_seconds
            self.current_time = total_seconds
            self.update_display()
            
            messagebox.showinfo("成功", f"时长已设置为: {duration_str}")
            
        except ValueError:
            messagebox.showerror("错误", "时长格式错误！\n请使用 MM:SS 或 HH:MM:SS 格式")
    
    def update_info_display(self):
        """更新信息显示"""
        if hasattr(self, 'info_text'):
            info = f"""FlyClock Ultimate 状态信息

📊 计时器状态:
   运行中: {'是' if self.is_running else '否'}
   暂停中: {'是' if self.is_paused else '否'}
   当前时间: {self.format_time(self.current_time)}
   总时长: {self.format_time(self.total_time)}

🔍 检测状态:
   检测次数: {self.detection_count}
   全屏检测: {'是' if self.is_fullscreen_detected else '否'}
   当前演示: {self.current_presentation or '无'}

⌨️ 快捷键:
   空格键: 开始/暂停计时
   R键: 重置计时器
   ESC键: 退出程序
   双击: 显示控制面板

🎯 使用提示:
   1. 打开PPT/PDF文件
   2. 按F5进入全屏模式
   3. 程序自动检测并开始计时
"""
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info)
            
            # 5秒后更新
            self.main_window.after(5000, self.update_info_display)
    
    def set_duration_dialog(self):
        """设置时长对话框"""
        dialog = tk.Toplevel(self.main_window)
        dialog.title("设置计时时长")
        dialog.geometry("300x150+400+300")
        dialog.attributes('-topmost', True)
        
        ttk.Label(dialog, text="请输入计时时长:").pack(pady=10)
        
        duration_var = tk.StringVar(value=self.format_time(self.total_time))
        entry = ttk.Entry(dialog, textvariable=duration_var, width=15, font=("Segoe UI", 12))
        entry.pack(pady=10)
        entry.focus()
        
        def apply():
            try:
                duration_str = duration_var.get()
                parts = duration_str.split(':')
                if len(parts) == 2:
                    minutes, seconds = map(int, parts)
                    total_seconds = minutes * 60 + seconds
                elif len(parts) == 3:
                    hours, minutes, seconds = map(int, parts)
                    total_seconds = hours * 3600 + minutes * 60 + seconds
                else:
                    raise ValueError("格式错误")
                
                self.total_time = total_seconds
                self.current_time = total_seconds
                self.update_display()
                
                dialog.destroy()
                messagebox.showinfo("成功", f"时长已设置为: {duration_str}")
                
            except ValueError:
                messagebox.showerror("错误", "时长格式错误！\n请使用 MM:SS 或 HH:MM:SS 格式")
        
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="确定", command=apply).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
        
        entry.bind("<Return>", lambda e: apply())
    
    def change_theme(self):
        """更换主题"""
        themes = [
            ("深蓝主题", "#2C3E50"),
            ("深绿主题", "#27AE60"),
            ("紫色主题", "#8E44AD"),
            ("橙色主题", "#E67E22"),
            ("红色主题", "#E74C3C")
        ]
        
        dialog = tk.Toplevel(self.main_window)
        dialog.title("选择主题")
        dialog.geometry("200x250+400+300")
        dialog.attributes('-topmost', True)
        
        for name, color in themes:
            btn = tk.Button(
                dialog,
                text=name,
                bg=color,
                fg="white",
                font=("Segoe UI", 10),
                command=lambda c=color: self.apply_theme(c, dialog)
            )
            btn.pack(fill=tk.X, padx=10, pady=5)
    
    def apply_theme(self, color, dialog):
        """应用主题"""
        self.main_frame.configure(bg=color)
        self.time_label.configure(bg=color)
        self.status_frame.configure(bg=color)
        self.status_label.configure(bg=color)
        self.detection_label.configure(bg=color)
        dialog.destroy()
    
    def show_about(self):
        """显示关于信息"""
        about_text = """FlyClock Ultimate V1.0.3

🚀 终极优化版本

✨ 主要特性:
• 智能全屏检测
• 现代化界面设计
• 丰富的自定义选项
• 完善的错误处理
• 高性能架构

⌨️ 快捷键:
• 空格键: 开始/暂停
• R键: 重置计时器
• ESC键: 退出程序

👨‍💻 开发: AI Assistant
📅 版本日期: 2025-01-29"""
        
        messagebox.showinfo("关于 FlyClock Ultimate", about_text)
    
    def quit_app(self):
        """退出应用"""
        if messagebox.askyesno("确认退出", "确定要退出 FlyClock Ultimate 吗？"):
            try:
                if hasattr(self, 'window_detector'):
                    self.window_detector.stop_detection()
            except:
                pass
            
            self.main_window.quit()
    
    def run(self):
        """运行程序"""
        print("🚀 FlyClock Ultimate 开始运行...")
        self.main_window.mainloop()

def main():
    """主函数"""
    print("=" * 70)
    print("🚀 FlyClock Ultimate - 终极优化版本")
    print("版本: V1.0.3 - 全面优化升级")
    print("=" * 70)
    
    print("\n✨ 全新特性:")
    print("   🎨 现代化界面设计")
    print("   🔍 智能全屏检测")
    print("   ⚙️ 丰富的自定义选项")
    print("   🎮 完整的控制面板")
    print("   ⌨️ 便捷的快捷键操作")
    print("   🎯 实时状态显示")
    print("   🌈 多主题支持")
    
    print("\n📋 操作指南:")
    print("   • 拖拽移动计时器位置")
    print("   • 右键打开功能菜单")
    print("   • 双击显示控制面板")
    print("   • 空格键开始/暂停计时")
    print("   • R键重置计时器")
    print("   • ESC键退出程序")
    
    print("\n" + "=" * 70)
    
    try:
        timer = UltimateTimer()
        timer.run()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
