#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock Tool - 简化EXE打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🏗️ FlyClock Tool - 简化EXE构建")
    print("=" * 40)
    
    # 检查是否在正确的目录
    if not os.path.exists('flyclock_tool/main.py'):
        print("❌ 错误：请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 清理之前的构建
    if os.path.exists('dist'):
        print("🧹 清理dist目录...")
        shutil.rmtree('dist')
    
    if os.path.exists('build'):
        print("🧹 清理build目录...")
        shutil.rmtree('build')
    
    # 构建命令
    cmd = [
        'pyinstaller',
        '--onefile',                    # 单文件
        '--windowed',                   # 无控制台
        '--name=FlyClock',              # 文件名
        '--add-data=flyclock_tool/assets;assets',  # 资源文件
        'flyclock_tool/main.py'         # 主程序
    ]
    
    print("📦 开始构建...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 构建成功！")
        
        # 检查生成的文件
        exe_path = Path("dist/FlyClock.exe")
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {file_size:.2f} MB")
            print("🎉 FlyClock.exe 构建完成！")
        else:
            print("❌ 未找到生成的exe文件")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败，错误代码: {e.returncode}")
        sys.exit(1)

if __name__ == "__main__":
    main()
