#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序 - 验证修复效果
"""

import sys
import os
import tkinter as tk
from pathlib import Path
import threading
import time

# 禁用字节码缓存
sys.dont_write_bytecode = True

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("开始启动FlyClock测试版本...")

try:
    from core.config_manager import config_manager
    print("✓ 配置管理器加载成功")
except Exception as e:
    print(f"✗ 配置管理器加载失败: {e}")
    sys.exit(1)

try:
    from core.window_detector import get_window_detector
    print("✓ 窗口检测器加载成功")
except Exception as e:
    print(f"✗ 窗口检测器加载失败: {e}")
    sys.exit(1)

class SimpleTimerWindow:
    """简化的计时器窗口"""
    
    def __init__(self):
        self.is_running = False
        self.current_time = 480  # 8分钟
        
        # 创建窗口
        self.window = tk.Tk()
        self.window.title("FlyClock Test")
        self.window.geometry("200x80+100+100")
        self.window.attributes('-topmost', True)
        
        # 时间显示
        self.time_label = tk.Label(
            self.window,
            text="08:00",
            font=("Arial", 24, "bold"),
            fg="#FFFFFF",
            bg="#4A90E2"
        )
        self.time_label.pack(expand=True)
        
        # 启动窗口检测
        self.window_detector = get_window_detector(self)
        self.start_window_detection()
        
        print("✓ 简化计时器窗口创建成功")
    
    def start_window_detection(self):
        """启动窗口检测"""
        try:
            if self.window_detector.start_detection():
                print("✓ PPT/PDF全屏检测已启动")
            else:
                print("✗ PPT/PDF全屏检测启动失败")
        except Exception as e:
            print(f"✗ 启动窗口检测失败: {e}")
    
    def start_timer(self):
        """开始计时"""
        if not self.is_running:
            self.is_running = True
            print("✓ 计时器已启动")
            # 启动计时线程
            threading.Thread(target=self.timer_loop, daemon=True).start()
    
    def pause_timer(self):
        """暂停计时"""
        if self.is_running:
            self.is_running = False
            print("✓ 计时器已暂停")
    
    def set_duration(self, duration_str):
        """设置计时时长"""
        print(f"✓ 设置计时时长: {duration_str}")
    
    def timer_loop(self):
        """计时循环"""
        while self.is_running and self.current_time > 0:
            self.current_time -= 1
            minutes = self.current_time // 60
            seconds = self.current_time % 60
            time_str = f"{minutes:02d}:{seconds:02d}"
            
            # 更新显示
            self.window.after(0, lambda: self.time_label.configure(text=time_str))
            time.sleep(1)
        
        if self.current_time <= 0:
            print("⏰ 计时结束！")
    
    def run(self):
        """运行窗口"""
        print("✓ 计时器窗口开始运行...")
        self.window.mainloop()

def main():
    """主函数"""
    print("=" * 50)
    print("FlyClock 测试版本 - 验证修复效果")
    print("=" * 50)
    
    try:
        # 创建并运行计时器窗口
        timer_window = SimpleTimerWindow()
        timer_window.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"✗ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
