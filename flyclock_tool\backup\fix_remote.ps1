# FlyClock远程控制修复脚本
Write-Host "FlyClock远程控制修复工具" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# 检查是否以管理员身份运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

Write-Host ""
Write-Host "1. 检查管理员权限..." -ForegroundColor Yellow
if ($isAdmin) {
    Write-Host "✓ 以管理员身份运行" -ForegroundColor Green
} else {
    Write-Host "✗ 需要管理员权限来配置防火墙" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "2. 检查防火墙规则..." -ForegroundColor Yellow
try {
    $rule = Get-NetFirewallRule -DisplayName "FlyClock-8080" -ErrorAction SilentlyContinue
    if ($rule) {
        Write-Host "✓ 防火墙规则已存在" -ForegroundColor Green
    } else {
        Write-Host "✗ 防火墙规则不存在" -ForegroundColor Red
        if ($isAdmin) {
            Write-Host "正在添加防火墙规则..." -ForegroundColor Yellow
            New-NetFirewallRule -DisplayName "FlyClock-8080" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
            Write-Host "✓ 防火墙规则添加成功" -ForegroundColor Green
        } else {
            Write-Host "请以管理员身份运行以下命令:" -ForegroundColor Yellow
            Write-Host "New-NetFirewallRule -DisplayName 'FlyClock-8080' -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "✗ 检查防火墙规则失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. 检查端口占用..." -ForegroundColor Yellow
$portCheck = netstat -an | Select-String ":8080.*LISTENING"
if ($portCheck) {
    Write-Host "✓ 端口8080正在监听" -ForegroundColor Green
} else {
    Write-Host "✗ 端口8080未监听" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. 获取网络信息..." -ForegroundColor Yellow
$localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"}).IPAddress
Write-Host "本机IP地址: $localIP" -ForegroundColor Cyan

Write-Host ""
Write-Host "5. 启动测试HTTP服务器..." -ForegroundColor Yellow
$webDir = Join-Path $PSScriptRoot "assets\web"
Write-Host "Web目录: $webDir" -ForegroundColor Cyan

if (Test-Path $webDir) {
    Write-Host "✓ Web目录存在" -ForegroundColor Green
    $files = Get-ChildItem $webDir
    Write-Host "Web文件: $($files.Name -join ', ')" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "启动HTTP服务器..." -ForegroundColor Yellow
    Write-Host "本地访问: http://localhost:8080/remote_client.html" -ForegroundColor Cyan
    Write-Host "局域网访问: http://$localIP:8080/remote_client.html" -ForegroundColor Cyan
    Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
    
    Set-Location $webDir
    & "d:\trae\.venv\Scripts\python.exe" -m http.server 8080 --bind 0.0.0.0
} else {
    Write-Host "✗ Web目录不存在: $webDir" -ForegroundColor Red
}
