# Development Files Archive

This directory contains files used during the development and setup process:

## 📁 Archived Files

- **`setup_consolidation_mdns.sh`** - Original manual startup script (superseded by systemd service)
- **`test_service.sh`** - Debug script for troubleshooting service startup issues
- **`STARTUP_SETUP_GUIDE.md`** - Original startup guide (superseded by COMPLETE_SETUP_GUIDE.md)

## 🔄 Superseded By

These files were used during development but are now superseded by:

- **Production Service**: `mcp-memory.service` + `service_control.sh`
- **Complete Documentation**: `COMPLETE_SETUP_GUIDE.md`
- **Quick Start**: `README_PRODUCTION.md`

## 🗂️ Purpose

These files are kept for:
- Historical reference
- Debugging if needed
- Understanding the development process
- Potential future troubleshooting

**Note**: Use the production files in the root directory for normal operation.