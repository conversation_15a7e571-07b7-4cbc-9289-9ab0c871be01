#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 修复验证脚本
验证全屏检测和文件变化逻辑的修复
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def main():
    """主测试函数"""
    print("🔧 ToolClock 修复验证")
    print("=" * 50)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.gui.timer_window import TimerWindow
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置测试文件
        print("📋 设置测试文件配置...")
        test_files = {
            '2025上半年工作总结-李国超.pptx': '00:08:00',
            '2025年上半年科技工作总结与展望-黎民浩.pptx': '00:00:30',
            'test_file.pptx': '00:05:00'
        }
        config_manager.set_setting('files.file_specific_timers', test_files)
        config_manager.set_setting('timer.auto_start', True)
        
        # 创建计时器和检测器
        print("🔧 创建计时器和检测器...")
        timer = TimerWindow()
        detector = WindowDetectorFixed(timer)
        
        print("✅ 初始化完成")
        print(f"📊 初始状态: 运行={timer.is_running}, 时间={timer.current_time}秒")
        
        # 测试1: 文件变化检测（应该不启动计时器）
        print("\n" + "=" * 50)
        print("📄 测试1: 文件变化检测")
        print("=" * 50)
        
        test_window_title = "PowerPoint - 2025上半年工作总结-李国超.pptx"
        print(f"模拟打开文件: {test_window_title}")
        
        # 模拟文件变化
        class MockHwnd:
            pass
        
        mock_hwnd = MockHwnd()
        
        import win32gui
        original_get_text = win32gui.GetWindowText
        win32gui.GetWindowText = lambda hwnd: test_window_title
        
        try:
            detector._check_file_change(mock_hwnd)
            
            print(f"📊 文件变化后状态:")
            print(f"  - 计时器运行: {timer.is_running}")
            print(f"  - 当前时间: {timer.current_time}秒")
            print(f"  - 最后检测文件: {detector.last_detected_file}")
            
            if not timer.is_running:
                print("✅ 正确：文件变化时计时器未自动启动")
            else:
                print("❌ 错误：文件变化时计时器不应自动启动")
            
        finally:
            win32gui.GetWindowText = original_get_text
        
        # 测试2: 全屏检测（应该启动计时器）
        print("\n" + "=" * 50)
        print("🖥️ 测试2: 全屏检测")
        print("=" * 50)
        
        print(f"模拟全屏进入: {test_window_title}")
        
        # 重置计时器状态
        if timer.is_running:
            timer.reset_timer()
        
        # 模拟全屏检测
        detector._on_fullscreen_enter(12345, test_window_title, "POWERPNT.EXE")
        
        # 等待一下让计时器启动
        time.sleep(1)
        
        print(f"📊 全屏检测后状态:")
        print(f"  - 计时器运行: {timer.is_running}")
        print(f"  - 当前时间: {timer.current_time}秒")
        print(f"  - 全屏窗口句柄: {detector.current_fullscreen_window}")
        
        if timer.is_running:
            print("✅ 正确：全屏时计时器自动启动")
        else:
            print("❌ 错误：全屏时计时器应该自动启动")
        
        # 测试3: 全屏退出（应该停止计时器）
        print("\n" + "=" * 50)
        print("🚪 测试3: 全屏退出")
        print("=" * 50)
        
        print("模拟全屏退出...")
        detector._on_fullscreen_exit()
        
        print(f"📊 全屏退出后状态:")
        print(f"  - 计时器运行: {timer.is_running}")
        print(f"  - 当前时间: {timer.current_time}秒")
        print(f"  - 全屏窗口句柄: {detector.current_fullscreen_window}")
        
        if not timer.is_running:
            print("✅ 正确：全屏退出时计时器停止")
        else:
            print("❌ 错误：全屏退出时计时器应该停止")
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 修复验证总结")
        print("=" * 50)
        
        print("🎯 修复内容:")
        print("1. ✅ 提高全屏检测阈值到95%，避免大窗口误判")
        print("2. ✅ 添加文件变化检测，只更新时间不启动计时器")
        print("3. ✅ 确保只有真正全屏时才启动倒计时")
        print("4. ✅ 修复方法名冲突问题")
        
        print("\n🔧 使用说明:")
        print("1. 打开PPT/PDF文件时，ToolClock会识别文件并准备对应的倒计时时间")
        print("2. 只有按F5或进入全屏放映时，才会开始倒计时")
        print("3. 切换文件时，会自动更新倒计时时间但不启动")
        print("4. 退出全屏时，倒计时会自动停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 修复验证完成！")
        print("现在ToolClock的逻辑是：")
        print("- 📄 打开文件 → 识别并准备时间（不启动）")
        print("- 🖥️ 全屏放映 → 开始倒计时")
        print("- 🚪 退出全屏 → 停止倒计时")
        print("- 🔄 切换文件 → 更新时间（不启动）")
    else:
        print("\n⚠️ 修复验证失败，需要进一步调试")
