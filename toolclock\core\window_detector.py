"""
窗口检测模块
检测PPT/PDF全屏状态并自动启动计时
"""

import sys
import os
import time
import threading
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.file_analyzer import analyze_presentation_file

try:
    import win32gui
    import win32process
    import win32api
    import win32con
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False
    print("警告: 未安装pywin32，PPT/PDF检测功能将不可用")

from core.config_manager import config_manager

logger = logging.getLogger(__name__)


class WindowDetector:
    """窗口检测器类"""
    
    def __init__(self, timer_window=None):
        """
        初始化窗口检测器
        
        Args:
            timer_window: 计时器窗口实例
        """
        self.timer_window = timer_window
        self.config_manager = config_manager
        self.is_running = False
        self.detection_thread = None
        self.current_fullscreen_window = None
        self.last_detected_file = None
        
        # PPT/PDF相关的窗口类名和进程名
        self.ppt_classes = [
            'PPTFrameClass',           # PowerPoint 2016+
            'PP12FrameClass',          # PowerPoint 2013
            'PP11FrameClass',          # PowerPoint 2010
            'PP10FrameClass',          # PowerPoint 2007
            'screenClass',             # PowerPoint 全屏模式
            'paneClassDC'              # PowerPoint 放映模式
        ]
        
        self.pdf_classes = [
            'AcrobatSDIWindow',        # Adobe Acrobat
            'AdobeAcrobat',           # Adobe Reader
            'FoxitReader',            # Foxit Reader
            'SumatraPDF',             # SumatraPDF
            'SUMATRA_PDF_FRAME'       # SumatraPDF Frame
        ]
        
        self.ppt_processes = [
            'POWERPNT.EXE',           # PowerPoint
            'PPTVIEW.EXE',            # PowerPoint Viewer
            'wpp.exe',                # WPS演示
            'et.exe'                  # WPS表格
        ]
        
        self.pdf_processes = [
            'AcroRd32.exe',           # Adobe Reader
            'Acrobat.exe',            # Adobe Acrobat
            'FoxitReader.exe',        # Foxit Reader
            'SumatraPDF.exe',         # SumatraPDF
            'PDFXCview.exe'           # PDF-XChange Viewer
        ]
        
        # 支持的文件扩展名
        self.supported_extensions = self.config_manager.get_setting(
            'files.ppt_pdf_extensions', 
            ['.ppt', '.pptx', '.pdf', '.pps', '.ppsx']
        )
    
    def start_detection(self):
        """开始窗口检测"""
        if not HAS_WIN32:
            logger.error("无法启动窗口检测：缺少win32gui模块")
            return False
        
        if self.is_running:
            return True
        
        self.is_running = True
        self.detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        self.detection_thread.start()
        logger.info("窗口检测已启动")
        return True
    
    def stop_detection(self):
        """停止窗口检测"""
        self.is_running = False
        if self.detection_thread:
            self.detection_thread.join(timeout=2)
        logger.info("窗口检测已停止")
    
    def _detection_loop(self):
        """检测循环"""
        while self.is_running:
            try:
                self._check_fullscreen_windows()
                time.sleep(1)  # 每秒检测一次
            except Exception as e:
                logger.error(f"窗口检测出错: {e}")
                time.sleep(2)  # 出错时延长检测间隔
    
    def _check_fullscreen_windows(self):
        """检查全屏窗口"""
        if not HAS_WIN32:
            return
        
        try:
            # 获取前台窗口
            foreground_window = win32gui.GetForegroundWindow()
            if not foreground_window:
                return
            
            # 检查是否为全屏窗口
            if not self._is_fullscreen_window(foreground_window):
                # 如果当前不是全屏，但之前有全屏窗口，则停止计时
                if self.current_fullscreen_window:
                    self._on_fullscreen_exit()
                return
            
            # 获取窗口信息
            window_class = win32gui.GetClassName(foreground_window)
            window_title = win32gui.GetWindowText(foreground_window)
            
            # 获取进程信息
            _, process_id = win32process.GetWindowThreadProcessId(foreground_window)
            process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, process_id)
            process_name = win32process.GetModuleFileNameEx(process_handle, 0)
            process_name = os.path.basename(process_name).upper()
            win32api.CloseHandle(process_handle)
            
            # 检查是否为PPT/PDF相关窗口
            if self._is_presentation_window(window_class, process_name, window_title):
                # 如果是新的全屏窗口，启动计时
                if foreground_window != self.current_fullscreen_window:
                    self._on_fullscreen_enter(foreground_window, window_title, process_name)
            else:
                # 不是演示窗口，如果之前有全屏窗口，则停止计时
                if self.current_fullscreen_window:
                    self._on_fullscreen_exit()
                    
        except Exception as e:
            logger.error(f"检查全屏窗口出错: {e}")
    
    def _is_fullscreen_window(self, hwnd):
        """判断窗口是否为全屏"""
        try:
            # 获取窗口矩形
            window_rect = win32gui.GetWindowRect(hwnd)

            # 验证返回值类型和格式
            if not isinstance(window_rect, (tuple, list)) or len(window_rect) != 4:
                logger.warning(f"GetWindowRect返回异常值: {window_rect}")
                return False

            # 确保所有坐标值都是数字类型 - 强化版本
            try:
                left, top, right, bottom = window_rect
                # 多重转换确保类型正确
                left = int(float(str(left).strip()))
                top = int(float(str(top).strip()))
                right = int(float(str(right).strip()))
                bottom = int(float(str(bottom).strip()))
            except (ValueError, TypeError) as e:
                logger.warning(f"窗口坐标类型转换失败: {window_rect}, 错误: {e}")
                print(f"窗口坐标类型转换失败: {window_rect}, 错误: {e}")
                return False

            # 计算窗口大小
            window_width = right - left
            window_height = bottom - top

            # 检查窗口大小是否有效
            if window_width <= 0 or window_height <= 0:
                logger.debug(f"窗口大小无效: {window_width}x{window_height}")
                return False

            # 获取屏幕分辨率
            screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
            screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

            # 确保屏幕分辨率是有效的数字 - 强化版本
            try:
                screen_width = int(float(str(screen_width).strip()))
                screen_height = int(float(str(screen_height).strip()))
            except (ValueError, TypeError):
                logger.warning(f"屏幕分辨率类型转换失败: {screen_width}x{screen_height}")
                print(f"屏幕分辨率类型转换失败: {screen_width}x{screen_height}")
                return False

            # 判断窗口是否占满屏幕（允许小幅偏差） - 强化版本
            try:
                width_diff = abs(int(window_width) - int(screen_width))
                height_diff = abs(int(window_height) - int(screen_height))
                left_pos = abs(int(left))
                top_pos = abs(int(top))

                width_match = width_diff <= 10
                height_match = height_diff <= 10
                position_match = left_pos <= 10 and top_pos <= 10
            except (ValueError, TypeError) as e:
                logger.error(f"比较操作失败: {e}")
                print(f"比较操作失败: {e}")
                return False

            # 调试信息
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"全屏检测: 窗口({window_width}x{window_height}) vs 屏幕({screen_width}x{screen_height}), "
                           f"位置({left},{top}), 匹配: 宽度={width_match}, 高度={height_match}, 位置={position_match}")

            return width_match and height_match and position_match

        except Exception as e:
            logger.error(f"判断全屏窗口出错: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def _is_presentation_window(self, window_class, process_name, window_title):
        """判断是否为演示相关窗口"""
        # 检查窗口类名
        for ppt_class in self.ppt_classes:
            if ppt_class.lower() in window_class.lower():
                return True
        
        for pdf_class in self.pdf_classes:
            if pdf_class.lower() in window_class.lower():
                return True
        
        # 检查进程名
        for ppt_process in self.ppt_processes:
            if ppt_process.upper() == process_name:
                return True
        
        for pdf_process in self.pdf_processes:
            if pdf_process.upper() == process_name:
                return True
        
        # 检查窗口标题中是否包含支持的文件扩展名
        window_title_lower = window_title.lower()
        for ext in self.supported_extensions:
            if ext.lower() in window_title_lower:
                return True
        
        # 检查是否包含演示相关关键词
        presentation_keywords = [
            'powerpoint', 'ppt', 'presentation', 'slideshow',
            'pdf', 'acrobat', 'reader', 'foxit', 'sumatra',
            '演示', '幻灯片', '放映'
        ]
        
        for keyword in presentation_keywords:
            if keyword in window_title_lower:
                return True
        
        return False
    
    def _on_fullscreen_enter(self, hwnd, window_title, process_name):
        """全屏进入事件"""
        self.current_fullscreen_window = hwnd

        logger.info(f"检测到全屏演示: {window_title} ({process_name})")
        print(f" 检测到全屏演示: {window_title}")

        # 尝试智能分析文件并设置计时时间
        suggested_duration = self._analyze_and_suggest_duration(window_title)

        # 获取文件特定的计时时长（用户手动设置的优先级更高）
        file_duration = self._get_file_specific_duration(window_title)

        # 选择使用的时长：用户设置 > 智能分析 > 默认时长
        final_duration = file_duration or suggested_duration

        if final_duration:
            duration_str = self._format_duration(final_duration)
            if file_duration:
                print(f" 使用文件特定时长: {duration_str}")
            else:
                print(f" 智能分析建议时长: {duration_str}")

            # 更新计时器时长
            if self.timer_window:
                self.timer_window.set_duration(final_duration)

        # 检查是否自动开始计时
        auto_start = self.config_manager.get_setting('timer.auto_start', False)
        if auto_start and self.timer_window:
            print(" 自动开始计时")
            self.timer_window.start_timer()

        self.last_detected_file = window_title
    
    def _on_fullscreen_exit(self):
        """全屏退出事件"""
        if self.current_fullscreen_window:
            logger.info("全屏演示已退出")
            print(" 全屏演示已退出")
            
            self.current_fullscreen_window = None
            
            # 根据配置决定是否停止计时
            auto_pause = self.config_manager.get_setting('control.auto_pause_on_exit', True)
            if auto_pause and self.timer_window:
                print(" 自动暂停计时")
                self.timer_window.pause_timer()
    
    def _analyze_and_suggest_duration(self, window_title):
        """智能分析文件并建议计时时长"""
        try:
            # 尝试从窗口标题中提取可能的文件路径
            possible_paths = self._extract_possible_file_paths(window_title)

            for file_path in possible_paths:
                if os.path.exists(file_path):
                    logger.info(f"正在分析文件: {file_path}")
                    analysis_result = analyze_presentation_file(file_path)

                    if analysis_result.get('analysis_success'):
                        suggested_time = analysis_result.get('suggested_time', 1800)
                        file_type = analysis_result.get('file_type', 'unknown')
                        page_count = analysis_result.get('page_count', 0)

                        logger.info(f"文件分析完成: {file_type}, {page_count}页/张, 建议{suggested_time}秒")

                        # 保存分析结果到配置中，供设置界面使用
                        self._save_file_analysis_result(file_path, analysis_result)

                        return suggested_time

            logger.info(f"无法找到对应的文件进行分析: {window_title}")
            return None

        except Exception as e:
            logger.error(f"智能分析文件失败: {e}")
            return None

    def _extract_possible_file_paths(self, window_title):
        """从窗口标题中提取可能的文件路径"""
        possible_paths = []

        # 常见的文档目录
        common_dirs = [
            os.path.expanduser("~/Desktop"),
            os.path.expanduser("~/Documents"),
            os.path.expanduser("~/Downloads"),
            "C:\\Users\\<USER>\\Documents",
            "D:\\Documents",
        ]

        # 从窗口标题中提取可能的文件名
        # 移除常见的应用程序名称
        title_clean = window_title
        for app_name in ["PowerPoint", "Adobe Acrobat", "PDF", "Microsoft", "演示文稿"]:
            title_clean = title_clean.replace(app_name, "").strip()

        # 移除常见的分隔符和后缀
        title_clean = title_clean.replace(" - ", "").replace("- ", "").strip()

        # 如果标题包含文件扩展名，直接使用
        if any(ext in title_clean.lower() for ext in ['.pptx', '.ppt', '.pdf']):
            filename = title_clean.strip()

            # 在常见目录中查找
            for directory in common_dirs:
                if os.path.exists(directory):
                    full_path = os.path.join(directory, filename)
                    possible_paths.append(full_path)

        # 如果没有扩展名，尝试添加常见扩展名
        else:
            base_name = title_clean.strip()
            if base_name:
                for ext in ['.pptx', '.ppt', '.pdf']:
                    filename = base_name + ext
                    for directory in common_dirs:
                        if os.path.exists(directory):
                            full_path = os.path.join(directory, filename)
                            possible_paths.append(full_path)

        return possible_paths

    def _save_file_analysis_result(self, file_path, analysis_result):
        """保存文件分析结果到配置"""
        try:
            # 获取现有的分析结果
            analysis_cache = self.config_manager.get_setting('files.analysis_cache', {})

            # 保存新的分析结果
            analysis_cache[file_path] = {
                'analysis_result': analysis_result,
                'analyzed_at': time.time()
            }

            # 限制缓存大小，只保留最近50个分析结果
            if len(analysis_cache) > 50:
                # 按时间排序，保留最新的50个
                sorted_items = sorted(analysis_cache.items(),
                                    key=lambda x: x[1].get('analyzed_at', 0),
                                    reverse=True)
                analysis_cache = dict(sorted_items[:50])

            # 保存到配置
            self.config_manager.set_setting('files.analysis_cache', analysis_cache)

        except Exception as e:
            logger.error(f"保存文件分析结果失败: {e}")

    def _format_duration(self, seconds):
        """格式化时长显示"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds == 0:
                return f"{minutes}分钟"
            else:
                return f"{minutes}分{remaining_seconds}秒"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes == 0:
                return f"{hours}小时"
            else:
                return f"{hours}小时{remaining_minutes}分钟"

    def _get_file_specific_duration(self, window_title):
        """获取文件特定的计时时长"""
        file_timers = self.config_manager.get_setting('files.file_specific_timers', {})

        # 尝试从窗口标题中提取文件名
        for file_path, duration in file_timers.items():
            filename = os.path.basename(file_path)
            if filename.lower() in window_title.lower():
                return duration

        return None
    
    def get_current_presentation_info(self):
        """获取当前演示信息"""
        if self.current_fullscreen_window and self.last_detected_file:
            return {
                'window_handle': self.current_fullscreen_window,
                'file_name': self.last_detected_file,
                'is_fullscreen': True
            }
        return None
    
    def force_detect_current_window(self):
        """强制检测当前窗口"""
        try:
            self._check_fullscreen_windows()
        except Exception as e:
            logger.error(f"强制检测失败: {e}")


# 全局窗口检测器实例
window_detector = None


def get_window_detector(timer_window=None):
    """获取窗口检测器实例"""
    global window_detector
    if window_detector is None:
        window_detector = WindowDetector(timer_window)
    return window_detector
