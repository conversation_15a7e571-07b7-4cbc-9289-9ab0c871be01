#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock - PPT/PDF 全屏放映计时辅助工具
最终版本主入口文件

功能特性：
- 自动检测PPT/PDF全屏状态
- 智能文件时长匹配
- 根据窗体自动增加带润文档后缀列表
- 悬浮计时器窗口
- 音频提醒功能
- 完整的配置管理

版本：V2.0.0 (最终整理版)
作者：黎民浩
日期：2025-07-30
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('toolclock_debug.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def main():
    """ToolClock主函数"""
    print("=" * 60)
    print("🔧 ToolClock - PPT/PDF 全屏放映计时辅助工具")
    print("版本：V2.0.0 (最终整理版)")
    print("=" * 60)
    
    logger.info("=" * 60)
    logger.info("🔧 ToolClock 启动中...")
    logger.info("=" * 60)
    
    try:
        logger.info("📦 导入核心模块...")
        
        # 导入GUI模块
        from gui.timer_window import TimerWindow
        logger.info("✓ TimerWindow 导入成功")
        
        # 导入窗口检测器（修复版）
        from window_detector_fixed import WindowDetectorFixed
        logger.info("✓ WindowDetectorFixed 导入成功")
        
        logger.info("🚀 创建计时器窗口...")
        
        # 创建计时器窗口
        timer_window = TimerWindow()
        logger.info("✓ 计时器窗口创建成功")
        
        logger.info("🔍 创建窗口检测器...")
        
        # 创建窗口检测器
        detector = WindowDetectorFixed(timer_window)
        logger.info("✓ 窗口检测器创建成功")
        
        logger.info("🎯 ToolClock已启动！")
        logger.info("📋 核心功能:")
        logger.info("  ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）")
        logger.info("  ✅ 按F5或全屏放映 → 自动开始倒计时")
        logger.info("  ✅ 退出全屏 → 自动停止倒计时")
        logger.info("  ✅ 切换文件 → 更新时间设置（不启动）")
        logger.info("  ✅ 右键调整 → 根据窗体自动增加带润文档后缀")
        logger.info("=" * 60)
        logger.info("🔍 实时监控开始:")
        logger.info("=" * 60)
        
        print("\n📋 使用说明:")
        print("  1. 打开PPT或PDF文件")
        print("  2. 按F5进入全屏模式 → 自动开始倒计时")
        print("  3. 退出全屏 → 自动停止倒计时")
        print("  4. 右键点击计时器 → 调整当前文件时长")
        print("  5. 可以拖拽移动计时器位置")
        print("  6. 程序会根据窗体自动管理文件时长列表")
        print("=" * 60)
        
        # 启动GUI主循环
        logger.info("▶️ 启动GUI主循环...")
        timer_window.run()
        
    except KeyboardInterrupt:
        logger.info("✓ ToolClock已停止")
        print("\n✓ ToolClock已停止")
    except Exception as e:
        logger.error(f"✗ ToolClock启动失败: {e}")
        print(f"✗ ToolClock启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()