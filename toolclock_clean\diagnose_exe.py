#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock exe功能诊断脚本
测试exe中的关键功能是否正常工作
"""

import sys
import os
import time
import threading
import subprocess
from pathlib import Path

def test_threading_in_exe():
    """测试exe中的线程功能"""
    print("=" * 60)
    print("测试exe中的线程功能")
    print("=" * 60)
    
    # 创建一个简单的线程测试脚本
    test_script = '''
import threading
import time

def test_thread():
    for i in range(5):
        print(f"线程运行中: {i}")
        time.sleep(1)
    print("线程结束")

print("启动线程测试...")
thread = threading.Thread(target=test_thread, daemon=True)
thread.start()
thread.join()
print("线程测试完成")
'''
    
    # 写入临时测试文件
    test_file = Path("thread_test.py")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        # 使用PyInstaller打包这个简单的线程测试
        print("打包线程测试...")
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'ThreadTest',
            '--hidden-import', 'threading',
            str(test_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("线程测试exe构建成功")
            
            # 运行测试exe
            test_exe = Path("dist/ThreadTest.exe")
            if test_exe.exists():
                print("运行线程测试exe...")
                result = subprocess.run([str(test_exe)], capture_output=True, text=True, timeout=30)
                print(f"退出代码: {result.returncode}")
                if result.stdout:
                    print(f"输出: {result.stdout}")
                if result.stderr:
                    print(f"错误: {result.stderr}")
            else:
                print("线程测试exe文件不存在")
        else:
            print(f"线程测试exe构建失败: {result.stderr}")
    
    except Exception as e:
        print(f"线程测试失败: {e}")
    
    finally:
        # 清理临时文件
        if test_file.exists():
            test_file.unlink()

def test_win32_in_exe():
    """测试exe中的win32功能"""
    print("\\n" + "=" * 60)
    print("测试exe中的win32功能")
    print("=" * 60)
    
    # 创建一个简单的win32测试脚本
    test_script = '''
try:
    import win32gui
    import win32api
    import win32process
    print("win32模块导入成功")
    
    # 测试基本功能
    foreground = win32gui.GetForegroundWindow()
    print(f"前台窗口句柄: {foreground}")
    
    if foreground:
        title = win32gui.GetWindowText(foreground)
        print(f"窗口标题: {title}")
    
    print("win32功能测试完成")
    
except ImportError as e:
    print(f"win32模块导入失败: {e}")
except Exception as e:
    print(f"win32功能测试失败: {e}")
'''
    
    # 写入临时测试文件
    test_file = Path("win32_test.py")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        # 使用PyInstaller打包这个简单的win32测试
        print("打包win32测试...")
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'Win32Test',
            '--hidden-import', 'win32gui',
            '--hidden-import', 'win32api',
            '--hidden-import', 'win32process',
            str(test_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("win32测试exe构建成功")
            
            # 运行测试exe
            test_exe = Path("dist/Win32Test.exe")
            if test_exe.exists():
                print("运行win32测试exe...")
                result = subprocess.run([str(test_exe)], capture_output=True, text=True, timeout=30)
                print(f"退出代码: {result.returncode}")
                if result.stdout:
                    print(f"输出: {result.stdout}")
                if result.stderr:
                    print(f"错误: {result.stderr}")
            else:
                print("win32测试exe文件不存在")
        else:
            print(f"win32测试exe构建失败: {result.stderr}")
    
    except Exception as e:
        print(f"win32测试失败: {e}")
    
    finally:
        # 清理临时文件
        if test_file.exists():
            test_file.unlink()

def analyze_toolclock_exe():
    """分析ToolClock exe的问题"""
    print("\\n" + "=" * 60)
    print("分析ToolClock exe问题")
    print("=" * 60)
    
    exe_path = Path("dist/ToolClock.exe")
    if not exe_path.exists():
        print("ToolClock.exe不存在")
        return
    
    print(f"exe文件大小: {exe_path.stat().st_size / (1024*1024):.2f} MB")
    
    # 检查是否能启动
    print("\\n测试exe启动...")
    try:
        # 启动exe并等待几秒
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待5秒
        time.sleep(5)
        
        if process.poll() is None:
            print("exe正在运行")
            # 终止进程
            process.terminate()
            process.wait(timeout=5)
        else:
            print("exe启动后立即退出")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"标准输出: {stdout.decode('utf-8', errors='ignore')}")
            if stderr:
                print(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
    
    except Exception as e:
        print(f"exe启动测试失败: {e}")

def main():
    """主函数"""
    print("ToolClock exe功能诊断")
    print("=" * 60)
    
    # 测试线程功能
    test_threading_in_exe()
    
    # 测试win32功能
    test_win32_in_exe()
    
    # 分析ToolClock exe
    analyze_toolclock_exe()
    
    print("\\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()