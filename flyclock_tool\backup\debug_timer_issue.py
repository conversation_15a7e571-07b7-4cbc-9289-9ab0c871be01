#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 计时器问题诊断脚本
诊断全屏放映时倒计时不运行的问题
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def test_timer_functionality():
    """测试计时器基本功能"""
    print("=" * 60)
    print("ToolClock 计时器功能诊断")
    print("=" * 60)
    
    try:
        # 导入计时器窗口
        from flyclock_tool.gui.timer_window import TimerWindow
        print("✓ 计时器窗口导入成功")
        
        # 创建计时器实例（不显示GUI）
        timer = TimerWindow()
        print("✓ 计时器实例创建成功")
        
        # 测试时间解析
        print("\n1. 测试时间解析功能...")
        test_durations = ["00:05:00", "10:30", "00:02:00"]
        for duration_str in test_durations:
            parsed = timer.parse_duration(duration_str)
            print(f"  {duration_str} -> {parsed}秒")
        
        # 测试set_duration方法
        print("\n2. 测试set_duration方法...")
        timer.set_duration("00:01:00")  # 设置1分钟
        print(f"  总时间: {timer.total_time}秒")
        print(f"  当前时间: {timer.current_time}秒")
        print(f"  计时模式: {timer.timer_mode}")
        
        # 测试计时器状态
        print("\n3. 测试计时器状态...")
        print(f"  是否运行: {timer.is_running}")
        print(f"  是否暂停: {timer.is_paused}")
        
        # 模拟启动计时器
        print("\n4. 模拟启动计时器...")
        if not timer.is_running:
            timer.is_running = True
            timer.is_paused = False
            print("  ✓ 计时器状态已设置为运行")
            
            # 模拟计时循环（只运行几秒）
            print("  开始模拟计时循环...")
            start_time = timer.current_time
            for i in range(5):  # 运行5秒
                if timer.is_running and not timer.is_paused:
                    timer.current_time -= 1
                    print(f"    第{i+1}秒: {timer.current_time}秒")
                time.sleep(1)
            
            timer.is_running = False
            print(f"  ✓ 计时循环完成，从{start_time}秒减少到{timer.current_time}秒")
        
        return True
        
    except Exception as e:
        print(f"✗ 计时器功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_window_detector_integration():
    """测试窗口检测器与计时器的集成"""
    print("\n" + "=" * 60)
    print("窗口检测器与计时器集成测试")
    print("=" * 60)
    
    try:
        # 导入修复版窗口检测器
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.gui.timer_window import TimerWindow
        
        # 创建计时器和检测器
        timer = TimerWindow()
        detector = WindowDetectorFixed(timer)
        
        print("✓ 窗口检测器和计时器创建成功")
        
        # 测试文件特定时间设置
        print("\n1. 测试文件特定时间设置...")
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置测试文件
        test_files = {
            'test_presentation.pptx': '00:05:00'
        }
        config_manager.set_setting('files.file_specific_timers', test_files)
        
        # 模拟全屏检测
        print("\n2. 模拟全屏检测...")
        window_title = "PowerPoint - test_presentation.pptx"
        
        # 检查文件特定时间获取
        file_duration = detector._get_file_specific_duration(window_title)
        print(f"  文件特定时间: {file_duration}")
        
        if file_duration:
            print(f"  ✓ 成功获取文件特定时间: {file_duration}")
            
            # 测试时间设置
            print("\n3. 测试时间设置到计时器...")
            original_time = timer.current_time
            timer.set_duration(file_duration)
            new_time = timer.current_time
            
            print(f"  原始时间: {original_time}秒")
            print(f"  新设置时间: {new_time}秒")
            
            if new_time != original_time:
                print("  ✓ 时间设置成功")
            else:
                print("  ✗ 时间设置失败")
        else:
            print("  ✗ 未能获取文件特定时间")
        
        # 模拟全屏进入事件
        print("\n4. 模拟全屏进入事件...")
        detector._on_fullscreen_enter(12345, window_title, "POWERPNT.EXE")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_threading_issues():
    """测试线程相关问题"""
    print("\n" + "=" * 60)
    print("线程问题诊断")
    print("=" * 60)
    
    try:
        from flyclock_tool.gui.timer_window import TimerWindow
        
        timer = TimerWindow()
        timer.set_duration("00:01:00")  # 1分钟
        
        print("1. 测试计时器线程启动...")
        
        # 检查是否有活跃的计时器线程
        active_threads = threading.active_count()
        print(f"  当前活跃线程数: {active_threads}")
        
        # 模拟启动计时器
        if not timer.is_running:
            timer.is_running = True
            timer.is_paused = False
            
            # 创建计时线程
            timer_thread = threading.Thread(target=timer.timer_loop, daemon=True)
            timer_thread.start()
            
            print("  ✓ 计时器线程已启动")
            
            # 等待几秒检查线程状态
            time.sleep(3)
            
            if timer_thread.is_alive():
                print("  ✓ 计时器线程正在运行")
                print(f"  当前时间: {timer.current_time}秒")
            else:
                print("  ✗ 计时器线程已停止")
            
            # 停止计时器
            timer.is_running = False
            timer_thread.join(timeout=2)
            
            if not timer_thread.is_alive():
                print("  ✓ 计时器线程已正常停止")
            else:
                print("  ⚠ 计时器线程未能正常停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 线程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("ToolClock 计时器问题诊断工具")
    print("请选择测试项目:")
    print("1. 基本计时器功能测试")
    print("2. 窗口检测器集成测试")
    print("3. 线程问题诊断")
    print("4. 全部测试")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    success = True
    
    if choice == "1":
        success = test_timer_functionality()
    elif choice == "2":
        success = test_window_detector_integration()
    elif choice == "3":
        success = test_threading_issues()
    elif choice == "4":
        success = (test_timer_functionality() and 
                  test_window_detector_integration() and 
                  test_threading_issues())
    else:
        print("无效选择")
        success = False
    
    if success:
        print("\n🎉 诊断完成！")
    else:
        print("\n❌ 发现问题，请检查输出信息")
