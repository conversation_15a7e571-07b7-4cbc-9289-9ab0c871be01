#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口控制模块
使用pyautogui库实现窗口激活和Ctrl+Tab键盘模拟功能
支持多线程操作，确保UI响应性
"""

import pyautogui
import time
import threading
import logging
import queue
from typing import Optional, Callable, Dict, Any
from concurrent.futures import ThreadPoolExecutor, Future

# 尝试导入Windows API
try:
    import win32gui
    import win32con
    import win32process
    import win32api
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WindowController:
    """窗口控制类 - 支持多线程操作"""
    
    def __init__(self, max_workers: int = 3):
        """
        初始化窗口控制器
        
        Args:
            max_workers: 线程池最大工作线程数
        """
        # 配置pyautogui
        pyautogui.FAILSAFE = True  # 启用安全机制
        pyautogui.PAUSE = 0.1      # 操作间隔
        
        # 线程池用于异步操作
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 操作队列和结果缓存
        self.operation_queue = queue.Queue()
        self.results_cache = {}
        self._cache_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'ctrl_tab_sent': 0,
            'windows_activated': 0,
            'operations_failed': 0,
            'operations_queued': 0
        }
        self._stats_lock = threading.Lock()
        
        # 启动后台处理线程
        self.is_running = True
        self.background_thread = threading.Thread(
            target=self._background_processor, 
            daemon=True
        )
        self.background_thread.start()
        
        logger.info("WindowController 初始化完成，多线程支持已启用")    
    def send_ctrl_tab(self, async_mode: bool = False) -> bool:
        """
        发送Ctrl+Tab组合键
        
        Args:
            async_mode: 是否异步执行
            
        Returns:
            bool: 操作是否成功
        """
        if async_mode:
            # 异步执行
            future = self.executor.submit(self._send_ctrl_tab_sync)
            return True  # 立即返回，实际结果通过Future获取
        else:
            # 同步执行
            return self._send_ctrl_tab_sync()
    
    def _send_ctrl_tab_sync(self) -> bool:
        """
        同步发送Ctrl+Tab组合键（内部方法）
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logger.info("发送Ctrl+Tab组合键")
            
            # 使用pyautogui发送组合键
            pyautogui.hotkey('ctrl', 'tab')
            
            # 更新统计信息
            with self._stats_lock:
                self.stats['ctrl_tab_sent'] += 1
            
            logger.info("Ctrl+Tab发送成功")
            return True
            
        except pyautogui.FailSafeException:
            logger.warning("操作被pyautogui安全机制阻止")
            with self._stats_lock:
                self.stats['operations_failed'] += 1
            return False
            
        except Exception as e:
            logger.error(f"发送Ctrl+Tab失败: {e}")
            with self._stats_lock:
                self.stats['operations_failed'] += 1
            return False
    
    def send_ctrl_tab_async(self, callback: Optional[Callable] = None) -> Future:
        """
        异步发送Ctrl+Tab组合键
        
        Args:
            callback: 完成后的回调函数
            
        Returns:
            Future: 异步操作的Future对象
        """
        def task():
            result = self._send_ctrl_tab_sync()
            if callback:
                try:
                    callback(result)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {e}")
            return result
        
        return self.executor.submit(task)    
    def activate_window_by_pid(self, pid: int, async_mode: bool = False) -> bool:
        """
        根据PID激活窗口
        
        Args:
            pid: 进程ID
            async_mode: 是否异步执行
            
        Returns:
            bool: 操作是否成功
        """
        if async_mode:
            # 异步执行
            future = self.executor.submit(self._activate_window_by_pid_sync, pid)
            return True  # 立即返回，实际结果通过Future获取
        else:
            # 同步执行
            return self._activate_window_by_pid_sync(pid)
    
    def _activate_window_by_pid_sync(self, pid: int) -> bool:
        """
        同步激活窗口（内部方法）
        
        Args:
            pid: 进程ID
            
        Returns:
            bool: 操作是否成功
        """
        if not HAS_WIN32:
            logger.warning("缺少win32gui库，无法激活窗口")
            return False
        
        try:
            logger.info(f"尝试激活PID {pid} 的窗口")
            
            # 查找进程的主窗口
            hwnd = self._find_main_window_by_pid(pid)
            if not hwnd:
                logger.warning(f"未找到PID {pid} 的主窗口")
                return False
            
            # 激活窗口
            if self._activate_window(hwnd):
                with self._stats_lock:
                    self.stats['windows_activated'] += 1
                logger.info(f"成功激活PID {pid} 的窗口")
                return True
            else:
                logger.warning(f"激活PID {pid} 的窗口失败")
                return False
                
        except Exception as e:
            logger.error(f"激活窗口时出错: {e}")
            with self._stats_lock:
                self.stats['operations_failed'] += 1
            return False
    
    def _find_main_window_by_pid(self, pid: int) -> Optional[int]:
        """
        根据PID查找主窗口句柄
        
        Args:
            pid: 进程ID
            
        Returns:
            Optional[int]: 窗口句柄，未找到返回None
        """
        if not HAS_WIN32:
            return None
        
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                if window_pid == pid:
                    windows.append(hwnd)
            return True
        
        windows = []
        try:
            win32gui.EnumWindows(enum_windows_callback, windows)
            # 返回第一个找到的窗口（通常是主窗口）
            return windows[0] if windows else None
        except Exception as e:
            logger.error(f"枚举窗口时出错: {e}")
            return None    
    def _activate_window(self, hwnd: int) -> bool:
        """
        激活指定的窗口
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否成功激活
        """
        if not HAS_WIN32:
            return False
        
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(hwnd):
                # 恢复最小化的窗口
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(hwnd)
            
            # 确保窗口可见
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            
            return True
            
        except Exception as e:
            logger.error(f"激活窗口失败: {e}")
            return False
    
    def batch_send_ctrl_tab(self, count: int, interval: float = 0.5, 
                           callback: Optional[Callable] = None) -> Future:
        """
        批量发送Ctrl+Tab（多线程）
        
        Args:
            count: 发送次数
            interval: 发送间隔（秒）
            callback: 完成后的回调函数
            
        Returns:
            Future: 异步操作的Future对象
        """
        def batch_task():
            results = []
            for i in range(count):
                result = self._send_ctrl_tab_sync()
                results.append(result)
                
                if i < count - 1:  # 最后一次不需要等待
                    time.sleep(interval)
            
            success_count = sum(results)
            logger.info(f"批量发送完成: {success_count}/{count} 成功")
            
            if callback:
                try:
                    callback(success_count, count)
                except Exception as e:
                    logger.error(f"批量操作回调函数执行失败: {e}")
            
            return success_count
        
        return self.executor.submit(batch_task)    
    def queue_operation(self, operation: str, **kwargs) -> str:
        """
        将操作加入队列
        
        Args:
            operation: 操作类型 ('ctrl_tab', 'activate_window')
            **kwargs: 操作参数
            
        Returns:
            str: 操作ID
        """
        operation_id = f"{operation}_{int(time.time() * 1000)}"
        
        operation_data = {
            'id': operation_id,
            'type': operation,
            'params': kwargs,
            'timestamp': time.time()
        }
        
        self.operation_queue.put(operation_data)
        
        with self._stats_lock:
            self.stats['operations_queued'] += 1
        
        logger.info(f"操作已加入队列: {operation_id}")
        return operation_id
    
    def _background_processor(self):
        """后台处理线程（内部方法）"""
        logger.info("后台处理线程已启动")
        
        while self.is_running:
            try:
                # 从队列获取操作（超时1秒）
                operation = self.operation_queue.get(timeout=1.0)
                
                # 执行操作
                result = self._execute_queued_operation(operation)
                
                # 缓存结果
                with self._cache_lock:
                    self.results_cache[operation['id']] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                
                # 清理过期缓存（保留最近100个结果）
                self._cleanup_cache()
                
                self.operation_queue.task_done()
                
            except queue.Empty:
                # 队列为空，继续循环
                continue
            except Exception as e:
                logger.error(f"后台处理出错: {e}")
        
        logger.info("后台处理线程已停止")
    
    def _execute_queued_operation(self, operation: Dict[str, Any]) -> bool:
        """
        执行队列中的操作
        
        Args:
            operation: 操作数据
            
        Returns:
            bool: 操作是否成功
        """
        op_type = operation['type']
        params = operation['params']
        
        try:
            if op_type == 'ctrl_tab':
                return self._send_ctrl_tab_sync()
            elif op_type == 'activate_window':
                pid = params.get('pid')
                if pid:
                    return self._activate_window_by_pid_sync(pid)
                else:
                    logger.error("activate_window操作缺少pid参数")
                    return False
            else:
                logger.error(f"未知的操作类型: {op_type}")
                return False
                
        except Exception as e:
            logger.error(f"执行队列操作失败: {e}")
            return False    
    def _cleanup_cache(self):
        """清理过期的结果缓存"""
        if len(self.results_cache) > 100:
            # 按时间戳排序，保留最新的100个
            sorted_items = sorted(
                self.results_cache.items(),
                key=lambda x: x[1]['timestamp'],
                reverse=True
            )
            
            # 保留前100个
            self.results_cache = dict(sorted_items[:100])
    
    def get_operation_result(self, operation_id: str) -> Optional[bool]:
        """
        获取操作结果
        
        Args:
            operation_id: 操作ID
            
        Returns:
            Optional[bool]: 操作结果，未找到返回None
        """
        with self._cache_lock:
            result_data = self.results_cache.get(operation_id)
            return result_data['result'] if result_data else None
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取统计信息
        
        Returns:
            Dict[str, int]: 统计信息字典
        """
        with self._stats_lock:
            return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        with self._stats_lock:
            self.stats = {
                'ctrl_tab_sent': 0,
                'windows_activated': 0,
                'operations_failed': 0,
                'operations_queued': 0
            }
        logger.info("统计信息已重置")
    
    def shutdown(self):
        """关闭窗口控制器"""
        logger.info("正在关闭WindowController...")
        
        # 停止后台线程
        self.is_running = False
        if self.background_thread.is_alive():
            self.background_thread.join(timeout=5)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("WindowController已关闭")
    
    def __del__(self):
        """析构函数"""
        self.shutdown()


# 全局窗口控制器实例
_window_controller = None


def get_window_controller(max_workers: int = 3) -> WindowController:
    """
    获取窗口控制器实例（单例模式）
    
    Args:
        max_workers: 线程池最大工作线程数
        
    Returns:
        WindowController: 窗口控制器实例
    """
    global _window_controller
    if _window_controller is None:
        _window_controller = WindowController(max_workers)
    return _window_controller