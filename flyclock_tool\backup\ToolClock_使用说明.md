# ToolClock - PPT/PDF 全屏放映计时辅助工具

## 版本信息
- **版本**: V1.0.0 (最终版)
- **开发者**: 黎民浩
- **单位**: 珠海分行智能科技部

## 功能特点

### ✅ 已修复的问题
1. **删除文件功能** - 现在可以正常删除设置中的文件项目
2. **PPT全屏自动检测** - 优化了全屏检测算法，提高识别准确性
3. **倒计时时间更新** - 修复了设置倒计时时间后程序不更新的问题
4. **按钮菜单功能** - 所有右键菜单功能都已完善

### 🚀 核心功能
- **自动全屏检测**: 自动识别PPT/PDF全屏状态并启动计时
- **双计时模式**: 支持倒计时和正计时两种模式
- **文件特定设置**: 可为不同文件设置专属计时时长
- **音频提醒**: 支持警告音和结束音提醒
- **悬浮显示**: 计时器悬浮在屏幕上方，不影响演示

## 使用方法

### 1. 启动程序
- 双击 `ToolClock.exe` 启动程序
- 程序会显示一个悬浮的计时器窗口

### 2. 基本操作
- **右键菜单**: 在计时器窗口上右键可打开功能菜单
- **拖拽移动**: 按住计时器窗口可拖拽到任意位置
- **双击设置**: 双击计时器窗口可快速打开设置

### 3. 设置功能

#### 时间设置
- **默认计时时长**: 设置默认的计时时间（格式：HH:MM:SS）
- **计时模式**: 选择倒计时或正计时
- **自动开始计时**: 检测到全屏时自动开始计时
- **提前警告时间**: 设置提前多少秒开始警告

#### 文件特定设置
- **新增文件**: 为特定PPT/PDF文件设置专属计时时长
- **修改时长**: 修改已设置文件的计时时长
- **删除文件**: 删除不需要的文件设置（已修复）
- **智能分析**: 自动分析PPT页数并建议计时时长

#### 显示设置
- **颜色配置**: 自定义正常和警告状态的颜色
- **透明度**: 调整计时器窗口的透明度
- **显示控制**: 选择是否显示控制按钮
- **全屏显示**: 设置全屏时的显示行为

#### 音频设置
- **启用音频**: 开启/关闭音频提醒功能
- **警告音**: 设置警告时播放的音频文件
- **结束音**: 设置计时结束时播放的音频文件

### 4. 快捷键
- **暂停/恢复**: 空格键
- **重置计时**: R键

## 故障排除

### 问题1: 删除文件不生效
**解决方案**: 已修复，现在删除文件后会自动保存配置

### 问题2: PPT全屏不自动计时
**解决方案**: 
1. 检查"自动开始计时"选项是否开启
2. 确保PPT处于真正的全屏模式（F5键）
3. 已优化全屏检测算法，提高识别准确性

### 问题3: 设置倒计时时间不更新
**解决方案**: 已修复，现在修改设置后会立即更新计时器显示

### 问题4: 程序启动失败
**解决方案**:
1. 确保系统已安装必要的运行库
2. 以管理员权限运行程序
3. 检查防病毒软件是否误报

## 技术支持

如有问题或建议，请联系：
- **开发者**: 黎民浩
- **部门**: 珠海分行智能科技部

## 更新日志

### V1.0.0 (最终版)
- ✅ 修复删除文件功能
- ✅ 优化PPT全屏检测算法
- ✅ 修复倒计时时间更新问题
- ✅ 完善所有按钮菜单功能
- ✅ 提高程序稳定性和容错性
