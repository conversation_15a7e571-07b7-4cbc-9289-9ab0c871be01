#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 红色背景持续蜂鸣版本打包脚本
生成单个exe文件，包含所有依赖
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理目录: {dir_name}")

def create_red_spec_file():
    """创建红色版本的PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run_red_version.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('timer_window_red.py', '.'),
        ('window_detector_fixed.py', '.'),
        ('../gui', 'gui'),
        ('../core', 'core'),
        ('../assets', 'assets'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.simpledialog',
        'win32gui',
        'win32api',
        'win32con',
        'winsound',
        'pygame',
        'pygame.mixer',
        'json',
        'threading',
        'time',
        'os',
        'sys',
        'pathlib',
        'logging',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ToolClock_V1.1.0_RedBeep',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open('ToolClock_RedBeep.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 已创建红色版本spec文件")

def build_red_exe():
    """构建红色版本exe文件"""
    try:
        print("🔴 开始构建红色背景持续蜂鸣版本exe...")
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--onefile',
            '--noconsole',
            'ToolClock_RedBeep.spec'
        ], check=True, capture_output=True, text=True)
        
        print("✅ 红色版本exe构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔴 ToolClock V1.1.0 红色背景持续蜂鸣版本打包工具")
    print("=" * 60)
    
    # 检查依赖
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 请先安装PyInstaller: pip install pyinstaller")
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建spec文件
    create_red_spec_file()
    
    # 构建exe
    if build_red_exe():
        print("\n" + "=" * 60)
        print("🔴 红色版本构建完成！")
        print("📁 输出文件: dist/ToolClock_V1.1.0_RedBeep.exe")
        print("🔴 特性: 红色背景 + 持续蜂鸣音")
        print("=" * 60)
    else:
        print("\n❌ 构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
