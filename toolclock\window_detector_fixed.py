"""
窗口检测模块 - 修复版本
检测PPT/PDF全屏状态并自动启动计时
"""

import sys
import os
import time
import threading
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.file_analyzer import analyze_presentation_file

try:
    import win32gui
    import win32process
    import win32api
    import win32con
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False
    print("警告: 未安装pywin32，PPT/PDF检测功能将不可用")

from core.config_manager import config_manager

logger = logging.getLogger(__name__)


class WindowDetectorFixed:
    """窗口检测器类 - 修复版本"""
    
    def __init__(self, timer_window=None):
        """
        初始化窗口检测器
        
        Args:
            timer_window: 计时器窗口实例
        """
        self.timer_window = timer_window
        self.config_manager = config_manager
        self.is_running = False
        self.detection_thread = None
        self.current_fullscreen_window = None
        self.last_detected_file = None
        
        # PPT/PDF相关的窗口类名和进程名
        self.ppt_classes = [
            'PPTFrameClass',           # PowerPoint 2016+
            'PP12FrameClass',          # PowerPoint 2013
            'PP11FrameClass',          # PowerPoint 2010
            'PP10FrameClass',          # PowerPoint 2007
            'screenClass',             # PowerPoint 全屏模式
            'paneClassDC'              # PowerPoint 放映模式
        ]
        
        self.pdf_classes = [
            'AcrobatSDIWindow',        # Adobe Acrobat
            'AdobeAcrobat',           # Adobe Reader
            'FoxitReader',            # Foxit Reader
            'SumatraPDF',             # SumatraPDF
            'SUMATRA_PDF_FRAME'       # SumatraPDF Frame
        ]
        
        self.ppt_processes = [
            'POWERPNT.EXE',           # PowerPoint
            'PPTVIEW.EXE',            # PowerPoint Viewer
            'wpp.exe',                # WPS演示
            'et.exe'                  # WPS表格
        ]
        
        self.pdf_processes = [
            'AcroRd32.exe',           # Adobe Reader
            'Acrobat.exe',            # Adobe Acrobat
            'FoxitReader.exe',        # Foxit Reader
            'SumatraPDF.exe',         # SumatraPDF
            'PDFXCview.exe'           # PDF-XChange Viewer
        ]
        
        # 支持的文件扩展名
        self.supported_extensions = self.config_manager.get_setting(
            'files.ppt_pdf_extensions', 
            ['.ppt', '.pptx', '.pdf', '.pps', '.ppsx']
        )
    
    def start_detection(self):
        """开始窗口检测"""
        if not HAS_WIN32:
            logger.error("无法启动窗口检测：缺少win32gui模块")
            return False
        
        if self.is_running:
            return True
        
        self.is_running = True
        self.detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        self.detection_thread.start()
        logger.info("窗口检测已启动")
        return True
    
    def stop_detection(self):
        """停止窗口检测"""
        self.is_running = False
        if self.detection_thread:
            self.detection_thread.join(timeout=2)
        logger.info("窗口检测已停止")
    
    def _detection_loop(self):
        """检测循环"""
        while self.is_running:
            try:
                self._check_fullscreen_windows()
                time.sleep(1)  # 每秒检测一次
            except Exception as e:
                logger.error(f"窗口检测出错: {e}")
                print(f"窗口检测出错: {e}")
                import traceback
                traceback.print_exc()
                time.sleep(2)  # 出错时延长检测间隔
    
    def _check_fullscreen_windows(self):
        """检查全屏窗口"""
        if not HAS_WIN32:
            return
        
        try:
            # 获取前台窗口
            foreground_window = win32gui.GetForegroundWindow()
            if not foreground_window:
                return
            
            # 检查是否为全屏窗口
            if not self._is_fullscreen_window(foreground_window):
                # 如果当前不是全屏，但之前有全屏窗口，则停止计时
                if self.current_fullscreen_window:
                    self._on_fullscreen_exit()
                else:
                    # 检查是否有文件变化（非全屏状态下）
                    self._check_file_change(foreground_window)
                return
            
            # 获取窗口信息
            window_class = win32gui.GetClassName(foreground_window)
            window_title = win32gui.GetWindowText(foreground_window)
            
            # 获取进程信息
            _, process_id = win32process.GetWindowThreadProcessId(foreground_window)
            process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, process_id)
            process_name = win32process.GetModuleFileNameEx(process_handle, 0)
            process_name = os.path.basename(process_name).upper()
            win32api.CloseHandle(process_handle)
            
            # 检查是否为PPT/PDF相关窗口
            if self._is_presentation_window(window_class, process_name, window_title):
                # 如果是新的全屏窗口，启动计时
                if foreground_window != self.current_fullscreen_window:
                    self._on_fullscreen_enter(foreground_window, window_title, process_name)
            else:
                # 不是演示窗口，如果之前有全屏窗口，则停止计时
                if self.current_fullscreen_window:
                    self._on_fullscreen_exit()
                    
        except Exception as e:
            logger.error(f"检查全屏窗口出错: {e}")
            print(f"检查全屏窗口出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _is_fullscreen_window(self, hwnd):
        """判断窗口是否为全屏 - 修复版本"""
        try:
            # 获取窗口矩形
            window_rect = win32gui.GetWindowRect(hwnd)

            # 验证返回值类型和格式
            if not isinstance(window_rect, (tuple, list)) or len(window_rect) != 4:
                logger.warning(f"GetWindowRect返回异常值: {window_rect}")
                return False

            # 确保所有坐标值都是数字类型 - 强化版本
            try:
                left, top, right, bottom = window_rect
                # 多重转换确保类型正确
                left = int(float(str(left).strip()))
                top = int(float(str(top).strip()))
                right = int(float(str(right).strip()))
                bottom = int(float(str(bottom).strip()))
            except (ValueError, TypeError) as e:
                logger.warning(f"窗口坐标类型转换失败: {window_rect}, 错误: {e}")
                print(f"窗口坐标类型转换失败: {window_rect}, 错误: {e}")
                return False

            # 计算窗口大小
            window_width = right - left
            window_height = bottom - top

            # 检查窗口大小是否有效
            if window_width <= 0 or window_height <= 0:
                logger.debug(f"窗口大小无效: {window_width}x{window_height}")
                return False

            # 获取屏幕分辨率
            screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
            screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

            # 确保屏幕分辨率是有效的数字 - 强化版本
            try:
                screen_width = int(float(str(screen_width).strip()))
                screen_height = int(float(str(screen_height).strip()))
            except (ValueError, TypeError):
                logger.warning(f"屏幕分辨率类型转换失败: {screen_width}x{screen_height}")
                print(f"屏幕分辨率类型转换失败: {screen_width}x{screen_height}")
                return False

            # 判断窗口是否占满屏幕（允许小幅偏差） - 改进版
            try:
                width_diff = abs(int(window_width) - int(screen_width))
                height_diff = abs(int(window_height) - int(screen_height))
                left_pos = abs(int(left))
                top_pos = abs(int(top))

                # 极严格的全屏检测：只有真正的全屏才通过
                width_match = width_diff <= 3  # 减少到3像素容差
                height_match = height_diff <= 3  # 减少到3像素容差
                position_match = left_pos <= 3 and top_pos <= 3  # 减少到3像素容差

                # 大小比例检查：必须占屏幕99%以上
                width_ratio = window_width / screen_width if screen_width > 0 else 0
                height_ratio = window_height / screen_height if screen_height > 0 else 0
                size_match = width_ratio >= 0.99 and height_ratio >= 0.99  # 提高到99%

                # 极严格的全屏判断：必须同时满足大小和位置条件
                is_fullscreen = (width_match and height_match and position_match) or \
                               (size_match and left_pos <= 2 and top_pos <= 2)  # 位置极严格

            except (ValueError, TypeError) as e:
                logger.error(f"比较操作失败: {e}")
                print(f"比较操作失败: {e}")
                return False

            # 调试信息
            print(f"全屏检测: 窗口({window_width}x{window_height}) vs 屏幕({screen_width}x{screen_height}), "
                  f"位置({left},{top}), 匹配: 宽度={width_match}, 高度={height_match}, 位置={position_match}, "
                  f"大小比例={width_ratio:.2f}x{height_ratio:.2f}, 结果={is_fullscreen}")

            return is_fullscreen

        except Exception as e:
            logger.error(f"判断全屏窗口出错: {e}")
            print(f"判断全屏窗口出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _is_presentation_window(self, window_class, process_name, window_title):
        """判断是否为演示相关窗口"""
        # 检查窗口类名
        for ppt_class in self.ppt_classes:
            if ppt_class.lower() in window_class.lower():
                return True
        
        for pdf_class in self.pdf_classes:
            if pdf_class.lower() in window_class.lower():
                return True
        
        # 检查进程名
        for ppt_process in self.ppt_processes:
            if ppt_process.upper() == process_name:
                return True
        
        for pdf_process in self.pdf_processes:
            if pdf_process.upper() == process_name:
                return True
        
        # 检查窗口标题中是否包含支持的文件扩展名
        window_title_lower = window_title.lower()
        for ext in self.supported_extensions:
            if ext.lower() in window_title_lower:
                return True
        
        # 检查是否包含演示相关关键词
        presentation_keywords = [
            'powerpoint', 'ppt', 'presentation', 'slideshow',
            'pdf', 'acrobat', 'reader', 'foxit', 'sumatra',
            '演示', '幻灯片', '放映'
        ]
        
        for keyword in presentation_keywords:
            if keyword in window_title_lower:
                return True
        
        return False
    
    def _on_fullscreen_enter(self, hwnd, window_title, process_name):
        """全屏进入事件"""
        self.current_fullscreen_window = hwnd

        logger.info(f"检测到全屏演示: {window_title} ({process_name})")
        print(f" 检测到全屏演示: {window_title}")

        # 获取文件特定的计时时长（用户手动设置的优先级最高）
        print(f" 开始查找文件特定计时时长...")
        file_duration = self._get_file_specific_duration(window_title)

        # 尝试智能分析文件并设置计时时间（作为备选方案）
        suggested_duration = None
        if not file_duration:
            print(f"🤖 尝试智能分析文件...")
            suggested_duration = self._analyze_and_suggest_duration(window_title)

        # 选择使用的时长：用户设置 > 智能分析 > 默认时长
        final_duration = file_duration or suggested_duration
        duration_source = ""

        if final_duration:
            # 根据时长类型判断是字符串还是数字
            if isinstance(final_duration, str):
                duration_str = final_duration
                # 验证时长格式
                try:
                    parts = duration_str.split(':')
                    if len(parts) == 3:  # HH:MM:SS格式
                        hours, minutes, seconds = map(int, parts)
                        duration_seconds = hours * 3600 + minutes * 60 + seconds
                    elif len(parts) == 2:  # MM:SS格式
                        minutes, seconds = map(int, parts)
                        duration_seconds = minutes * 60 + seconds
                    else:
                        duration_seconds = int(final_duration)
                except Exception as e:
                    print(f" 时长格式解析失败: {e}，使用默认时长")
                    duration_seconds = 1800  # 默认30分钟
                    duration_str = "00:30:00"
            else:
                duration_seconds = final_duration
                duration_str = self._format_duration(duration_seconds)

            if file_duration:
                duration_source = "文件特定设置"
                print(f" 使用文件特定时长: {duration_str} ({duration_seconds}秒)")
            else:
                duration_source = "智能分析建议"
                print(f"🤖 使用智能分析建议时长: {duration_str} ({duration_seconds}秒)")

            # 更新计时器时长（但要保护timer_finished_confirmed状态）
            if self.timer_window:
                print(f" 正在设置计时器时长...")
                # 保存timer_finished_confirmed状态
                was_timer_finished = self.timer_window.timer_finished_confirmed
                print(f" 保存timer_finished_confirmed状态: {was_timer_finished}")

                self.timer_window.set_duration(duration_str)

                # 如果之前已确认计时结束，恢复该状态
                if was_timer_finished:
                    self.timer_window.timer_finished_confirmed = True
                    print(f" 恢复timer_finished_confirmed状态: True")

                print(f" 计时器时长设置完成")
            else:
                print(f" 计时器窗口不存在，无法设置时长")
        else:
            duration_source = "默认设置"
            print(f"⚙ 使用默认计时时长")

        # 检查是否自动开始计时
        auto_start = self.config_manager.get_setting('timer.auto_start', True)
        print(f"⚙ 自动启动设置: {auto_start}")

        if auto_start and self.timer_window:
            print(f" 准备自动启动计时器...")

            # 检查计时器当前状态
            print(f"📊 计时器状态检查:")
            print(f"   - 是否运行: {self.timer_window.is_running}")
            print(f"   - 是否暂停: {self.timer_window.is_paused}")
            print(f"   - 当前时间: {self.timer_window.current_time}秒")
            print(f"   - 总时间: {self.timer_window.total_time}秒")

            # 检查是否需要重置计时器
            should_reset = False

            if self.timer_window.timer_finished_confirmed:
                print(f" 用户已确认计时结束，不重置计时器，保持负数状态...")
                should_reset = False
            elif self.timer_window.is_running:
                print(f"⏹ 计时器已在运行，需要重置...")
                should_reset = True
            elif self.timer_window.current_time <= 0:
                print(f"🔄 计时器已结束但未确认，重置为新的倒计时...")
                should_reset = True

            if should_reset:
                self.timer_window.reset_timer()

            # 添加调试信息
            print(f" 检查timer_finished_confirmed标志: {self.timer_window.timer_finished_confirmed}")
            print(f" 当前时间: {self.timer_window.current_time}秒")
            logger.info(f"DEBUG: timer_finished_confirmed={self.timer_window.timer_finished_confirmed}, current_time={self.timer_window.current_time}")

            # 只有在未确认计时结束的情况下才启动计时器
            if not self.timer_window.timer_finished_confirmed:
                try:
                    print(f" 启动计时器...")
                    self.timer_window.start_timer()
                    print(f" 计时器启动成功")

                    # 验证启动状态
                    if self.timer_window.is_running:
                        print(f" 计时器状态验证: 正在运行")
                    else:
                        print(f" 计时器状态验证: 未运行")
                except Exception as e:
                    print(f" 启动计时器失败: {e}")
            else:
                print(f" 用户已确认计时结束，设置负数时间从-00:00开始")
                # 设置时间为0，这样显示逻辑会显示-00:00
                self.timer_window.current_time = 0
                # 如果计时器没有运行，启动它以显示负数时间
                if not self.timer_window.is_running:
                    try:
                        self.timer_window.start_timer()
                        print(f" 负数计时器启动成功")
                    except Exception as e:
                        print(f" 启动负数计时器失败: {e}")
                # 强制更新显示为红色背景和负数时间
                self.timer_window.update_display()
                print(f" 已强制更新显示为红色背景和负数时间，从-00:00开始")
        else:
            if not auto_start:
                print(f"⚙ 自动启动已禁用")
            if not self.timer_window:
                print(f" 计时器窗口不存在")

        self.last_detected_file = window_title
        print(f"📝 已记录检测到的文件: {window_title}")
        print(f" 全屏检测处理完成，时长来源: {duration_source}")
        print("=" * 60)

    def _check_file_change(self, hwnd):
        """检查文件变化（非全屏状态下）"""
        try:
            # 获取窗口标题
            window_title = win32gui.GetWindowText(hwnd)
            if not window_title:
                return

            # 检查是否为PPT/PDF相关窗口
            if not self._is_presentation_file(window_title):
                return

            # 如果文件发生变化，更新时间设置但不启动计时器
            if window_title != self.last_detected_file:
                print(f"📄 检测到文件变化: {window_title}")

                # 获取文件特定的计时时长
                file_duration = self._get_file_specific_duration(window_title)

                if file_duration and self.timer_window:
                    print(f" 更新计时器时长为: {file_duration} (不启动)")
                    # 只设置时间，不启动计时器
                    self.timer_window.set_duration(file_duration)
                    # 确保计时器不会自动启动
                    if self.timer_window.is_running:
                        self.timer_window.reset_timer()
                        print(f"⏹ 已停止之前的计时器")
                else:
                    print(f"ℹ 未找到文件特定时间设置")

                # 更新记录的文件
                self.last_detected_file = window_title
                print(f"📝 已更新当前文件: {window_title}")

        except Exception as e:
            print(f" 检查文件变化失败: {e}")

    def _is_presentation_file(self, window_title):
        """判断是否为演示文稿相关窗口（用于文件变化检测）"""
        presentation_keywords = [
            'powerpoint', 'ppt', 'pptx', 'pdf', 'acrobat', 'reader',
            'wps', '演示', '幻灯片', 'presentation', 'slide'
        ]

        title_lower = window_title.lower()
        return any(keyword in title_lower for keyword in presentation_keywords)
    
    def _on_fullscreen_exit(self):
        """全屏退出事件"""
        if self.current_fullscreen_window:
            logger.info("全屏演示已退出")
            print(" 全屏演示已退出")
            
            self.current_fullscreen_window = None
            
            # 全屏退出时停止计时器（不是暂停）
            if self.timer_window and self.timer_window.is_running:
                if self.timer_window.timer_finished_confirmed:
                    print(" 用户已确认计时结束，只停止计时器，不重置状态")
                    self.timer_window.is_running = False  # 只停止运行，不重置
                else:
                    print(" 自动停止计时")
                    self.timer_window.reset_timer()  # 完全停止并重置
    
    def get_current_presentation_info(self):
        """获取当前演示信息 - 改进版"""
        # 优先返回全屏状态的信息
        if self.current_fullscreen_window and self.last_detected_file:
            # 清理文件名，移除"润文档"等后缀
            clean_filename = self._get_clean_filename(self.last_detected_file)
            return {
                'window_handle': self.current_fullscreen_window,
                'file_name': clean_filename,
                'is_fullscreen': True
            }

        # 如果没有全屏，尝试检测当前活动的PPT/PDF窗口
        try:
            import win32gui
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window:
                window_title = win32gui.GetWindowText(foreground_window)
                if window_title and self._is_presentation_file(window_title):
                    # 清理文件名，移除"润文档"等后缀
                    clean_filename = self._get_clean_filename(window_title)
                    return {
                        'window_handle': foreground_window,
                        'file_name': clean_filename,
                        'is_fullscreen': False
                    }
        except Exception as e:
            print(f" 检测当前窗口失败: {e}")

        # 如果有最后检测到的文件，也返回（即使不在全屏状态）
        if self.last_detected_file:
            # 清理文件名，移除"润文档"等后缀
            clean_filename = self._get_clean_filename(self.last_detected_file)
            return {
                'window_handle': None,
                'file_name': clean_filename,
                'is_fullscreen': False
            }

        return None

    def _get_clean_filename(self, window_title):
        """清理文件名，移除润文档等后缀，返回最佳的文件名"""
        import re

        # 特殊处理WPS全屏放映格式：WPS Presentation Slide Show - [文件名.pptx]
        wps_slideshow_match = re.search(r'WPS Presentation Slide Show\s*-\s*\[(.*?)\]', window_title, re.IGNORECASE)
        if wps_slideshow_match:
            return wps_slideshow_match.group(1).strip()

        # 特殊处理PowerPoint全屏格式：PowerPoint Slide Show - [文件名.pptx]
        ppt_slideshow_match = re.search(r'PowerPoint Slide Show\s*-\s*\[(.*?)\]', window_title, re.IGNORECASE)
        if ppt_slideshow_match:
            return ppt_slideshow_match.group(1).strip()

        # 特殊处理WPS格式：移除 "- 润文档" 等后缀
        title_clean = window_title
        wps_suffixes = [r'\s*-\s*润文档', r'\s*-\s*WPS\s*Office', r'\s*-\s*WPS']
        for suffix in wps_suffixes:
            title_clean = re.sub(suffix, '', title_clean, flags=re.IGNORECASE)

        # 移除常见的应用程序名称和前缀
        app_patterns = [
            r'^PowerPoint\s*-\s*',
            r'^Microsoft PowerPoint\s*-\s*',
            r'^Adobe Acrobat\s*-\s*',
            r'^Adobe Reader\s*-\s*',
            r'^WPS演示\s*-\s*',
            r'^WPS Presentation\s*-\s*',
            r'^\s*-\s*',  # 开头的连字符
        ]

        for pattern in app_patterns:
            title_clean = re.sub(pattern, '', title_clean, flags=re.IGNORECASE)

        title_clean = title_clean.strip()

        # 如果清理后的标题包含文件扩展名，直接返回
        file_extensions = ['.ppt', '.pptx', '.pdf', '.pps', '.ppsx']
        for ext in file_extensions:
            if ext.lower() in title_clean.lower():
                return title_clean

        # 如果没有扩展名，尝试从原始标题中提取
        filenames = self._extract_filenames_from_title(window_title)
        if filenames:
            return filenames[0]  # 返回第一个提取的文件名

        # 如果都没有，返回清理后的标题
        return title_clean

    def _get_file_specific_duration(self, window_title):
        """获取文件特定的计时时长 - 改进的匹配算法"""
        # 强制重新加载配置，确保获取最新的用户设置
        self.config_manager.load_config()
        file_timers = self.config_manager.get_setting('files.file_specific_timers', {})

        print(f" 查找文件特定时间，窗口标题: '{window_title}'")
        print(f" 可用的文件计时器: {file_timers}")
        print(f"🔄 已重新加载配置文件以获取最新设置")

        if not file_timers:
            print(" 没有配置文件特定计时器")
            return None

        # 从窗口标题中提取可能的文件名
        extracted_filenames = self._extract_filenames_from_title(window_title)
        print(f"📄 从标题中提取的文件名: {extracted_filenames}")

        # 创建匹配结果列表，包含匹配度评分
        matches = []

        # 尝试匹配文件
        for file_path, duration in file_timers.items():
            filename = os.path.basename(file_path)
            filename_no_ext = os.path.splitext(filename)[0]

            print(f" 检查文件: {file_path}")

            # 多种匹配策略
            for extracted in extracted_filenames:
                if self._is_filename_match(extracted, filename, filename_no_ext):
                    # 计算匹配度评分（更精确的匹配得分更高）
                    score = self._calculate_match_score(extracted, file_path, window_title)
                    matches.append((file_path, duration, score))
                    print(f" 找到匹配的文件 '{file_path}'，时长: {duration}，匹配度: {score}")
                    break

        if matches:
            # 按匹配度排序，选择最佳匹配
            matches.sort(key=lambda x: x[2], reverse=True)
            best_match = matches[0]
            print(f" 选择最佳匹配: '{best_match[0]}'，时长: {best_match[1]}，匹配度: {best_match[2]}")
            return best_match[1]

        print(" 未找到匹配的文件特定时间")
        return None

    def _extract_filenames_from_title(self, window_title):
        """从窗口标题中提取可能的文件名 - 改进版"""
        import re

        filenames = []

        print(f" 原始窗口标题: '{window_title}'")

        # 特殊处理WPS格式：移除 "- 润文档" 等后缀
        title_clean = window_title
        wps_suffixes = [r'\s*-\s*润文档', r'\s*-\s*WPS\s*Office', r'\s*-\s*WPS']
        for suffix in wps_suffixes:
            title_clean = re.sub(suffix, '', title_clean, flags=re.IGNORECASE)

        print(f" 移除WPS后缀后: '{title_clean}'")

        # 移除常见的应用程序名称和前缀
        app_patterns = [
            r'^PowerPoint\s*-\s*',
            r'^Microsoft PowerPoint\s*-\s*',
            r'^Adobe Acrobat\s*-\s*',
            r'^Adobe Reader\s*-\s*',
            r'^WPS演示\s*-\s*',
            r'^WPS Presentation\s*-\s*',
            r'^\s*-\s*',  # 开头的连字符
        ]

        for pattern in app_patterns:
            title_clean = re.sub(pattern, '', title_clean, flags=re.IGNORECASE)

        title_clean = title_clean.strip()
        print(f" 移除应用前缀后: '{title_clean}'")

        # 直接提取文件名（保持原始格式）
        file_extensions = ['.ppt', '.pptx', '.pdf', '.pps', '.ppsx']

        # 方法1：直接匹配完整的文件名（包含扩展名）
        for ext in file_extensions:
            pattern = rf'([^/\\]*{re.escape(ext)})'
            matches = re.findall(pattern, title_clean, re.IGNORECASE)
            for match in matches:
                # 清理匹配结果
                clean_match = match.strip()
                if clean_match:
                    filenames.append(clean_match)
                    # 同时添加不同扩展名的版本
                    base_name = os.path.splitext(clean_match)[0]
                    for other_ext in file_extensions:
                        if other_ext != ext:
                            filenames.append(base_name + other_ext)

        # 方法2：如果标题就是文件名，直接使用
        if any(ext in title_clean.lower() for ext in file_extensions):
            filenames.append(title_clean)
            # 添加不同扩展名版本
            base_name = os.path.splitext(title_clean)[0]
            for ext in file_extensions:
                filenames.append(base_name + ext)

        # 去重并过滤
        unique_filenames = []
        for filename in filenames:
            if filename and filename not in unique_filenames:
                unique_filenames.append(filename)

        print(f" 提取的文件名候选: {unique_filenames}")
        return unique_filenames

    def _is_filename_match(self, extracted, filename, filename_no_ext):
        """判断提取的文件名是否匹配 - 改进版"""
        extracted_lower = extracted.lower().strip()
        filename_lower = filename.lower().strip()
        filename_no_ext_lower = filename_no_ext.lower().strip()

        print(f" 匹配检查: '{extracted}' vs '{filename}'")

        # 1. 精确匹配
        if extracted_lower == filename_lower:
            print(f" 精确匹配成功")
            return True

        # 2. 无扩展名精确匹配
        extracted_no_ext = os.path.splitext(extracted_lower)[0]
        if extracted_no_ext == filename_no_ext_lower:
            print(f" 无扩展名精确匹配成功")
            return True

        # 3. 包含匹配（双向）
        if extracted_lower in filename_lower or filename_lower in extracted_lower:
            print(f" 包含匹配成功")
            return True

        # 4. 无扩展名包含匹配
        if extracted_no_ext in filename_no_ext_lower or filename_no_ext_lower in extracted_no_ext:
            print(f" 无扩展名包含匹配成功")
            return True

        # 5. 模糊匹配：移除特殊字符后比较
        import re
        extracted_clean = re.sub(r'[^\w\u4e00-\u9fff]', '', extracted_lower)
        filename_clean = re.sub(r'[^\w\u4e00-\u9fff]', '', filename_no_ext_lower)

        if extracted_clean and filename_clean:
            if extracted_clean == filename_clean:
                print(f" 清理后精确匹配成功")
                return True
            if extracted_clean in filename_clean or filename_clean in extracted_clean:
                print(f" 清理后包含匹配成功")
                return True

        print(f" 匹配失败")
        return False

    def _calculate_match_score(self, extracted, config_filename, window_title):
        """计算匹配度评分，分数越高表示匹配越精确"""
        score = 0

        # 基础分数
        score += 10

        # 【核心逻辑】优先级判断：
        # 1. 当前窗口模式下，优先使用窗口模式的设置
        # 2. 当前全屏模式下，优先使用全屏模式的设置
        # 3. 同模式下，用户设置优先于默认设置

        current_is_fullscreen = 'WPS Presentation Slide Show' in window_title
        config_is_fullscreen = 'WPS Presentation Slide Show' in config_filename
        config_is_user_setting = ('- 润文档' in config_filename or 'WPS Presentation Slide Show' in config_filename)

        # 模式匹配优先级（最高优先级）
        if current_is_fullscreen == config_is_fullscreen:
            score += 2000  # 模式匹配，最高优先级
            print(f"   模式匹配加分: +2000 (当前{'全屏' if current_is_fullscreen else '窗口'}模式)")
        else:
            score += 500   # 模式不匹配，较低优先级
            print(f"   模式不匹配: +500 (当前{'全屏' if current_is_fullscreen else '窗口'}模式 vs 配置{'全屏' if config_is_fullscreen else '窗口'}模式)")

        # 用户设置加分（次高优先级）
        if config_is_user_setting:
            score += 1000
            if '- 润文档' in config_filename:
                print(f"  👤 用户窗口模式设置加分: +1000")
            if 'WPS Presentation Slide Show' in config_filename:
                print(f"  👤 用户全屏模式设置加分: +1000")

        # 精确匹配加分
        if extracted.lower() == config_filename.lower():
            score += 100
            print(f"  💯 精确匹配加分: +100")

        # 长度匹配加分（更长的配置文件名通常更精确）
        config_length = len(config_filename)
        if config_length > 50:
            score += 50
            print(f"  📏 长文件名加分: +50")
        elif config_length > 30:
            score += 30
            print(f"  📏 中等文件名加分: +30")

        # 包含窗口标题信息加分
        window_title_lower = window_title.lower()
        config_lower = config_filename.lower()

        # 检查是否包含特殊标识符
        special_markers = ['- 润文档', 'wps presentation slide show', 'powerpoint', 'adobe']
        for marker in special_markers:
            if marker in config_lower and marker in window_title_lower:
                score += 40
                print(f"   特殊标识符匹配加分: +40 ({marker})")

        print(f"  📊 总评分: {score}")
        return score

    def _format_duration(self, seconds):
        """格式化时长显示"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds == 0:
                return f"{minutes}分钟"
            else:
                return f"{minutes}分{remaining_seconds}秒"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes == 0:
                return f"{hours}小时"
            else:
                return f"{hours}小时{remaining_minutes}分钟"

    def _analyze_and_suggest_duration(self, window_title):
        """智能分析文件并建议计时时长"""
        try:
            # 尝试从窗口标题中提取可能的文件路径
            possible_paths = self._extract_possible_file_paths(window_title)

            for file_path in possible_paths:
                if os.path.exists(file_path):
                    logger.info(f"正在分析文件: {file_path}")
                    analysis_result = analyze_presentation_file(file_path)

                    if analysis_result.get('analysis_success'):
                        suggested_time = analysis_result.get('suggested_time', 1800)
                        file_type = analysis_result.get('file_type', 'unknown')
                        page_count = analysis_result.get('page_count', 0)

                        logger.info(f"文件分析完成: {file_type}, {page_count}页/张, 建议{suggested_time}秒")

                        # 保存分析结果到配置中，供设置界面使用
                        self._save_file_analysis_result(file_path, analysis_result)

                        return suggested_time

            logger.info(f"无法找到对应的文件进行分析: {window_title}")
            return None

        except Exception as e:
            logger.error(f"智能分析文件失败: {e}")
            return None

    def _extract_possible_file_paths(self, window_title):
        """从窗口标题中提取可能的文件路径"""
        possible_paths = []

        # 常见的文档目录
        common_dirs = [
            os.path.expanduser("~/Desktop"),
            os.path.expanduser("~/Documents"),
            os.path.expanduser("~/Downloads"),
            "C:\\Users\\<USER>\\Documents",
            "D:\\Documents",
        ]

        # 从窗口标题中提取可能的文件名
        # 移除常见的应用程序名称
        title_clean = window_title
        for app_name in ["PowerPoint", "Adobe Acrobat", "PDF", "Microsoft", "演示文稿"]:
            title_clean = title_clean.replace(app_name, "").strip()

        # 移除常见的分隔符和后缀
        title_clean = title_clean.replace(" - ", "").replace("- ", "").strip()

        # 如果标题包含文件扩展名，直接使用
        if any(ext in title_clean.lower() for ext in ['.pptx', '.ppt', '.pdf']):
            filename = title_clean.strip()

            # 在常见目录中查找
            for directory in common_dirs:
                if os.path.exists(directory):
                    full_path = os.path.join(directory, filename)
                    possible_paths.append(full_path)

        # 如果没有扩展名，尝试添加常见扩展名
        else:
            base_name = title_clean.strip()
            if base_name:
                for ext in ['.pptx', '.ppt', '.pdf']:
                    filename = base_name + ext
                    for directory in common_dirs:
                        if os.path.exists(directory):
                            full_path = os.path.join(directory, filename)
                            possible_paths.append(full_path)

        return possible_paths

    def _save_file_analysis_result(self, file_path, analysis_result):
        """保存文件分析结果到配置"""
        try:
            # 获取现有的分析结果
            analysis_cache = self.config_manager.get_setting('files.analysis_cache', {})

            # 保存新的分析结果
            analysis_cache[file_path] = {
                'analysis_result': analysis_result,
                'analyzed_at': time.time()
            }

            # 限制缓存大小，只保留最近50个分析结果
            if len(analysis_cache) > 50:
                # 按时间排序，保留最新的50个
                sorted_items = sorted(analysis_cache.items(),
                                    key=lambda x: x[1].get('analyzed_at', 0),
                                    reverse=True)
                analysis_cache = dict(sorted_items[:50])

            # 保存到配置
            self.config_manager.set_setting('files.analysis_cache', analysis_cache)

        except Exception as e:
            logger.error(f"保存文件分析结果失败: {e}")


# 全局窗口检测器实例
window_detector_fixed = None


def get_window_detector_fixed(timer_window=None):
    """获取修复版窗口检测器实例"""
    global window_detector_fixed
    if window_detector_fixed is None:
        window_detector_fixed = WindowDetectorFixed(timer_window)
    return window_detector_fixed
