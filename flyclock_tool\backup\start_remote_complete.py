#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的FlyClock远程控制服务器
"""

import sys
import os
import threading
import asyncio
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from remote.websocket_server import RemoteControlServer
from gui.timer_window import TimerWindow

def main():
    print("🚀 启动FlyClock完整远程控制服务器...")
    
    try:
        # 端口配置
        http_port = 8080
        ws_port = 8081
        
        # 设置HTTP服务器
        web_dir = Path(__file__).parent / "assets" / "web"
        print(f"📁 Web目录: {web_dir}")
        
        class LoggingHTTPRequestHandler(SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory=str(web_dir), **kwargs)
            
            def log_message(self, format, *args):
                print(f"📡 HTTP: {format % args}")
        
        # 启动HTTP服务器
        def run_http_server():
            try:
                httpd = HTTPServer(('0.0.0.0', http_port), LoggingHTTPRequestHandler)
                print(f"✅ HTTP服务器启动成功，端口: {http_port}")
                print(f"📱 本地访问: http://localhost:{http_port}/remote_client.html")
                
                # 获取本机IP地址
                import socket
                try:
                    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    s.connect(("*******", 80))
                    local_ip = s.getsockname()[0]
                    s.close()
                    print(f"📱 局域网访问: http://{local_ip}:{http_port}/remote_client.html")
                except Exception as e:
                    print(f"获取IP地址失败: {e}")
                
                httpd.serve_forever()
            except Exception as e:
                print(f"✗ HTTP服务器启动失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 启动计时器窗口（后台运行）
        print("✓ 启动计时器窗口...")
        timer_window = TimerWindow()
        
        def run_timer_window():
            try:
                timer_window.run()
            except Exception as e:
                print(f"计时器窗口运行失败: {e}")
        
        timer_thread = threading.Thread(target=run_timer_window, daemon=True)
        timer_thread.start()
        
        # 等待计时器窗口初始化
        time.sleep(2)
        
        # 创建远程控制服务器
        server = RemoteControlServer(timer_window=timer_window, port=ws_port)
        
        # 启动WebSocket服务器
        async def run_websocket_server():
            try:
                await server.start_server()
                print(f"✅ WebSocket服务器启动成功，端口: {ws_port}")
                print("🎯 远程控制服务器完全启动！")
                print("按 Ctrl+C 停止服务器...")
                
                # 保持服务器运行
                try:
                    await asyncio.Future()  # 永远等待，直到被中断
                except asyncio.CancelledError:
                    await server.stop_server()
            except Exception as e:
                print(f"✗ WebSocket服务器启动失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 启动HTTP服务器线程
        http_thread = threading.Thread(target=run_http_server, daemon=True)
        http_thread.start()
        
        # 启动WebSocket服务器
        asyncio.run(run_websocket_server())
        
    except KeyboardInterrupt:
        print("\n🛑 远程控制服务器已停止")
    except Exception as e:
        print(f"✗ 远程控制服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
