#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 调试运行脚本
用于实时查看程序运行状态和调试信息
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def main():
    """启动ToolClock并显示调试信息"""
    print("=" * 60)
    print("🔧 ToolClock 调试模式启动")
    print("=" * 60)
    print("✓ 支持PPT/PDF全屏自动检测")
    print("✓ 支持倒计时和正计时模式") 
    print("✓ 支持文件特定计时设置")
    print("✓ 修复了全屏检测逻辑")
    print("=" * 60)
    
    try:
        # 强制刷新输出
        sys.stdout.flush()
        
        # 导入并启动计时器窗口
        print("📦 导入模块...")
        from flyclock_tool.gui.timer_window import TimerWindow
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        
        print("🚀 启动ToolClock计时器...")
        sys.stdout.flush()
        
        # 创建计时器窗口
        timer_window = TimerWindow()
        print("✓ 计时器窗口创建成功")
        sys.stdout.flush()
        
        # 创建窗口检测器
        detector = WindowDetectorFixed(timer_window)
        print("✓ 窗口检测器创建成功")
        sys.stdout.flush()
        
        print("🎯 ToolClock已启动，正在监控全屏状态...")
        print("📋 使用说明:")
        print("  1. 打开PPT/PDF文件 → 识别文件并准备时间（不启动）")
        print("  2. 按F5或全屏放映 → 自动开始倒计时")
        print("  3. 退出全屏 → 自动停止倒计时")
        print("  4. 切换文件 → 更新时间设置（不启动）")
        print("=" * 60)
        print("🔍 监控日志:")
        sys.stdout.flush()
        
        # 运行计时器窗口
        timer_window.run()
        
    except KeyboardInterrupt:
        print("\n✓ ToolClock已停止")
    except Exception as e:
        print(f"✗ ToolClock启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
