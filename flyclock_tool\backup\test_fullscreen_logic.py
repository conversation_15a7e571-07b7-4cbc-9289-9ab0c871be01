#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 全屏逻辑测试脚本
测试修复后的全屏检测和文件变化逻辑
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def test_fullscreen_detection_threshold():
    """测试全屏检测阈值"""
    print("=" * 60)
    print("1. 全屏检测阈值测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        
        detector = WindowDetectorFixed()
        
        # 模拟不同大小的窗口
        test_cases = [
            # (窗口宽, 窗口高, 屏幕宽, 屏幕高, 左, 上, 期望结果, 描述)
            (1280, 800, 1280, 800, 0, 0, True, "完全全屏"),
            (1280, 800, 1280, 800, -7, -7, True, "全屏带边框"),
            (1200, 750, 1280, 800, 40, 25, False, "大窗口但不是全屏(93.75%x93.75%)"),
            (1216, 760, 1280, 800, 32, 20, True, "接近全屏(95%x95%)"),
            (800, 600, 1280, 800, 240, 100, False, "普通窗口"),
            (1280, 720, 1280, 800, 0, 40, False, "宽度全屏但高度不够(90%)"),
        ]
        
        print("测试不同窗口大小的全屏检测:")
        for window_w, window_h, screen_w, screen_h, left, top, expected, desc in test_cases:
            # 模拟窗口矩形
            window_rect = (left, top, left + window_w, top + window_h)
            
            # 临时修改屏幕尺寸进行测试
            original_method = detector._get_screen_size
            detector._get_screen_size = lambda: (screen_w, screen_h)
            
            # 创建模拟窗口句柄
            class MockHwnd:
                def __init__(self, rect):
                    self.rect = rect
            
            mock_hwnd = MockHwnd(window_rect)
            
            # 模拟GetWindowRect
            import win32gui
            original_get_rect = win32gui.GetWindowRect
            win32gui.GetWindowRect = lambda hwnd: mock_hwnd.rect
            
            try:
                result = detector._is_fullscreen_window(mock_hwnd)
                status = "✅" if result == expected else "❌"
                print(f"  {status} {desc}: {window_w}x{window_h} -> {result}")
            finally:
                # 恢复原始方法
                detector._get_screen_size = original_method
                win32gui.GetWindowRect = original_get_rect
        
        return True
        
    except Exception as e:
        print(f"❌ 全屏检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_change_logic():
    """测试文件变化逻辑"""
    print("\n" + "=" * 60)
    print("2. 文件变化逻辑测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.gui.timer_window import TimerWindow
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置测试文件
        test_files = {
            'fileA.pptx': '00:05:00',
            'fileB.pptx': '00:08:00',
            'fileC.pdf': '00:10:00'
        }
        config_manager.set_setting('files.file_specific_timers', test_files)
        
        # 创建计时器和检测器
        timer = TimerWindow()
        detector = WindowDetectorFixed(timer)
        
        print("✓ 计时器和检测器创建成功")
        
        # 测试文件变化检测
        test_scenarios = [
            ("PowerPoint - fileA.pptx", "00:05:00"),
            ("WPS演示 - fileB.pptx", "00:08:00"),
            ("Adobe Acrobat - fileC.pdf", "00:10:00"),
        ]
        
        print("\n测试文件变化检测:")
        for window_title, expected_duration in test_scenarios:
            print(f"\n📄 模拟打开文件: {window_title}")
            
            # 记录初始状态
            initial_running = timer.is_running
            
            # 模拟文件变化检测
            class MockHwnd:
                pass
            
            mock_hwnd = MockHwnd()
            
            # 模拟GetWindowText
            import win32gui
            original_get_text = win32gui.GetWindowText
            win32gui.GetWindowText = lambda hwnd: window_title
            
            try:
                detector._check_file_change(mock_hwnd)
                
                # 检查结果
                final_running = timer.is_running
                current_time = timer.current_time
                
                print(f"  📊 结果:")
                print(f"    - 计时器运行状态: {initial_running} -> {final_running}")
                print(f"    - 当前时间: {current_time}秒")
                print(f"    - 期望时间: {expected_duration}")
                
                # 验证逻辑
                if not final_running:
                    print(f"  ✅ 正确：文件变化时计时器未自动启动")
                else:
                    print(f"  ❌ 错误：文件变化时计时器不应自动启动")
                
            finally:
                # 恢复原始方法
                win32gui.GetWindowText = original_get_text
        
        return True
        
    except Exception as e:
        print(f"❌ 文件变化逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fullscreen_vs_file_change():
    """测试全屏检测与文件变化的区别"""
    print("\n" + "=" * 60)
    print("3. 全屏检测 vs 文件变化区别测试")
    print("=" * 60)
    
    try:
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        from flyclock_tool.gui.timer_window import TimerWindow
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置测试文件
        test_files = {
            'test_presentation.pptx': '00:03:00'
        }
        config_manager.set_setting('files.file_specific_timers', test_files)
        config_manager.set_setting('timer.auto_start', True)
        
        # 创建计时器和检测器
        timer = TimerWindow()
        detector = WindowDetectorFixed(timer)
        
        window_title = "PowerPoint - test_presentation.pptx"
        
        print("📄 场景1: 文件变化（非全屏）")
        print(f"  模拟打开文件: {window_title}")
        
        # 模拟文件变化
        class MockHwnd:
            pass
        
        mock_hwnd = MockHwnd()
        
        import win32gui
        original_get_text = win32gui.GetWindowText
        win32gui.GetWindowText = lambda hwnd: window_title
        
        try:
            detector._check_file_change(mock_hwnd)
            
            file_change_running = timer.is_running
            file_change_time = timer.current_time
            
            print(f"  结果: 运行={file_change_running}, 时间={file_change_time}秒")
            
            # 重置计时器
            timer.reset_timer()
            timer.set_duration("00:03:00")
            
            print(f"\n🖥️ 场景2: 全屏检测")
            print(f"  模拟全屏进入: {window_title}")
            
            # 模拟全屏检测
            detector._on_fullscreen_enter(12345, window_title, "POWERPNT.EXE")
            
            fullscreen_running = timer.is_running
            fullscreen_time = timer.current_time
            
            print(f"  结果: 运行={fullscreen_running}, 时间={fullscreen_time}秒")
            
            # 验证逻辑
            print(f"\n📊 逻辑验证:")
            if not file_change_running and fullscreen_running:
                print(f"  ✅ 正确：文件变化不启动，全屏启动")
            else:
                print(f"  ❌ 错误：逻辑不符合预期")
                print(f"    文件变化应该不启动: {not file_change_running}")
                print(f"    全屏应该启动: {fullscreen_running}")
            
        finally:
            win32gui.GetWindowText = original_get_text
        
        return True
        
    except Exception as e:
        print(f"❌ 全屏vs文件变化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 ToolClock 全屏逻辑修复测试")
    print("测试修复后的全屏检测和文件变化逻辑...")
    
    tests = [
        ("全屏检测阈值", test_fullscreen_detection_threshold),
        ("文件变化逻辑", test_file_change_logic),
        ("全屏vs文件变化区别", test_fullscreen_vs_file_change)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！全屏逻辑已修复")
        print("\n📋 修复总结:")
        print("1. ✅ 提高全屏检测阈值到95%，避免大窗口误判")
        print("2. ✅ 添加文件变化检测，只更新时间不启动计时器")
        print("3. ✅ 确保只有真正全屏时才启动倒计时")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
