import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QListWidget, QListWidgetItem, QFileDialog, 
                             QMessageBox, QProgressBar, QLabel, QGroupBox, QSplitter)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap

# 动态导入预览模块
try:
    from preview import OFDPreviewWindow
    PREVIEW_AVAILABLE = True
except ImportError:
    PREVIEW_AVAILABLE = False

class ConvertWorker(QThread):
    progress_updated = pyqtSignal(int)  # 进度信号
    file_converted = pyqtSignal(str, bool)  # 文件转换完成信号
    conversion_finished = pyqtSignal()  # 转换完成信号
    
    def __init__(self, files, output_dir):
        super().__init__()
        self.files = files
        self.output_dir = output_dir
        
    def run(self):
        try:
            # 导入OFD处理库
            from easyofd import OFD
            
            total_files = len(self.files)
            for i, file_path in enumerate(self.files):
                try:
                    # 更新进度
                    progress = int((i / total_files) * 100)
                    self.progress_updated.emit(progress)
                    
                    # 执行转换
                    with open(file_path, "rb") as f:
                        ofd = OFD()
                        ofd.read(f.read())
                        pdf_bytes = ofd.to_pdf()
                        
                        # 生成输出文件路径
                        base_name = os.path.splitext(os.path.basename(file_path))[0]
                        output_path = os.path.join(self.output_dir, f"{base_name}.pdf")
                        
                        # 保存PDF文件
                        with open(output_path, "wb") as pdf_file:
                            pdf_file.write(pdf_bytes)
                            
                    self.file_converted.emit(file_path, True)
                except Exception as e:
                    print(f"转换文件 {file_path} 时出错: {str(e)}")
                    self.file_converted.emit(file_path, False)
                    
                # 更新进度
                progress = int(((i + 1) / total_files) * 100)
                self.progress_updated.emit(progress)
                
            self.conversion_finished.emit()
        except ImportError:
            QMessageBox.critical(None, "错误", "缺少必要的库，请安装 easyofd")

class OFDConverterWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OFD转PDF转换器")
        self.setGeometry(100, 100, 1000, 700)
        
        # 存储文件列表
        self.file_list = []
        self.output_dir = ""
        
        self.init_ui()
        
    def init_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建文件操作区域
        file_group = QGroupBox("文件操作")
        file_layout = QHBoxLayout()
        file_group.setLayout(file_layout)
        
        # 添加文件按钮
        self.add_file_btn = QPushButton("添加OFD文件")
        self.add_file_btn.clicked.connect(self.add_files)
        file_layout.addWidget(self.add_file_btn)
        
        # 添加文件夹按钮
        self.add_folder_btn = QPushButton("添加文件夹")
        self.add_folder_btn.clicked.connect(self.add_folder)
        file_layout.addWidget(self.add_folder_btn)
        
        # 清空列表按钮
        self.clear_btn = QPushButton("清空列表")
        self.clear_btn.clicked.connect(self.clear_list)
        file_layout.addWidget(self.clear_btn)
        
        # 输出目录按钮
        self.output_dir_btn = QPushButton("选择输出目录")
        self.output_dir_btn.clicked.connect(self.select_output_dir)
        file_layout.addWidget(self.output_dir_btn)
        
        # 转换按钮
        self.convert_btn = QPushButton("开始转换")
        self.convert_btn.clicked.connect(self.start_conversion)
        self.convert_btn.setEnabled(False)
        file_layout.addWidget(self.convert_btn)
        
        # 预览按钮
        self.preview_btn = QPushButton("预览选中文件")
        self.preview_btn.clicked.connect(self.preview_file)
        self.preview_btn.setEnabled(PREVIEW_AVAILABLE)
        if not PREVIEW_AVAILABLE:
            self.preview_btn.setToolTip("预览功能不可用，请检查preview.py文件")
        file_layout.addWidget(self.preview_btn)
        
        # 添加到主布局
        main_layout.addWidget(file_group)
        
        # 创建分割器用于文件列表和预览
        splitter = QSplitter(Qt.Horizontal)
        
        # 文件列表区域
        list_group = QGroupBox("文件列表")
        list_layout = QVBoxLayout()
        list_group.setLayout(list_layout)
        
        # 文件列表
        self.file_list_widget = QListWidget()
        self.file_list_widget.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.file_list_widget)
        
        splitter.addWidget(list_group)
        
        # 添加到主布局
        main_layout.addWidget(splitter)
        
        # 进度和状态区域
        status_group = QGroupBox("状态")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("请添加需要转换的OFD文件")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        main_layout.addWidget(status_group)
        
    def add_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择OFD文件", "", "OFD文件 (*.ofd)")
        
        if files:
            for file_path in files:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
                    item = QListWidgetItem(os.path.basename(file_path))
                    item.setToolTip(file_path)
                    self.file_list_widget.addItem(item)
            
            self.update_ui_state()
    
    def add_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择包含OFD文件的文件夹")
        
        if folder:
            ofd_files = []
            for file in os.listdir(folder):
                if file.lower().endswith(".ofd"):
                    file_path = os.path.join(folder, file)
                    ofd_files.append(file_path)
            
            for file_path in ofd_files:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
                    item = QListWidgetItem(os.path.basename(file_path))
                    item.setToolTip(file_path)
                    self.file_list_widget.addItem(item)
            
            self.update_ui_state()
    
    def clear_list(self):
        self.file_list.clear()
        self.file_list_widget.clear()
        self.update_ui_state()
    
    def select_output_dir(self):
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_dir = directory
            self.status_label.setText(f"输出目录: {directory}")
            self.update_ui_state()
    
    def update_ui_state(self):
        has_files = len(self.file_list) > 0
        has_output_dir = bool(self.output_dir)
        
        self.convert_btn.setEnabled(has_files and has_output_dir)
        
        if has_files and not has_output_dir:
            self.status_label.setText("请选择输出目录")
        elif has_files and has_output_dir:
            self.status_label.setText(f"已选择 {len(self.file_list)} 个文件，准备转换")
        elif not has_files and has_output_dir:
            self.status_label.setText("请添加需要转换的OFD文件")
    
    def on_selection_changed(self):
        selected_items = self.file_list_widget.selectedItems()
        self.preview_btn.setEnabled(len(selected_items) > 0 and PREVIEW_AVAILABLE)
    
    def preview_file(self):
        selected_items = self.file_list_widget.selectedItems()
        if selected_items:
            file_path = self.file_list[self.file_list_widget.row(selected_items[0])]
            # 打开预览窗口
            self.preview_window = OFDPreviewWindow()
            # 这里需要实现直接打开文件的功能
            self.preview_window.show()
            
            # 手动加载文件
            try:
                self.preview_window.open_file_with_path(file_path)
            except:
                # 如果预览窗口没有这个方法，则让用户自己打开
                QMessageBox.information(self, "提示", "请在预览窗口中手动打开文件:\n" + file_path)
    
    def start_conversion(self):
        if not self.file_list:
            QMessageBox.warning(self, "警告", "请先添加OFD文件")
            return
            
        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录")
            return
            
        # 禁用按钮
        self.add_file_btn.setEnabled(False)
        self.add_folder_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        self.output_dir_btn.setEnabled(False)
        self.convert_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        
        # 创建并启动转换线程
        self.worker = ConvertWorker(self.file_list, self.output_dir)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.file_converted.connect(self.on_file_converted)
        self.worker.conversion_finished.connect(self.on_conversion_finished)
        self.worker.start()
        
        self.status_label.setText("正在转换...")
    
    def update_progress(self, value):
        self.progress_bar.setValue(value)
    
    def on_file_converted(self, file_path, success):
        file_name = os.path.basename(file_path)
        if success:
            self.status_label.setText(f"已转换: {file_name}")
        else:
            self.status_label.setText(f"转换失败: {file_name}")
    
    def on_conversion_finished(self):
        # 恢复按钮
        self.add_file_btn.setEnabled(True)
        self.add_folder_btn.setEnabled(True)
        self.clear_btn.setEnabled(True)
        self.output_dir_btn.setEnabled(True)
        self.convert_btn.setEnabled(True)
        self.update_ui_state()  # 重新更新预览按钮状态
        
        self.status_label.setText("转换完成")
        QMessageBox.information(self, "完成", "所有文件转换完成")

def main():
    app = QApplication(sys.argv)
    window = OFDConverterWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()