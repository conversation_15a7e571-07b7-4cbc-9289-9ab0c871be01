#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试流程测试脚本
按照用户提供的正确流程进行测试
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_correct_flow():
    """测试正确的流程"""
    print("=" * 60)
    print("🧪 开始测试正确流程")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from gui.timer_window import TimerWindow
        from window_detector_fixed import WindowDetectorFixed
        
        print("✅ 模块导入成功")
        
        # 创建计时器窗口
        print("🔧 创建计时器窗口...")
        timer_window = TimerWindow()
        print("✅ 计时器窗口创建成功")
        
        # 创建窗口检测器
        print("🔧 创建窗口检测器...")
        detector = WindowDetectorFixed(timer_window)
        print("✅ 窗口检测器创建成功")
        
        # 步骤1：设置短时间倒计时（10秒）
        print("\n📋 步骤1：设置短时间倒计时（10秒）")
        timer_window.set_duration("00:00:10")
        print(f"✅ 设置完成，当前时长: {timer_window.total_time}秒")
        
        # 检查计时器状态
        print(f"📊 计时器状态:")
        print(f"   - 是否运行: {timer_window.is_running}")
        print(f"   - 当前时间: {timer_window.current_time}秒")
        print(f"   - 总时间: {timer_window.total_time}秒")
        
        # 步骤2：模拟全屏放映
        print("\n📋 步骤2：模拟全屏放映")
        print("⚠️ 请手动进入全屏放映模式...")
        print("   程序将自动检测并开始倒计时")
        
        # 等待用户操作
        input("按回车键继续到下一步...")
        
        # 检查计时器是否已启动
        print(f"📊 全屏后计时器状态:")
        print(f"   - 是否运行: {timer_window.is_running}")
        print(f"   - 当前时间: {timer_window.current_time}秒")
        
        if timer_window.is_running:
            print("✅ 计时器已启动")
        else:
            print("❌ 计时器未启动，请检查配置")
            return
        
        # 步骤3：等待倒计时结束
        print("\n📋 步骤3：等待倒计时结束")
        print("⏳ 等待倒计时结束...")
        
        # 监控计时器状态
        while timer_window.is_running and timer_window.current_time > 0:
            print(f"⏱️ 剩余时间: {timer_window.current_time}秒")
            time.sleep(1)
        
        print("⏰ 倒计时应该已结束")
        
        # 步骤4：检查是否自动退出全屏
        print("\n📋 步骤4：检查是否自动退出全屏")
        print("❓ 是否自动退出了全屏？")
        auto_exit = input("输入 y/n: ").lower() == 'y'
        
        if auto_exit:
            print("✅ 自动退出全屏功能正常")
        else:
            print("❌ 自动退出全屏功能异常")
        
        # 步骤5：检查系统弹窗
        print("\n📋 步骤5：检查系统弹窗")
        print("❓ 是否显示了'演示时间已到'的弹窗？")
        popup_shown = input("输入 y/n: ").lower() == 'y'
        
        if popup_shown:
            print("✅ 系统弹窗显示正常")
            print("请点击确定关闭弹窗...")
            input("点击确定后按回车键继续...")
        else:
            print("❌ 系统弹窗未显示")
        
        # 步骤6：检查计时器显示
        print("\n📋 步骤6：检查计时器显示")
        print(f"📊 当前计时器状态:")
        print(f"   - 是否运行: {timer_window.is_running}")
        print(f"   - 当前时间: {timer_window.current_time}秒")
        
        if timer_window.current_time < 0:
            print("✅ 计时器显示负数时间")
        else:
            print("❌ 计时器未显示负数时间")
        
        # 步骤7：重新进入全屏
        print("\n📋 步骤7：重新进入全屏")
        print("⚠️ 请重新按F5进入全屏放映...")
        input("进入全屏后按回车键继续...")
        
        # 步骤8：检查红色背景和负数显示
        print("\n📋 步骤8：检查红色背景和负数显示")
        print("❓ 计时器是否显示红色背景？")
        red_bg = input("输入 y/n: ").lower() == 'y'
        
        print("❓ 计时器是否显示负数时间格式（如-00:01）？")
        negative_time = input("输入 y/n: ").lower() == 'y'
        
        if red_bg and negative_time:
            print("✅ 红色背景和负数时间显示正常")
        else:
            print("❌ 红色背景或负数时间显示异常")
        
        # 总结
        print("\n" + "=" * 60)
        print("📋 测试结果总结:")
        print(f"   - 自动退出全屏: {'✅' if auto_exit else '❌'}")
        print(f"   - 系统弹窗显示: {'✅' if popup_shown else '❌'}")
        print(f"   - 红色背景显示: {'✅' if red_bg else '❌'}")
        print(f"   - 负数时间显示: {'✅' if negative_time else '❌'}")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_correct_flow()
