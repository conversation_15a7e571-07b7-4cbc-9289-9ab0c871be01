#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock Tool - PPT/PDF全屏放映计时辅助工具
项目根包初始化文件

版本：1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__description__ = "PPT/PDF全屏放映计时辅助工具"

# 项目信息
PROJECT_NAME = "FlyClock Tool"
PROJECT_VERSION = __version__
PROJECT_DESCRIPTION = __description__

# 支持的文件格式
SUPPORTED_PPT_FORMATS = ['.ppt', '.pptx', '.pps', '.ppsx']
SUPPORTED_PDF_FORMATS = ['.pdf']
SUPPORTED_FORMATS = SUPPORTED_PPT_FORMATS + SUPPORTED_PDF_FORMATS

# 默认配置
DEFAULT_TIMER_DURATION = 1800  # 30分钟（秒）
DEFAULT_CHECK_INTERVAL = 1.5   # 窗口检测间隔（秒）
DEFAULT_UPDATE_INTERVAL = 1000 # UI更新间隔（毫秒）