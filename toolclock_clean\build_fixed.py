#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 修复版打包脚本
专门解决线程和win32api打包问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")

def create_hook_file():
    """创建PyInstaller hook文件来确保正确打包"""
    hook_content = '''# PyInstaller hook for ToolClock
# 确保所有必要的模块被正确包含

hiddenimports = [
    # 核心线程模块
    'threading',
    '_thread',
    'queue',
    
    # 时间相关
    'time',
    'datetime',
    
    # Windows API
    'win32gui',
    'win32api',
    'win32con',
    'win32process',
    'pywintypes',
    'pythoncom',
    
    # GUI相关
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    
    # 音频相关
    'pygame',
    'pygame.mixer',
    'winsound',
    
    # 其他必要模块
    'logging',
    'json',
    'pathlib',
    'subprocess',
    're',
]

# 确保包含所有pygame子模块
try:
    import pygame
    import pkgutil
    for importer, modname, ispkg in pkgutil.iter_modules(pygame.__path__):
        hiddenimports.append(f'pygame.{modname}')
except ImportError:
    pass
'''
    
    hook_file = Path('hook-toolclock.py')
    with open(hook_file, 'w', encoding='utf-8') as f:
        f.write(hook_content)
    
    print("已创建PyInstaller hook文件")
    return hook_file

def build_toolclock_fixed():
    """构建修复版ToolClock exe"""
    print("\\n构建修复版ToolClock exe...")
    
    # 创建hook文件
    hook_file = create_hook_file()
    
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'ToolClock_Fixed',
            
            # 添加数据文件
            '--add-data', 'gui;gui',
            '--add-data', 'core;core',
            '--add-data', 'assets;assets',
            '--add-data', 'window_detector_fixed.py;.',
            
            # 使用自定义hook
            '--additional-hooks-dir', '.',
            
            # 核心隐藏导入
            '--hidden-import', 'threading',
            '--hidden-import', '_thread',
            '--hidden-import', 'queue',
            '--hidden-import', 'time',
            '--hidden-import', 'datetime',
            
            # Windows API - 关键！
            '--hidden-import', 'win32gui',
            '--hidden-import', 'win32api',
            '--hidden-import', 'win32con',
            '--hidden-import', 'win32process',
            '--hidden-import', 'pywintypes',
            '--hidden-import', 'pythoncom',
            
            # GUI模块
            '--hidden-import', 'tkinter',
            '--hidden-import', 'tkinter.ttk',
            '--hidden-import', 'tkinter.messagebox',
            '--hidden-import', 'tkinter.filedialog',
            '--hidden-import', 'tkinter.simpledialog',
            
            # 音频模块
            '--hidden-import', 'pygame',
            '--hidden-import', 'pygame.mixer',
            '--hidden-import', 'pygame.sndarray',
            '--hidden-import', 'winsound',
            
            # 其他必要模块
            '--hidden-import', 'logging',
            '--hidden-import', 'json',
            '--hidden-import', 'pathlib',
            '--hidden-import', 'subprocess',
            '--hidden-import', 're',
            
            # 收集所有相关模块
            '--collect-all', 'pygame',
            '--collect-all', 'win32gui',
            '--collect-all', 'pywintypes',
            
            # 运行时hook
            '--runtime-hook', 'runtime_hook.py',
            
            'run_with_log.py'
        ]
        
        # 创建运行时hook
        runtime_hook_content = '''# Runtime hook for ToolClock
import sys
import os

# 确保线程模块正确初始化
try:
    import threading
    import _thread
    print("Runtime: 线程模块初始化成功")
except Exception as e:
    print(f"Runtime: 线程模块初始化失败: {e}")

# 确保win32模块正确初始化
try:
    import win32gui
    import win32api
    print("Runtime: win32模块初始化成功")
except Exception as e:
    print(f"Runtime: win32模块初始化失败: {e}")

# 设置pygame环境变量
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'
'''
        
        with open('runtime_hook.py', 'w', encoding='utf-8') as f:
            f.write(runtime_hook_content)
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("ToolClock修复版exe构建成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    
    finally:
        # 清理临时文件
        if hook_file.exists():
            hook_file.unlink()
        if Path('runtime_hook.py').exists():
            Path('runtime_hook.py').unlink()

def main():
    """主函数"""
    print("=" * 60)
    print("ToolClock 修复版打包工具")
    print("专门解决线程和win32api问题")
    print("=" * 60)
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建修复版exe
    success = build_toolclock_fixed()
    
    # 清理临时文件（保留dist目录）
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
    
    # 总结
    print("\\n" + "=" * 60)
    print("构建总结")
    print("=" * 60)
    print(f"ToolClock修复版: {'成功' if success else '失败'}")
    
    if success:
        print("\\nToolClock修复版exe构建成功！")
        print("输出文件: dist/ToolClock_Fixed.exe")

        # 复制exe到当前目录
        import shutil
        try:
            shutil.copy2("dist/ToolClock_Fixed.exe", "ToolClock_Final.exe")
            print("已复制到: ToolClock_Final.exe")
        except Exception as e:
            print(f"复制文件失败: {e}")

        print("\\n修复内容:")
        print("- 移除智能分析功能")
        print("- 修复颜色切换逻辑")
        print("- 修复负数倒计时保持")
        print("- 强化线程模块打包")
        print("- 完整win32api支持")
        print("- 运行时模块初始化")
        print("- 自定义hook确保依赖完整")
    else:
        print("\\n构建失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()