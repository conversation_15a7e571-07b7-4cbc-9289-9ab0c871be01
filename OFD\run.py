#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OFD转PDF工具启动脚本
"""

import sys
import os

def main():
    print("OFD转PDF工具")
    print("1. 启动转换器")
    print("2. 启动预览器")
    
    if len(sys.argv) > 1:
        option = sys.argv[1]
    else:
        option = input("请选择要启动的程序 (1 或 2): ")
    
    if option == "1":
        try:
            from main import main as converter_main
            converter_main()
        except ImportError as e:
            print(f"无法启动转换器: {e}")
            print("请确保所有依赖已正确安装")
    elif option == "2":
        try:
            from preview import main as preview_main
            preview_main()
        except ImportError as e:
            print(f"无法启动预览器: {e}")
            print("请确保所有依赖已正确安装")
    else:
        print("无效选项")

if __name__ == "__main__":
    main()