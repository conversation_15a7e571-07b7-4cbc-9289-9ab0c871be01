#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程列表组件模块
实现进程列表的显示、刷新、选择等功能，与ProcessMonitor模块集成
提供可复用的进程选择界面组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import logging
from typing import List, Dict, Optional, Callable, Set

# 导入项目模块
try:
    from ..core.process_monitor import ProcessMonitor
except ImportError:
    # 开发环境导入
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.process_monitor import ProcessMonitor

# 配置日志
logger = logging.getLogger(__name__)


class ProcessList:
    """进程列表组件类 - 可复用的进程选择界面"""
    
    def __init__(self, parent_frame: tk.Widget, process_monitor: ProcessMonitor, 
                 selection_mode: str = 'extended', show_details: bool = True):
        """
        初始化进程列表组件
        
        Args:
            parent_frame: 父容器
            process_monitor: 进程监控器实例
            selection_mode: 选择模式 ('single', 'browse', 'multiple', 'extended')
            show_details: 是否显示详细信息（PID、路径等）
        """
        self.parent_frame = parent_frame
        self.process_monitor = process_monitor
        self.selection_mode = selection_mode
        self.show_details = show_details
        
        # 数据存储
        self.processes = []  # 当前进程列表
        self.filtered_processes = []  # 过滤后的进程列表
        self.selected_pids = set()  # 选中的进程PID集合
        
        # 过滤和搜索
        self.filter_text = ""
        self.sort_column = "name"
        self.sort_reverse = False
        
        # 回调函数
        self.selection_changed_callback = None
        self.double_click_callback = None
        
        # GUI组件
        self.tree = None
        self.search_var = None
        self.status_label = None
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 设置界面
        self.setup_ui()
        
        logger.info("ProcessList 组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_frame = tk.Frame(self.parent_frame, bg='#F0F0F0')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 顶部工具栏
        self.setup_toolbar()
        
        # 进程列表
        self.setup_process_tree()
        
        # 底部状态栏
        self.setup_status_bar()
        
        logger.info("ProcessList UI设置完成")
    
    def setup_toolbar(self):
        """设置顶部工具栏"""
        toolbar_frame = tk.Frame(self.main_frame, bg='#F0F0F0')
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 搜索框
        search_frame = tk.Frame(toolbar_frame, bg='#F0F0F0')
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        tk.Label(search_frame, text="搜索:", bg='#F0F0F0').pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self._on_search_changed)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=25)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        # 清除搜索按钮
        clear_btn = tk.Button(search_frame, text="清除", command=self.clear_search,
                             bg='#E1E1E1', relief=tk.RAISED, padx=10)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 操作按钮
        button_frame = tk.Frame(toolbar_frame, bg='#F0F0F0')
        button_frame.pack(side=tk.RIGHT)
        
        self.refresh_btn = tk.Button(button_frame, text="刷新", command=self.refresh_process_list,
                                    bg='#4CAF50', fg='white', relief=tk.RAISED, padx=15)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.select_all_btn = tk.Button(button_frame, text="全选", command=self.select_all,
                                       bg='#2196F3', fg='white', relief=tk.RAISED, padx=15)
        self.select_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_selection_btn = tk.Button(button_frame, text="清除选择", command=self.clear_selection,
                                            bg='#FF9800', fg='white', relief=tk.RAISED, padx=15)
        self.clear_selection_btn.pack(side=tk.LEFT)    
    def setup_process_tree(self):
        """设置进程列表树"""
        # 树容器
        tree_frame = tk.Frame(self.main_frame, bg='#F0F0F0')
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 定义列
        if self.show_details:
            columns = ('name', 'pid', 'path', 'memory', 'status')
            column_widths = {'name': 150, 'pid': 80, 'path': 250, 'memory': 100, 'status': 80}
            column_names = {'name': '进程名称', 'pid': 'PID', 'path': '路径', 'memory': '内存', 'status': '状态'}
        else:
            columns = ('name', 'pid')
            column_widths = {'name': 200, 'pid': 80}
            column_names = {'name': '进程名称', 'pid': 'PID'}
        
        # 创建Treeview
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='tree headings',
                                selectmode=self.selection_mode)
        
        # 设置列标题和宽度
        self.tree.heading('#0', text='选择')
        self.tree.column('#0', width=60, minwidth=60)
        
        for col in columns:
            self.tree.heading(col, text=column_names[col], 
                             command=lambda c=col: self._sort_by_column(c))
            self.tree.column(col, width=column_widths[col], minwidth=60)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.tree.bind('<<TreeviewSelect>>', self._on_selection_changed)
        self.tree.bind('<Double-1>', self._on_double_click)
        self.tree.bind('<Button-3>', self._on_right_click)  # 右键菜单
        
        # 配置样式
        self.tree.tag_configure('selected', background='#E3F2FD')
        self.tree.tag_configure('filtered', foreground='#666666')
    
    def setup_status_bar(self):
        """设置底部状态栏"""
        status_frame = tk.Frame(self.main_frame, bg='#E0E0E0', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X)
        
        # 状态信息
        self.status_label = tk.Label(status_frame, text="就绪", bg='#E0E0E0', anchor=tk.W, padx=10)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 统计信息
        self.count_label = tk.Label(status_frame, text="进程: 0 | 选中: 0", bg='#E0E0E0', padx=10)
        self.count_label.pack(side=tk.RIGHT)
    
    def refresh_process_list(self):
        """刷新进程列表"""
        self.status_label.config(text="正在刷新进程列表...")
        self.refresh_btn.config(state=tk.DISABLED)
        
        def fetch_processes():
            try:
                # 获取进程列表
                processes = self.process_monitor.get_exe_processes()
                
                # 在主线程中更新UI
                self.parent_frame.after(0, lambda: self._update_process_display(processes))
                
            except Exception as e:
                logger.error(f"获取进程列表失败: {e}")
                self.parent_frame.after(0, lambda: self._handle_refresh_error(str(e)))
        
        # 在后台线程中获取进程
        threading.Thread(target=fetch_processes, daemon=True).start()
    
    def _update_process_display(self, processes: List[Dict]):
        """更新进程显示（主线程中调用）"""
        try:
            with self._lock:
                self.processes = processes
                self._apply_filter_and_sort()
                self._populate_tree()
                self._update_status()
            
            self.status_label.config(text=f"已加载 {len(processes)} 个进程")
            
        except Exception as e:
            logger.error(f"更新进程显示失败: {e}")
            self.status_label.config(text="更新失败")
        finally:
            self.refresh_btn.config(state=tk.NORMAL)
    
    def _handle_refresh_error(self, error_msg: str):
        """处理刷新错误"""
        self.status_label.config(text="刷新失败")
        self.refresh_btn.config(state=tk.NORMAL)
        messagebox.showerror("错误", f"刷新进程列表失败:\n{error_msg}")    
    def _apply_filter_and_sort(self):
        """应用过滤和排序"""
        # 过滤
        if self.filter_text:
            filter_lower = self.filter_text.lower()
            self.filtered_processes = [
                proc for proc in self.processes
                if filter_lower in proc['name'].lower() or 
                   filter_lower in proc.get('exe_path', '').lower()
            ]
        else:
            self.filtered_processes = self.processes.copy()
        
        # 排序
        if self.sort_column and self.filtered_processes:
            try:
                if self.sort_column == 'pid':
                    self.filtered_processes.sort(key=lambda x: x.get('pid', 0), reverse=self.sort_reverse)
                elif self.sort_column == 'name':
                    self.filtered_processes.sort(key=lambda x: x.get('name', '').lower(), reverse=self.sort_reverse)
                elif self.sort_column == 'path':
                    self.filtered_processes.sort(key=lambda x: x.get('exe_path', '').lower(), reverse=self.sort_reverse)
                elif self.sort_column == 'memory':
                    # 需要获取内存信息
                    for proc in self.filtered_processes:
                        if 'memory_rss' not in proc:
                            proc_info = self.process_monitor.get_process_info(proc['pid'])
                            if proc_info and 'memory_info' in proc_info:
                                proc['memory_rss'] = proc_info['memory_info'].get('rss', 0)
                            else:
                                proc['memory_rss'] = 0
                    
                    self.filtered_processes.sort(key=lambda x: x.get('memory_rss', 0), reverse=self.sort_reverse)
                
            except Exception as e:
                logger.error(f"排序失败: {e}")
    
    def _populate_tree(self):
        """填充树控件"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加进程
        for process in self.filtered_processes:
            self._add_process_to_tree(process)
        
        # 恢复选择状态
        self._restore_selection()
    
    def _add_process_to_tree(self, process: Dict):
        """添加进程到树控件"""
        try:
            pid = process['pid']
            name = process['name']
            path = process.get('exe_path', 'Unknown')
            
            # 截断长路径
            if len(path) > 60:
                path = '...' + path[-57:]
            
            if self.show_details:
                # 获取内存信息
                memory_mb = 0
                status = 'Running'
                
                if 'memory_rss' in process:
                    memory_mb = process['memory_rss'] / 1024 / 1024
                else:
                    proc_info = self.process_monitor.get_process_info(pid)
                    if proc_info and 'memory_info' in proc_info:
                        memory_mb = proc_info['memory_info'].get('rss', 0) / 1024 / 1024
                        status = proc_info.get('status', 'Running')
                
                values = (name, pid, path, f"{memory_mb:.1f} MB", status)
            else:
                values = (name, pid)
            
            item_id = self.tree.insert('', 'end', values=values)
            
            # 如果进程被选中，添加标记
            if pid in self.selected_pids:
                self.tree.set(item_id, '#0', '✓')
                self.tree.item(item_id, tags=('selected',))
            
        except Exception as e:
            logger.error(f"添加进程到树失败: {e}")
    
    def _restore_selection(self):
        """恢复选择状态"""
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
            if values and len(values) >= 2:
                try:
                    pid = int(values[1])
                    if pid in self.selected_pids:
                        self.tree.selection_add(item)
                except ValueError:
                    continue
    
    def _update_status(self):
        """更新状态显示"""
        total_count = len(self.processes)
        filtered_count = len(self.filtered_processes)
        selected_count = len(self.selected_pids)
        
        if self.filter_text:
            self.count_label.config(text=f"进程: {filtered_count}/{total_count} | 选中: {selected_count}")
        else:
            self.count_label.config(text=f"进程: {total_count} | 选中: {selected_count}")
    
    def _sort_by_column(self, column: str):
        """按列排序"""
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False
        
        # 重新应用过滤和排序
        with self._lock:
            self._apply_filter_and_sort()
            self._populate_tree()
        
        # 更新列标题显示排序方向
        for col in self.tree['columns']:
            if col == column:
                direction = ' ↓' if self.sort_reverse else ' ↑'
                current_text = self.tree.heading(col, 'text')
                if ' ↑' in current_text or ' ↓' in current_text:
                    current_text = current_text[:-2]
                self.tree.heading(col, text=current_text + direction)
            else:
                current_text = self.tree.heading(col, 'text')
                if ' ↑' in current_text or ' ↓' in current_text:
                    self.tree.heading(col, text=current_text[:-2])    
    def _on_search_changed(self, *args):
        """搜索框内容变化事件"""
        self.filter_text = self.search_var.get()
        
        with self._lock:
            self._apply_filter_and_sort()
            self._populate_tree()
            self._update_status()
    
    def _on_selection_changed(self, event):
        """选择变化事件"""
        selected_items = self.tree.selection()
        new_selected_pids = set()
        
        for item in selected_items:
            values = self.tree.item(item, 'values')
            if values and len(values) >= 2:
                try:
                    pid = int(values[1])
                    new_selected_pids.add(pid)
                except ValueError:
                    continue
        
        # 更新选择状态
        self.selected_pids = new_selected_pids
        
        # 更新树显示
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
            if values and len(values) >= 2:
                try:
                    pid = int(values[1])
                    if pid in self.selected_pids:
                        self.tree.set(item, '#0', '✓')
                        self.tree.item(item, tags=('selected',))
                    else:
                        self.tree.set(item, '#0', '')
                        self.tree.item(item, tags=())
                except ValueError:
                    continue
        
        # 更新状态
        self._update_status()
        
        # 调用回调函数
        if self.selection_changed_callback:
            try:
                selected_processes = self.get_selected_processes()
                self.selection_changed_callback(selected_processes)
            except Exception as e:
                logger.error(f"选择变化回调失败: {e}")
    
    def _on_double_click(self, event):
        """双击事件"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if item:
            values = self.tree.item(item, 'values')
            if values and len(values) >= 2:
                try:
                    pid = int(values[1])
                    process = next((p for p in self.filtered_processes if p['pid'] == pid), None)
                    
                    if process and self.double_click_callback:
                        self.double_click_callback(process)
                except ValueError:
                    pass
    
    def _on_right_click(self, event):
        """右键菜单事件"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self._show_context_menu(event, item)
    
    def _show_context_menu(self, event, item):
        """显示右键菜单"""
        values = self.tree.item(item, 'values')
        if not values or len(values) < 2:
            return
        
        try:
            pid = int(values[1])
            process = next((p for p in self.filtered_processes if p['pid'] == pid), None)
            if not process:
                return
            
            # 创建右键菜单
            context_menu = tk.Menu(self.tree, tearoff=0)
            
            context_menu.add_command(label=f"进程: {process['name']}", state=tk.DISABLED)
            context_menu.add_command(label=f"PID: {pid}", state=tk.DISABLED)
            context_menu.add_separator()
            
            if pid in self.selected_pids:
                context_menu.add_command(label="取消选择", command=lambda: self._toggle_selection(pid))
            else:
                context_menu.add_command(label="选择", command=lambda: self._toggle_selection(pid))
            
            context_menu.add_separator()
            context_menu.add_command(label="查看详细信息", command=lambda: self._show_process_details(process))
            context_menu.add_command(label="复制进程名", command=lambda: self._copy_to_clipboard(process['name']))
            context_menu.add_command(label="复制PID", command=lambda: self._copy_to_clipboard(str(pid)))
            
            # 显示菜单
            context_menu.post(event.x_root, event.y_root)
            
        except ValueError:
            pass
    
    def _toggle_selection(self, pid: int):
        """切换进程选择状态"""
        if pid in self.selected_pids:
            self.selected_pids.remove(pid)
        else:
            self.selected_pids.add(pid)
        
        # 更新显示
        self._populate_tree()
        self._update_status()
        
        # 调用回调
        if self.selection_changed_callback:
            try:
                selected_processes = self.get_selected_processes()
                self.selection_changed_callback(selected_processes)
            except Exception as e:
                logger.error(f"选择变化回调失败: {e}")
    
    def _show_process_details(self, process: Dict):
        """显示进程详细信息"""
        details_window = tk.Toplevel(self.parent_frame)
        details_window.title(f"进程详细信息 - {process['name']}")
        details_window.geometry("500x400")
        details_window.resizable(True, True)
        
        # 创建文本框显示详细信息
        text_frame = tk.Frame(details_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 获取详细信息
        detailed_info = self.process_monitor.get_process_info(process['pid'])
        
        info_text = f"进程名称: {process['name']}\n"
        info_text += f"进程ID: {process['pid']}\n"
        info_text += f"可执行文件: {process.get('exe_path', 'Unknown')}\n"
        info_text += f"创建时间: {time.ctime(process.get('create_time', 0))}\n\n"
        
        if detailed_info:
            info_text += f"状态: {detailed_info.get('status', 'Unknown')}\n"
            if 'memory_info' in detailed_info:
                memory = detailed_info['memory_info']
                info_text += f"内存使用:\n"
                info_text += f"  RSS: {memory.get('rss', 0) / 1024 / 1024:.2f} MB\n"
                info_text += f"  VMS: {memory.get('vms', 0) / 1024 / 1024:.2f} MB\n"
            
            info_text += f"CPU使用率: {detailed_info.get('cpu_percent', 0):.2f}%\n"
        
        text_widget.insert(1.0, info_text)
        text_widget.config(state=tk.DISABLED)
        
        # 关闭按钮
        close_btn = tk.Button(details_window, text="关闭", command=details_window.destroy,
                             bg='#E1E1E1', relief=tk.RAISED, padx=20)
        close_btn.pack(pady=10)
    
    def _copy_to_clipboard(self, text: str):
        """复制文本到剪贴板"""
        try:
            self.parent_frame.clipboard_clear()
            self.parent_frame.clipboard_append(text)
            self.status_label.config(text=f"已复制: {text}")
        except Exception as e:
            logger.error(f"复制到剪贴板失败: {e}")    
    # 公共接口方法
    def get_selected_processes(self) -> List[Dict]:
        """获取选中的进程信息"""
        selected_processes = []
        
        for process in self.processes:
            if process['pid'] in self.selected_pids:
                selected_processes.append(process)
        
        return selected_processes
    
    def get_selected_pids(self) -> List[int]:
        """获取选中的进程PID列表"""
        return list(self.selected_pids)
    
    def set_selected_pids(self, pids: List[int]):
        """设置选中的进程PID列表"""
        self.selected_pids = set(pids)
        self._populate_tree()
        self._update_status()
        
        # 调用回调
        if self.selection_changed_callback:
            try:
                selected_processes = self.get_selected_processes()
                self.selection_changed_callback(selected_processes)
            except Exception as e:
                logger.error(f"选择变化回调失败: {e}")
    
    def select_all(self):
        """全选所有进程"""
        self.selected_pids = {proc['pid'] for proc in self.filtered_processes}
        self._populate_tree()
        self._update_status()
        
        # 调用回调
        if self.selection_changed_callback:
            try:
                selected_processes = self.get_selected_processes()
                self.selection_changed_callback(selected_processes)
            except Exception as e:
                logger.error(f"选择变化回调失败: {e}")
    
    def clear_selection(self):
        """清除所有选择"""
        self.selected_pids.clear()
        self.tree.selection_remove(self.tree.selection())
        self._populate_tree()
        self._update_status()
        
        # 调用回调
        if self.selection_changed_callback:
            try:
                selected_processes = self.get_selected_processes()
                self.selection_changed_callback(selected_processes)
            except Exception as e:
                logger.error(f"选择变化回调失败: {e}")
    
    def clear_search(self):
        """清除搜索"""
        self.search_var.set("")
    
    def set_filter(self, filter_text: str):
        """设置过滤文本"""
        self.search_var.set(filter_text)
    
    def get_process_count(self) -> Dict[str, int]:
        """获取进程数量统计"""
        return {
            'total': len(self.processes),
            'filtered': len(self.filtered_processes),
            'selected': len(self.selected_pids)
        }
    
    def set_selection_changed_callback(self, callback: Callable[[List[Dict]], None]):
        """设置选择变化回调函数"""
        self.selection_changed_callback = callback
    
    def set_double_click_callback(self, callback: Callable[[Dict], None]):
        """设置双击回调函数"""
        self.double_click_callback = callback
    
    def enable_auto_refresh(self, interval: int = 5):
        """启用自动刷新"""
        def auto_refresh():
            if self.parent_frame.winfo_exists():
                self.refresh_process_list()
                self.parent_frame.after(interval * 1000, auto_refresh)
        
        self.parent_frame.after(interval * 1000, auto_refresh)
    
    def export_process_list(self, file_path: str, selected_only: bool = False):
        """导出进程列表到文件"""
        try:
            processes_to_export = self.get_selected_processes() if selected_only else self.processes
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("进程名称,PID,路径,创建时间\n")
                for proc in processes_to_export:
                    create_time = time.strftime('%Y-%m-%d %H:%M:%S', 
                                              time.localtime(proc.get('create_time', 0)))
                    f.write(f"{proc['name']},{proc['pid']},{proc.get('exe_path', '')},{create_time}\n")
            
            self.status_label.config(text=f"已导出 {len(processes_to_export)} 个进程到 {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出进程列表失败: {e}")
            self.status_label.config(text="导出失败")
            return False
    
    def get_status(self) -> Dict[str, any]:
        """获取组件状态"""
        return {
            'total_processes': len(self.processes),
            'filtered_processes': len(self.filtered_processes),
            'selected_processes': len(self.selected_pids),
            'filter_text': self.filter_text,
            'sort_column': self.sort_column,
            'sort_reverse': self.sort_reverse,
            'selection_mode': self.selection_mode,
            'show_details': self.show_details
        }


# 工厂函数
def create_process_list(parent_frame: tk.Widget, process_monitor: ProcessMonitor,
                       selection_mode: str = 'extended', show_details: bool = True) -> ProcessList:
    """
    创建进程列表组件实例
    
    Args:
        parent_frame: 父容器
        process_monitor: 进程监控器
        selection_mode: 选择模式
        show_details: 是否显示详细信息
        
    Returns:
        ProcessList: 进程列表组件实例
    """
    return ProcessList(parent_frame, process_monitor, selection_mode, show_details)