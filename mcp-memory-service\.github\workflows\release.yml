name: Release (Manual)

on:
  workflow_dispatch:

jobs:
  release:
    runs-on: ubuntu-latest
    concurrency: release
    permissions:
      id-token: write
      contents: write

    steps:
    - uses: actions/checkout@v3 # would probably be better to use v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9' # this setup python action uses a separate version than the python-semantic-release, thats why we had the error

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install build hatchling python-semantic-release

    - name: Verify build module installation
      run: python -m pip show build

    - name: Build package
      run: python -m build

    - name: Python Semantic Release
      uses: python-semantic-release/python-semantic-release@master
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        root_options: "-v"
