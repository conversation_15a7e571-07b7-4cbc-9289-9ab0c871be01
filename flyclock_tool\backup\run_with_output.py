#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 带输出运行脚本
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def main():
    """启动ToolClock并显示详细输出"""
    print("=" * 60)
    print("🔧 ToolClock 启动中...")
    print("=" * 60)
    
    try:
        print("📦 导入模块...")
        sys.stdout.flush()
        
        from flyclock_tool.gui.timer_window import TimerWindow
        print("✓ TimerWindow 导入成功")
        sys.stdout.flush()
        
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        print("✓ WindowDetectorFixed 导入成功")
        sys.stdout.flush()
        
        print("🚀 创建计时器窗口...")
        sys.stdout.flush()
        
        # 创建计时器窗口
        timer_window = TimerWindow()
        print("✓ 计时器窗口创建成功")
        sys.stdout.flush()
        
        print("🔍 创建窗口检测器...")
        sys.stdout.flush()
        
        # 创建窗口检测器
        detector = WindowDetectorFixed(timer_window)
        print("✓ 窗口检测器创建成功")
        sys.stdout.flush()
        
        print("🎯 ToolClock已启动！")
        print("📋 修复后的逻辑:")
        print("  ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）")
        print("  ✅ 按F5或全屏放映 → 自动开始倒计时")
        print("  ✅ 退出全屏 → 自动停止倒计时")
        print("  ✅ 切换文件 → 更新时间设置（不启动）")
        print("=" * 60)
        print("🔍 实时监控日志（请进行测试）:")
        print("=" * 60)
        sys.stdout.flush()
        
        # 启动GUI主循环
        print("▶️ 启动GUI主循环...")
        sys.stdout.flush()
        
        timer_window.run()
        
    except KeyboardInterrupt:
        print("\n✓ ToolClock已停止")
    except Exception as e:
        print(f"✗ ToolClock启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
