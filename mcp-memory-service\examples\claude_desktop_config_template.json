{"mcpServers": {"memory": {"command": "${PYTHON_PATH}", "args": ["${PROJECT_PATH}/scripts/run_memory_server.py"], "env": {"MCP_MEMORY_CHROMA_PATH": "${USER_DATA_PATH}/mcp-memory/chroma_db", "MCP_MEMORY_BACKUPS_PATH": "${USER_DATA_PATH}/mcp-memory/backups", "PYTHONNOUSERSITE": "1", "PIP_NO_DEPENDENCIES": "1", "PIP_NO_INSTALL": "1", "PYTORCH_ENABLE_MPS_FALLBACK": "1", "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:128"}}}}