2025-07-30 16:53:40,753 - INFO - ============================================================
2025-07-30 16:53:40,753 - INFO - 🔧 <PERSON><PERSON><PERSON><PERSON> 启动中...
2025-07-30 16:53:40,753 - INFO - ============================================================
2025-07-30 16:53:40,753 - INFO - 📦 导入模块...
2025-07-30 16:53:40,786 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 16:53:41,836 - INFO - pygame音频系统初始化成功
2025-07-30 16:53:41,836 - INFO - ✓ TimerWindow 导入成功
2025-07-30 16:53:41,838 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 16:53:41,838 - INFO - 🚀 创建计时器窗口...
2025-07-30 16:53:42,017 - INFO - 窗口检测已启动
2025-07-30 16:53:42,027 - INFO - ✓ 计时器窗口创建成功
2025-07-30 16:53:42,027 - INFO - 🔍 创建窗口检测器...
2025-07-30 16:53:42,027 - INFO - ✓ 窗口检测器创建成功
2025-07-30 16:53:42,028 - INFO - 🎯 ToolClock已启动！
2025-07-30 16:53:42,028 - INFO - 📋 修复后的逻辑:
2025-07-30 16:53:42,028 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 16:53:42,028 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 16:53:42,028 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 16:53:42,029 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 16:53:42,029 - INFO - ============================================================
2025-07-30 16:53:42,029 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 16:53:42,029 - INFO - ============================================================
2025-07-30 16:53:42,029 - INFO - ▶️ 启动GUI主循环...
2025-07-30 16:53:52,030 - INFO - 检测到全屏演示: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档 (WPSOFFICE.EXE)
2025-07-30 16:53:52,034 - INFO - 无法找到对应的文件进行分析: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档
2025-07-30 16:54:00,048 - INFO - 全屏演示已退出
2025-07-30 16:54:53,122 - INFO - 检测到全屏演示: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档 (WPSOFFICE.EXE)
2025-07-30 16:54:53,126 - INFO - 无法找到对应的文件进行分析: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档
2025-07-30 16:55:00,143 - INFO - 全屏演示已退出
2025-07-30 16:55:02,153 - INFO - 检测到全屏演示: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档 (WPSOFFICE.EXE)
2025-07-30 16:55:02,155 - INFO - 无法找到对应的文件进行分析: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档
2025-07-30 16:55:04,162 - INFO - 全屏演示已退出
2025-07-30 16:55:44,229 - INFO - 检测到全屏演示: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档 (WPSOFFICE.EXE)
2025-07-30 16:55:44,235 - INFO - 无法找到对应的文件进行分析: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档
2025-07-30 16:55:47,244 - INFO - 全屏演示已退出
2025-07-30 16:55:50,250 - INFO - 检测到全屏演示: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档 (WPSOFFICE.EXE)
2025-07-30 16:55:50,252 - INFO - 无法找到对应的文件进行分析: 2025年上半年科技工作总结与展望-黎民浩.pptx - 润文档
2025-07-30 16:55:51,256 - INFO - 全屏演示已退出
2025-07-30 16:56:16,288 - INFO - 窗口检测已停止
2025-07-30 16:56:16,289 - INFO - 已停止所有音频播放
2025-07-30 16:56:16,366 - INFO - 音频资源清理完成
2025-07-30 16:57:29,200 - INFO - ============================================================
2025-07-30 16:57:29,201 - INFO - 🔧 ToolClock 启动中...
2025-07-30 16:57:29,201 - INFO - ============================================================
2025-07-30 16:57:29,201 - INFO - 📦 导入模块...
2025-07-30 16:57:29,234 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 16:57:30,327 - INFO - pygame音频系统初始化成功
2025-07-30 16:57:30,327 - INFO - ✓ TimerWindow 导入成功
2025-07-30 16:57:30,329 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 16:57:30,329 - INFO - 🚀 创建计时器窗口...
2025-07-30 16:57:30,608 - INFO - 窗口检测已启动
2025-07-30 16:57:30,609 - INFO - ✓ 计时器窗口创建成功
2025-07-30 16:57:30,609 - INFO - 🔍 创建窗口检测器...
2025-07-30 16:57:30,609 - INFO - ✓ 窗口检测器创建成功
2025-07-30 16:57:30,610 - INFO - 🎯 ToolClock已启动！
2025-07-30 16:57:30,610 - INFO - 📋 修复后的逻辑:
2025-07-30 16:57:30,610 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 16:57:30,611 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 16:57:30,611 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 16:57:30,611 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 16:57:30,612 - INFO - ============================================================
2025-07-30 16:57:30,612 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 16:57:30,612 - INFO - ============================================================
2025-07-30 16:57:30,613 - INFO - ▶️ 启动GUI主循环...
2025-07-30 16:57:51,647 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 16:57:51,650 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx]
2025-07-30 16:57:59,674 - INFO - 全屏演示已退出
2025-07-30 16:59:44,806 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 16:59:44,809 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx]
2025-07-30 16:59:46,816 - INFO - 全屏演示已退出
2025-07-30 17:01:30,851 - INFO - ============================================================
2025-07-30 17:01:30,851 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:01:30,852 - INFO - ============================================================
2025-07-30 17:01:30,852 - INFO - 📦 导入模块...
2025-07-30 17:01:30,903 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:01:32,051 - INFO - pygame音频系统初始化成功
2025-07-30 17:01:32,051 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:01:32,053 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:01:32,054 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:01:32,205 - INFO - 窗口检测已启动
2025-07-30 17:01:32,206 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:01:32,206 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:01:32,206 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:01:32,206 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:01:32,206 - INFO - 📋 修复后的逻辑:
2025-07-30 17:01:32,206 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:01:32,206 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:01:32,206 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:01:32,206 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:01:32,206 - INFO - ============================================================
2025-07-30 17:01:32,207 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:01:32,207 - INFO - ============================================================
2025-07-30 17:01:32,207 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:01:50,305 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:01:54,323 - INFO - 全屏演示已退出
2025-07-30 17:01:57,334 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-李国超.pptx] (WPP.EXE)
2025-07-30 17:02:02,348 - INFO - 全屏演示已退出
2025-07-30 17:02:04,357 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-李国超.pptx] (WPP.EXE)
2025-07-30 17:02:07,366 - INFO - 全屏演示已退出
2025-07-30 17:03:16,532 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-谢建雄.pptx] (WPP.EXE)
2025-07-30 17:03:17,569 - INFO - 全屏演示已退出
2025-07-30 17:03:27,585 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-谢建雄.pptx] (WPP.EXE)
2025-07-30 17:03:29,603 - INFO - 全屏演示已退出
2025-07-30 17:05:50,021 - INFO - ============================================================
2025-07-30 17:05:50,021 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:05:50,022 - INFO - ============================================================
2025-07-30 17:05:50,022 - INFO - 📦 导入模块...
2025-07-30 17:05:50,088 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:05:51,201 - INFO - pygame音频系统初始化成功
2025-07-30 17:05:51,201 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:05:51,203 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:05:51,203 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:05:51,426 - INFO - 窗口检测已启动
2025-07-30 17:05:51,427 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:05:51,427 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:05:51,427 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:05:51,427 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:05:51,428 - INFO - 📋 修复后的逻辑:
2025-07-30 17:05:51,428 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:05:51,428 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:05:51,428 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:05:51,428 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:05:51,429 - INFO - ============================================================
2025-07-30 17:05:51,429 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:05:51,429 - INFO - ============================================================
2025-07-30 17:05:51,429 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:07:48,602 - INFO - 窗口检测已停止
2025-07-30 17:07:48,603 - INFO - 已停止所有音频播放
2025-07-30 17:07:48,681 - INFO - 音频资源清理完成
2025-07-30 17:09:05,505 - INFO - ============================================================
2025-07-30 17:09:05,505 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:09:05,505 - INFO - ============================================================
2025-07-30 17:09:05,505 - INFO - 📦 导入模块...
2025-07-30 17:09:05,550 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:09:06,626 - INFO - pygame音频系统初始化成功
2025-07-30 17:09:06,627 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:09:06,629 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:09:06,629 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:09:06,823 - INFO - 窗口检测已启动
2025-07-30 17:09:06,824 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:09:06,824 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:09:06,824 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:09:06,824 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:09:06,825 - INFO - 📋 修复后的逻辑:
2025-07-30 17:09:06,825 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:09:06,825 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:09:06,825 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:09:06,826 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:09:06,826 - INFO - ============================================================
2025-07-30 17:09:06,826 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:09:06,826 - INFO - ============================================================
2025-07-30 17:09:06,827 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:09:59,908 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-谢建雄.pptx] (WPP.EXE)
2025-07-30 17:10:01,921 - INFO - 全屏演示已退出
2025-07-30 17:10:05,931 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:10:08,942 - INFO - 全屏演示已退出
2025-07-30 17:10:17,956 - INFO - 窗口检测已停止
2025-07-30 17:10:17,957 - INFO - 已停止所有音频播放
2025-07-30 17:10:18,032 - INFO - 音频资源清理完成
2025-07-30 17:11:33,099 - INFO - ============================================================
2025-07-30 17:11:33,099 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:11:33,100 - INFO - ============================================================
2025-07-30 17:11:33,100 - INFO - 📦 导入模块...
2025-07-30 17:11:33,145 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:11:34,170 - INFO - pygame音频系统初始化成功
2025-07-30 17:11:34,170 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:11:34,171 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:11:34,172 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:11:34,283 - INFO - 窗口检测已启动
2025-07-30 17:11:34,283 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:11:34,283 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:11:34,283 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:11:34,284 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:11:34,284 - INFO - 📋 修复后的逻辑:
2025-07-30 17:11:34,284 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:11:34,284 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:11:34,284 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:11:34,284 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:11:34,284 - INFO - ============================================================
2025-07-30 17:11:34,284 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:11:34,285 - INFO - ============================================================
2025-07-30 17:11:34,285 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:17:02,612 - INFO - ============================================================
2025-07-30 17:17:02,613 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:17:02,613 - INFO - ============================================================
2025-07-30 17:17:02,613 - INFO - 📦 导入模块...
2025-07-30 17:17:02,648 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:17:02,938 - INFO - pygame音频系统初始化成功
2025-07-30 17:17:02,938 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:17:02,939 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:17:02,939 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:17:03,040 - INFO - 窗口检测已启动
2025-07-30 17:17:03,041 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:17:03,041 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:17:03,041 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:17:03,041 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:17:03,041 - INFO - 📋 修复后的逻辑:
2025-07-30 17:17:03,041 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:17:03,041 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:17:03,041 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:17:03,041 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:17:03,041 - INFO - ============================================================
2025-07-30 17:17:03,041 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:17:03,041 - INFO - ============================================================
2025-07-30 17:17:03,041 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:17:59,109 - INFO - 窗口检测已停止
2025-07-30 17:17:59,110 - INFO - 已停止所有音频播放
2025-07-30 17:17:59,155 - INFO - 音频资源清理完成
2025-07-30 17:18:11,803 - INFO - 窗口检测已停止
2025-07-30 17:18:11,803 - INFO - 已停止所有音频播放
2025-07-30 17:18:11,893 - INFO - 音频资源清理完成
2025-07-30 17:19:09,485 - INFO - ============================================================
2025-07-30 17:19:09,485 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:19:09,485 - INFO - ============================================================
2025-07-30 17:19:09,485 - INFO - 📦 导入模块...
2025-07-30 17:19:09,525 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:19:10,592 - INFO - pygame音频系统初始化成功
2025-07-30 17:19:10,592 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:19:10,594 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:19:10,594 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:19:10,715 - INFO - 窗口检测已启动
2025-07-30 17:19:10,715 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:19:10,716 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:19:10,716 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:19:10,716 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:19:10,716 - INFO - 📋 修复后的逻辑:
2025-07-30 17:19:10,716 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:19:10,716 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:19:10,716 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:19:10,717 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:19:10,717 - INFO - ============================================================
2025-07-30 17:19:10,717 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:19:10,717 - INFO - ============================================================
2025-07-30 17:19:10,717 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:19:23,164 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:19:26,756 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:19:31,808 - INFO - 全屏演示已退出
2025-07-30 17:19:40,439 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:19:43,835 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:19:45,851 - INFO - 全屏演示已退出
2025-07-30 17:19:46,858 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:19:47,871 - INFO - 全屏演示已退出
2025-07-30 17:21:27,504 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:21:27,506 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:24:51,233 - INFO - ============================================================
2025-07-30 17:24:51,233 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:24:51,234 - INFO - ============================================================
2025-07-30 17:24:51,234 - INFO - 📦 导入模块...
2025-07-30 17:24:51,271 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:24:52,355 - INFO - pygame音频系统初始化成功
2025-07-30 17:24:52,356 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:24:52,357 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:24:52,357 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:24:52,579 - INFO - 窗口检测已启动
2025-07-30 17:24:52,579 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:24:52,580 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:24:52,580 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:24:52,580 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:24:52,580 - INFO - 📋 修复后的逻辑:
2025-07-30 17:24:52,580 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:24:52,583 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:24:52,584 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:24:52,584 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:24:52,585 - INFO - ============================================================
2025-07-30 17:24:52,585 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:24:52,585 - INFO - ============================================================
2025-07-30 17:24:52,585 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:25:04,291 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:25:07,619 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:25:10,637 - INFO - 全屏演示已退出
2025-07-30 17:27:47,788 - INFO - ============================================================
2025-07-30 17:27:47,789 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:27:47,789 - INFO - ============================================================
2025-07-30 17:27:47,789 - INFO - 📦 导入模块...
2025-07-30 17:27:47,836 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:27:48,920 - INFO - pygame音频系统初始化成功
2025-07-30 17:27:48,921 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:27:48,922 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:27:48,922 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:27:49,133 - INFO - 窗口检测已启动
2025-07-30 17:27:49,134 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:27:49,134 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:27:49,135 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:27:49,135 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:27:49,135 - INFO - 📋 修复后的逻辑:
2025-07-30 17:27:49,135 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:27:49,136 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:27:49,136 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:27:49,136 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:27:49,136 - INFO - ============================================================
2025-07-30 17:27:49,137 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:27:49,137 - INFO - ============================================================
2025-07-30 17:27:49,137 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:28:02,333 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:28:05,167 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:28:11,191 - INFO - 全屏演示已退出
2025-07-30 17:28:18,281 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:28:22,217 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:28:25,232 - INFO - 全屏演示已退出
2025-07-30 17:29:07,920 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:29:12,314 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:29:14,332 - INFO - 全屏演示已退出
2025-07-30 17:31:04,489 - INFO - 窗口检测已停止
2025-07-30 17:31:04,489 - INFO - 已停止所有音频播放
2025-07-30 17:31:04,576 - INFO - 音频资源清理完成
2025-07-30 17:31:33,171 - INFO - ============================================================
2025-07-30 17:31:33,171 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:31:33,171 - INFO - ============================================================
2025-07-30 17:31:33,171 - INFO - 📦 导入模块...
2025-07-30 17:31:33,204 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:31:34,277 - INFO - pygame音频系统初始化成功
2025-07-30 17:31:34,277 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:31:34,278 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:31:34,278 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:31:34,526 - INFO - 窗口检测已启动
2025-07-30 17:31:34,527 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:31:34,527 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:31:34,527 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:31:34,528 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:31:34,528 - INFO - 📋 修复后的逻辑:
2025-07-30 17:31:34,528 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:31:34,528 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:31:34,529 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:31:34,529 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:31:34,530 - INFO - ============================================================
2025-07-30 17:31:34,530 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:31:34,530 - INFO - ============================================================
2025-07-30 17:31:34,531 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:31:38,534 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:31:45,679 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:31:48,587 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年科技工作总结与展望-黎民浩.pptx] (WPP.EXE)
2025-07-30 17:31:48,589 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:31:53,616 - INFO - 全屏演示已退出
2025-07-30 17:31:54,620 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:33:28,750 - INFO - 窗口检测已停止
2025-07-30 17:33:28,751 - INFO - 已停止所有音频播放
2025-07-30 17:33:28,820 - INFO - 音频资源清理完成
2025-07-30 17:35:29,264 - INFO - ============================================================
2025-07-30 17:35:29,264 - INFO - 🔧 ToolClock 启动中...
2025-07-30 17:35:29,264 - INFO - ============================================================
2025-07-30 17:35:29,264 - INFO - 📦 导入模块...
2025-07-30 17:35:29,323 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:35:30,475 - INFO - pygame音频系统初始化成功
2025-07-30 17:35:30,476 - INFO - ✓ TimerWindow 导入成功
2025-07-30 17:35:30,478 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-30 17:35:30,478 - INFO - 🚀 创建计时器窗口...
2025-07-30 17:35:30,821 - INFO - 窗口检测已启动
2025-07-30 17:35:30,821 - INFO - ✓ 计时器窗口创建成功
2025-07-30 17:35:30,821 - INFO - 🔍 创建窗口检测器...
2025-07-30 17:35:30,822 - INFO - ✓ 窗口检测器创建成功
2025-07-30 17:35:30,822 - INFO - 🎯 ToolClock已启动！
2025-07-30 17:35:30,822 - INFO - 📋 修复后的逻辑:
2025-07-30 17:35:30,822 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-30 17:35:30,823 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-30 17:35:30,823 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-30 17:35:30,824 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-30 17:35:30,824 - INFO - ============================================================
2025-07-30 17:35:30,824 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-30 17:35:30,825 - INFO - ============================================================
2025-07-30 17:35:30,825 - INFO - ▶️ 启动GUI主循环...
2025-07-30 17:35:52,859 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:35:53,345 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:35:53,346 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:35:54,878 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:08,914 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:14,845 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:18,950 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:19,965 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:25,598 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:41,468 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:44,012 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-30 17:36:46,714 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:08:29,937 - INFO - ============================================================
2025-07-31 15:08:29,937 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:08:29,938 - INFO - ============================================================
2025-07-31 15:08:29,938 - INFO - 📦 导入模块...
2025-07-31 15:08:29,939 - ERROR - ✗ ToolClock启动失败: No module named 'flyclock_tool'
2025-07-31 15:08:29,945 - ERROR - Traceback (most recent call last):
  File "D:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from flyclock_tool.gui.timer_window import TimerWindow
ModuleNotFoundError: No module named 'flyclock_tool'

2025-07-31 15:10:47,624 - INFO - ============================================================
2025-07-31 15:10:47,624 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:10:47,624 - INFO - ============================================================
2025-07-31 15:10:47,625 - INFO - 📦 导入模块...
2025-07-31 15:10:47,732 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:10:49,189 - INFO - pygame音频系统初始化成功
2025-07-31 15:10:49,190 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:10:49,190 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:10:49,190 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:10:49,443 - INFO - 窗口检测已启动
2025-07-31 15:10:49,443 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:10:49,443 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:10:49,444 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:10:49,444 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:10:49,444 - INFO - 📋 修复后的逻辑:
2025-07-31 15:10:49,444 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:10:49,445 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:10:49,445 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:10:49,445 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:10:49,445 - INFO - ============================================================
2025-07-31 15:10:49,446 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:10:49,446 - INFO - ============================================================
2025-07-31 15:10:49,446 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:10:58,817 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:10:58,819 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:10:58,820 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:10:58,821 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:01,505 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:01,507 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:01,508 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:01,510 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:04,273 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:04,275 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:04,277 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:04,278 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:06,417 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:06,418 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:06,419 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:06,419 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:08,520 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:08,522 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:08,523 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:08,524 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:10,913 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:10,914 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:10,915 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:10,915 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:13,484 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:11:57,112 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:09,642 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:11,655 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:14,672 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:15,680 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:16,694 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:17,700 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:18,715 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:19,724 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:20,752 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:22,759 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:23,771 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:54,167 - INFO - ============================================================
2025-07-31 15:12:54,167 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:12:54,168 - INFO - ============================================================
2025-07-31 15:12:54,168 - INFO - 📦 导入模块...
2025-07-31 15:12:54,336 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:12:55,307 - INFO - pygame音频系统初始化成功
2025-07-31 15:12:55,307 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:12:55,308 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:12:55,308 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:12:55,484 - INFO - 窗口检测已启动
2025-07-31 15:12:55,485 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:12:55,485 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:12:55,486 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:12:55,487 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:12:55,488 - INFO - 📋 修复后的逻辑:
2025-07-31 15:12:55,488 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:12:55,488 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:12:55,488 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:12:55,489 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:12:55,489 - INFO - ============================================================
2025-07-31 15:12:55,489 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:12:55,490 - INFO - ============================================================
2025-07-31 15:12:55,490 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:13:12,216 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:13:18,514 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:13:47,176 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:13:49,718 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:13:52,737 - INFO - 检测到全屏演示:  (WPP.EXE)
2025-07-31 15:13:52,739 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:13:52,742 - INFO - 无法找到对应的文件进行分析: 
2025-07-31 15:13:56,767 - INFO - 全屏演示已退出
2025-07-31 15:13:57,777 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:13:58,794 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:14:00,812 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-李建雄.pptx] (WPP.EXE)
2025-07-31 15:14:00,814 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:14:02,831 - INFO - 全屏演示已退出
2025-07-31 15:14:03,836 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:14:05,847 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:14:05,850 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:14:07,871 - INFO - 全屏演示已退出
2025-07-31 15:14:14,885 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:20:07,749 - INFO - ============================================================
2025-07-31 15:20:07,749 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:20:07,749 - INFO - ============================================================
2025-07-31 15:20:07,750 - INFO - 📦 导入模块...
2025-07-31 15:20:07,783 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:20:08,361 - INFO - pygame音频系统初始化成功
2025-07-31 15:20:08,362 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:20:08,362 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:20:08,362 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:20:08,533 - INFO - 窗口检测已启动
2025-07-31 15:20:08,533 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:20:08,534 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:20:08,534 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:20:08,534 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:20:08,534 - INFO - 📋 修复后的逻辑:
2025-07-31 15:20:08,534 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:20:08,534 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:20:08,535 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:20:08,535 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:20:08,535 - INFO - ============================================================
2025-07-31 15:20:08,535 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:20:08,535 - INFO - ============================================================
2025-07-31 15:20:08,535 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:20:28,622 - INFO - ✓ ToolClock已停止
2025-07-31 15:28:28,275 - INFO - ============================================================
2025-07-31 15:28:28,275 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:28:28,275 - INFO - ============================================================
2025-07-31 15:28:28,275 - INFO - 📦 导入模块...
2025-07-31 15:28:28,324 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:29,499 - INFO - pygame音频系统初始化成功
2025-07-31 15:28:29,500 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:28:29,500 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:28:29,501 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:28:29,617 - INFO - 窗口检测已启动
2025-07-31 15:28:29,617 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:28:29,618 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:28:29,618 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:28:29,618 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:28:29,618 - INFO - 📋 修复后的逻辑:
2025-07-31 15:28:29,618 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:28:29,618 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:28:29,618 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:28:29,619 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:28:29,619 - INFO - ============================================================
2025-07-31 15:28:29,620 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:28:29,620 - INFO - ============================================================
2025-07-31 15:28:29,620 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:28:35,700 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:35,701 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:35,702 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:35,703 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:38,185 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:42,655 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:44,674 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:50,875 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:28:50,877 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:06,721 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:07,730 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:18,417 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:22,781 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:28,808 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:33,822 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:37,842 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:45,865 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:51,887 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:29:59,917 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:30:14,110 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:30:22,968 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:30:22,970 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:30:27,989 - INFO - 全屏演示已退出
2025-07-31 15:30:28,994 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:30:31,016 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:30:33,033 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [年中工作总结-李宇锜（2025年）.pptx] (WPP.EXE)
2025-07-31 15:30:33,035 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:30:36,060 - INFO - 全屏演示已退出
2025-07-31 15:30:51,550 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:31:04,115 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [年中工作总结-李宇锜（2025年）.pptx] (WPP.EXE)
2025-07-31 15:31:04,118 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:31:05,138 - INFO - 全屏演示已退出
2025-07-31 15:31:08,148 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [年中工作总结-李宇锜（2025年）.pptx] (WPP.EXE)
2025-07-31 15:31:08,148 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:31:09,161 - INFO - 全屏演示已退出
2025-07-31 15:33:17,092 - INFO - ============================================================
2025-07-31 15:33:17,092 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:33:17,093 - INFO - ============================================================
2025-07-31 15:33:17,093 - INFO - 📦 导入模块...
2025-07-31 15:33:17,134 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:18,192 - INFO - pygame音频系统初始化成功
2025-07-31 15:33:18,193 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:33:18,193 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:33:18,194 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:33:18,518 - INFO - 窗口检测已启动
2025-07-31 15:33:18,518 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:33:18,519 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:33:18,519 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:33:18,519 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:33:18,519 - INFO - 📋 修复后的逻辑:
2025-07-31 15:33:18,519 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:33:18,520 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:33:18,520 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:33:18,520 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:33:18,521 - INFO - ============================================================
2025-07-31 15:33:18,521 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:33:18,521 - INFO - ============================================================
2025-07-31 15:33:18,521 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:33:31,026 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:31,028 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:31,031 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:31,033 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:32,390 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:39,549 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:42,562 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:33:42,566 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:33:43,580 - INFO - 全屏演示已退出
2025-07-31 15:34:01,724 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:06,653 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:34:06,657 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:07,675 - INFO - 全屏演示已退出
2025-07-31 15:34:08,683 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:15,708 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:34:15,710 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:19,735 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:24,755 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:25,737 - INFO - 全屏演示已退出
2025-07-31 15:34:27,757 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:30,761 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:33,754 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:34:33,756 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:33,764 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:36,768 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:37,791 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:39,771 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:42,774 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:42,815 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:43,794 - INFO - 全屏演示已退出
2025-07-31 15:34:45,776 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:45,800 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:45,816 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:46,816 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:34:46,817 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:34:48,778 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:48,818 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:50,840 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:51,781 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:51,819 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:54,783 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:54,821 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:55,859 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:56,845 - INFO - 全屏演示已退出
2025-07-31 15:34:57,786 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:57,822 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:34:58,862 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:00,789 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:00,825 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:01,864 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:03,791 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:03,826 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:04,866 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:06,793 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:06,829 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:07,869 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:09,795 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:09,831 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:10,867 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:35:10,870 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:12,797 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:12,833 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:12,884 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 15:35:12,886 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:35:13,873 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:15,800 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:15,834 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:16,874 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:16,915 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:18,802 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:18,836 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:19,877 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:21,805 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:21,839 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:21,943 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:22,879 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:22,916 - INFO - 全屏演示已退出
2025-07-31 15:35:24,807 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:24,840 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:24,946 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:25,881 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:27,810 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:27,842 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:27,949 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:28,883 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:30,812 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:30,844 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:30,951 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:31,886 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:33,814 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:33,845 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:33,953 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:34,887 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:36,816 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:36,847 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:36,957 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:37,890 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:39,819 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:39,848 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:39,961 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:40,892 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:42,821 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:42,850 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:42,964 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:43,895 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:45,825 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:45,852 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:45,967 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:46,897 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:48,828 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:48,853 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:48,970 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:49,900 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:51,831 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:51,856 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:51,973 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:52,902 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:54,833 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:54,858 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:54,976 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:55,904 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:57,836 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:57,861 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:57,980 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:35:58,907 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:00,840 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:00,863 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:00,982 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:01,909 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:03,843 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:03,865 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:03,985 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:04,912 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:06,847 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:06,867 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:06,987 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:07,914 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:09,849 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:09,869 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:09,991 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:10,917 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:12,852 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:12,870 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:12,994 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:13,919 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:15,854 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:15,874 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:15,997 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:16,922 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:18,856 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:18,877 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:19,000 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:19,925 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:21,858 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:21,879 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:22,004 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:22,927 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:24,861 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:24,880 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:25,007 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:25,930 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:27,864 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:27,882 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:28,010 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:28,932 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:30,866 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:30,884 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:31,013 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:31,934 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:33,869 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:33,886 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:34,016 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:34,936 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:36,872 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:36,889 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:37,020 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:37,938 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:39,874 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:39,892 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:40,022 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:40,941 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:42,876 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:42,893 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:43,024 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:43,943 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:45,878 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:45,894 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:46,026 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:46,945 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:48,879 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:48,896 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:49,030 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:49,947 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:51,882 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:51,898 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:52,033 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:52,948 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:54,884 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:54,900 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:55,037 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:55,950 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:57,886 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:57,901 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:58,040 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:36:58,952 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:00,889 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:00,902 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:01,042 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:01,954 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:03,891 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:03,904 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:04,044 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:04,957 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:06,893 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:06,905 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:07,047 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:07,960 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:09,895 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:09,906 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:10,050 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:10,962 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:12,897 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:12,908 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:13,053 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:13,965 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:15,899 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:15,910 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:16,057 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:16,968 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:18,901 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:18,912 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:19,059 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:19,970 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:20,089 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:37:21,903 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:21,914 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:22,062 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:22,972 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:24,906 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:24,915 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:25,064 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:25,974 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:27,909 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:27,918 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:28,067 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:28,976 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:30,912 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:30,920 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:31,070 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:31,977 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:33,913 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:33,922 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:34,073 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:34,979 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:36,916 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:36,924 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:37,075 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:37,981 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:39,919 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:39,926 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:40,077 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:40,984 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:42,922 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:42,928 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:43,079 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:43,987 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:45,924 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:45,931 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:46,081 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:46,989 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:48,926 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:48,933 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:49,085 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:49,991 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:51,927 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:51,935 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:52,087 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:52,992 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:54,929 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:54,937 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:55,089 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:55,994 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:57,931 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:57,939 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:58,091 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:37:58,997 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:00,933 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:00,940 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:01,093 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:01,999 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:03,936 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:03,943 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:04,096 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:05,002 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:06,939 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:06,944 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:07,098 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:08,003 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:09,941 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:09,947 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:10,101 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:11,006 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:12,944 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:12,949 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:13,104 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:14,008 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:15,945 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:15,951 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:16,105 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:17,009 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:18,948 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:18,954 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:19,107 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:20,011 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:21,951 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:21,956 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:22,109 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:23,013 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:24,954 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:24,959 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:25,111 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:26,016 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:27,957 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:27,960 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:28,114 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:29,019 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:30,960 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:30,962 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:31,117 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:32,022 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:33,964 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:33,966 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:34,119 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:35,024 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:36,968 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:36,970 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:37,121 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:38,026 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:39,972 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:39,973 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:40,124 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:41,027 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:42,975 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:42,976 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:43,126 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:44,030 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:45,978 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:45,978 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:46,129 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:47,032 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:48,981 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:48,982 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:49,131 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:50,034 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:51,984 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:51,985 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:52,134 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:53,036 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:54,987 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:54,988 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:55,136 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:56,037 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:57,991 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:57,992 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:58,139 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:38:59,040 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:00,994 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:00,995 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:01,141 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:02,042 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:03,998 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:03,999 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:04,143 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:05,044 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:07,002 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:07,002 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:07,145 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:08,047 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:10,005 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:10,006 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:10,148 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:23,557 - INFO - ============================================================
2025-07-31 15:39:23,557 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:39:23,557 - INFO - ============================================================
2025-07-31 15:39:23,558 - INFO - 📦 导入模块...
2025-07-31 15:39:23,611 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:39:24,671 - INFO - pygame音频系统初始化成功
2025-07-31 15:39:24,672 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:39:24,672 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:39:24,673 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:39:24,792 - INFO - 窗口检测已启动
2025-07-31 15:39:24,792 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:39:24,793 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:39:24,793 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:39:24,793 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:39:24,793 - INFO - 📋 修复后的逻辑:
2025-07-31 15:39:24,793 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:39:24,794 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:39:24,794 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:39:24,794 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:39:24,794 - INFO - ============================================================
2025-07-31 15:39:24,794 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:39:24,795 - INFO - ============================================================
2025-07-31 15:39:24,795 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:39:34,812 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:39:47,084 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:39:49,856 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:39:49,858 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:39:53,886 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:58,904 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:39:59,886 - INFO - 全屏演示已退出
2025-07-31 15:40:02,898 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:40:02,899 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:40:06,922 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:40:11,946 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:40:12,929 - INFO - 全屏演示已退出
2025-07-31 15:40:17,946 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:41:12,038 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:41:12,039 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:41:16,067 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:41:21,095 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:41:22,074 - INFO - 全屏演示已退出
2025-07-31 15:43:23,287 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:45:05,193 - INFO - ============================================================
2025-07-31 15:45:05,193 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:45:05,194 - INFO - ============================================================
2025-07-31 15:45:05,194 - INFO - 📦 导入模块...
2025-07-31 15:45:05,237 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:45:06,278 - INFO - pygame音频系统初始化成功
2025-07-31 15:45:06,278 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:45:06,279 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:45:06,279 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:45:06,416 - INFO - 窗口检测已启动
2025-07-31 15:45:06,417 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:45:06,418 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:45:06,418 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:45:06,418 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:45:06,418 - INFO - 📋 修复后的逻辑:
2025-07-31 15:45:06,418 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:45:06,419 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:45:06,419 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:45:06,419 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:45:06,419 - INFO - ============================================================
2025-07-31 15:45:06,419 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:45:06,419 - INFO - ============================================================
2025-07-31 15:45:06,420 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:45:14,429 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:45:18,452 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:45:18,453 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:45:22,486 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:45:27,508 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:45:28,496 - INFO - 全屏演示已退出
2025-07-31 15:45:30,503 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:45:31,521 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:45:31,522 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:45:35,544 - INFO - 全屏演示已退出
2025-07-31 15:48:10,799 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:49:20,063 - INFO - ============================================================
2025-07-31 15:49:20,064 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:49:20,064 - INFO - ============================================================
2025-07-31 15:49:20,064 - INFO - 📦 导入模块...
2025-07-31 15:49:20,136 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:49:21,183 - INFO - pygame音频系统初始化成功
2025-07-31 15:49:21,183 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:49:21,184 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:49:21,185 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:49:21,561 - INFO - 窗口检测已启动
2025-07-31 15:49:21,562 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:49:21,562 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:49:21,562 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:49:21,562 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:49:21,562 - INFO - 📋 修复后的逻辑:
2025-07-31 15:49:21,562 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:49:21,563 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:49:21,563 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:49:21,563 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:49:21,563 - INFO - ============================================================
2025-07-31 15:49:21,563 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:49:21,563 - INFO - ============================================================
2025-07-31 15:49:21,563 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:49:28,574 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:49:30,593 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:49:30,595 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:49:34,623 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:39,648 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:40,625 - INFO - 全屏演示已退出
2025-07-31 15:49:42,649 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:43,630 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:49:43,634 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:49:45,651 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:47,667 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:48,654 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:51,656 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:52,694 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:53,674 - INFO - 全屏演示已退出
2025-07-31 15:49:54,658 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:55,696 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:57,659 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:49:58,697 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:00,661 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:01,699 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:03,663 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:04,702 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:06,665 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:07,705 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:09,669 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:10,707 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:12,672 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:13,709 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:15,674 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:16,711 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:18,677 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:19,713 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:21,681 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:22,716 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:24,683 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:25,720 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:27,686 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:28,723 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:30,688 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:31,726 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:31,741 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:50:33,689 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:34,728 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:36,692 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:37,732 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:39,694 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:40,735 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:42,696 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:43,737 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:45,700 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:46,739 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:48,702 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:49,742 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:51,703 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:52,744 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:54,705 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:55,747 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:57,707 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:50:58,750 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:00,709 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:01,753 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:03,712 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:04,755 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:06,715 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:07,757 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:09,717 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:10,759 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:12,720 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:13,761 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:15,723 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:16,764 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:18,725 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:19,766 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:21,726 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:22,769 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:24,728 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:25,771 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:27,731 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:28,772 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:30,734 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:31,775 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:33,737 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:34,777 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:36,739 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:37,779 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:39,742 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:40,781 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:42,745 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:43,783 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:45,749 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:46,786 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:48,750 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:49,788 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:51,752 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:52,789 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:54,753 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:51:55,791 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:52:06,543 - INFO - ============================================================
2025-07-31 15:52:06,543 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:52:06,544 - INFO - ============================================================
2025-07-31 15:52:06,544 - INFO - 📦 导入模块...
2025-07-31 15:52:06,590 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:07,664 - INFO - pygame音频系统初始化成功
2025-07-31 15:52:07,665 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:52:07,665 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:52:07,666 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:52:08,068 - INFO - 窗口检测已启动
2025-07-31 15:52:08,068 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:52:08,069 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:52:08,069 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:52:08,069 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:52:08,069 - INFO - 📋 修复后的逻辑:
2025-07-31 15:52:08,069 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:52:08,069 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:52:08,069 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:52:08,070 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:52:08,070 - INFO - ============================================================
2025-07-31 15:52:08,070 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:52:08,070 - INFO - ============================================================
2025-07-31 15:52:08,070 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:52:12,075 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:14,097 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:52:14,100 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:18,132 - INFO - 全屏演示已退出
2025-07-31 15:52:18,775 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:18,777 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:22,138 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:52:22,139 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:26,166 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:52:31,198 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:52:32,174 - INFO - 全屏演示已退出
2025-07-31 15:52:34,200 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:52:35,181 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:36,197 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:52:36,198 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:52:37,202 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:52:38,221 - INFO - 全屏演示已退出
2025-07-31 15:53:11,270 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:55:34,109 - INFO - ============================================================
2025-07-31 15:55:34,110 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:55:34,110 - INFO - ============================================================
2025-07-31 15:55:34,111 - INFO - 📦 导入模块...
2025-07-31 15:55:34,154 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:55:34,154 - INFO - 已自动更新timer_end_action设置：exit_fullscreen -> keep_fullscreen
2025-07-31 15:55:34,155 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:55:35,244 - INFO - pygame音频系统初始化成功
2025-07-31 15:55:35,245 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:55:35,245 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:55:35,246 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:55:35,649 - INFO - 窗口检测已启动
2025-07-31 15:55:35,650 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:55:35,651 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:55:35,651 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:55:35,651 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:55:35,651 - INFO - 📋 修复后的逻辑:
2025-07-31 15:55:35,652 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:55:35,652 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:55:35,652 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:55:35,652 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:55:35,652 - INFO - ============================================================
2025-07-31 15:55:35,653 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:55:35,653 - INFO - ============================================================
2025-07-31 15:55:35,653 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:55:40,660 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:55:42,681 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:55:42,685 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:55:46,729 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:55:51,754 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:55:54,734 - INFO - 全屏演示已退出
2025-07-31 15:55:54,755 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:55:57,757 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:00,758 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:03,761 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:04,752 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:56:04,754 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:56:06,763 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:08,785 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:09,765 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:12,768 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:13,812 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:15,771 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:16,814 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:18,773 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:18,796 - INFO - 全屏演示已退出
2025-07-31 15:56:19,817 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:21,775 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:21,801 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:56:21,803 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:56:22,819 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:24,778 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:25,821 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:25,838 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:27,780 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:28,823 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:30,783 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:30,853 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:31,824 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:31,836 - INFO - 全屏演示已退出
2025-07-31 15:56:33,785 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:33,855 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:34,826 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:36,787 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:36,856 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:37,829 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:39,790 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:39,858 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:40,832 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:42,792 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:42,859 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:43,834 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:45,795 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:45,862 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:46,836 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:48,797 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:48,865 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:49,838 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:51,799 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:51,867 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:52,839 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:54,800 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:54,869 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:55,841 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:57,802 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:57,870 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:56:58,842 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:00,804 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:00,873 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:01,845 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:03,806 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:03,875 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:04,848 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:06,808 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:06,878 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:07,850 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:09,811 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:09,881 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:10,852 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:12,813 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:12,884 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:13,855 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:15,815 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:15,886 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:16,858 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:18,818 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:18,888 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:19,862 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:21,820 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:21,891 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:22,864 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:24,822 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:24,894 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:25,868 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:27,824 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:27,897 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:28,871 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:30,826 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:30,899 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:31,873 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:33,829 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:33,900 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:34,876 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:36,832 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:36,903 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:37,879 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:39,834 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:39,904 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:40,881 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:42,837 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:42,906 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:43,884 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:45,839 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:45,910 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:46,886 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:48,842 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:48,912 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:49,888 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:51,845 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:51,914 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:52,890 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:54,848 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:54,916 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:55,891 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:57,850 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:57,918 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:57:58,894 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:00,852 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:00,919 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:01,896 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:03,854 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:03,921 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:04,897 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:06,856 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:06,922 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:07,899 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:09,859 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:09,924 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:10,902 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:12,862 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:12,927 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:13,905 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:15,864 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:15,929 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:16,908 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:18,867 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:18,931 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:19,910 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:21,868 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:21,932 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:22,911 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:24,871 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:24,933 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:25,919 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:25,991 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:58:27,873 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:27,935 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:28,922 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:30,875 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:30,937 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:31,925 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:33,877 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:33,940 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:34,927 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:36,879 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:36,942 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:37,929 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:39,882 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:39,944 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:40,930 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:42,884 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:42,946 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:43,932 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:45,887 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:45,948 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:46,934 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:48,890 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:48,950 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:49,936 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:51,894 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:51,952 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:52,939 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:54,895 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:54,954 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:55,941 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:57,898 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:57,957 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:58:58,944 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:00,899 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:00,959 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:01,946 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:03,901 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:03,962 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:04,948 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:06,903 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:06,965 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:07,950 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:09,905 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:09,967 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:10,952 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:12,907 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:12,969 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:13,955 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:15,909 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:15,971 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:16,957 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:18,911 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:18,974 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:19,960 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:21,913 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:21,975 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:22,963 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:24,916 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:24,977 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:25,964 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:27,918 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:27,979 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:28,966 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:40,775 - INFO - ============================================================
2025-07-31 15:59:40,775 - INFO - 🔧 ToolClock 启动中...
2025-07-31 15:59:40,775 - INFO - ============================================================
2025-07-31 15:59:40,776 - INFO - 📦 导入模块...
2025-07-31 15:59:40,843 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:59:41,898 - INFO - pygame音频系统初始化成功
2025-07-31 15:59:41,898 - INFO - ✓ TimerWindow 导入成功
2025-07-31 15:59:41,899 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 15:59:41,899 - INFO - 🚀 创建计时器窗口...
2025-07-31 15:59:42,092 - INFO - 窗口检测已启动
2025-07-31 15:59:42,092 - INFO - ✓ 计时器窗口创建成功
2025-07-31 15:59:42,093 - INFO - 🔍 创建窗口检测器...
2025-07-31 15:59:42,093 - INFO - ✓ 窗口检测器创建成功
2025-07-31 15:59:42,093 - INFO - 🎯 ToolClock已启动！
2025-07-31 15:59:42,093 - INFO - 📋 修复后的逻辑:
2025-07-31 15:59:42,093 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 15:59:42,094 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 15:59:42,094 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 15:59:42,094 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 15:59:42,094 - INFO - ============================================================
2025-07-31 15:59:42,095 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 15:59:42,095 - INFO - ============================================================
2025-07-31 15:59:42,095 - INFO - ▶️ 启动GUI主循环...
2025-07-31 15:59:47,101 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:59:49,118 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 15:59:49,121 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 15:59:53,147 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 15:59:58,161 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:00:00,152 - INFO - 全屏演示已退出
2025-07-31 16:01:45,331 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:01:45,335 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:01:46,355 - INFO - 全屏演示已退出
2025-07-31 16:01:47,360 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:10,580 - INFO - ============================================================
2025-07-31 16:05:10,581 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:05:10,581 - INFO - ============================================================
2025-07-31 16:05:10,581 - INFO - 📦 导入模块...
2025-07-31 16:05:10,636 - ERROR - 加载配置文件失败: 'utf-8' codec can't decode bytes in position 3479-3480: invalid continuation byte
2025-07-31 16:05:11,761 - INFO - pygame音频系统初始化成功
2025-07-31 16:05:11,762 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:05:11,763 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:05:11,763 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:05:12,081 - INFO - 窗口检测已启动
2025-07-31 16:05:12,082 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:05:12,082 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:05:12,082 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:05:12,083 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:05:12,083 - INFO - 📋 修复后的逻辑:
2025-07-31 16:05:12,083 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:05:12,083 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:05:12,084 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:05:12,084 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:05:12,084 - INFO - ============================================================
2025-07-31 16:05:12,084 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:05:12,085 - INFO - ============================================================
2025-07-31 16:05:12,085 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:05:15,712 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:15,714 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:18,094 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:21,872 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:21,876 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:23,102 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:05:23,104 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:23,107 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx]
2025-07-31 16:05:26,117 - INFO - 全屏演示已退出
2025-07-31 16:05:35,996 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:35,997 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:05:50,386 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:53,174 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:05:56,184 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:05:56,188 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:00,213 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:06:05,227 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:06:06,223 - INFO - 全屏演示已退出
2025-07-31 16:06:08,234 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:06:08,237 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:11,263 - INFO - 全屏演示已退出
2025-07-31 16:06:12,279 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:14,290 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:06:14,295 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:18,330 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:06:23,357 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:06:24,343 - INFO - 全屏演示已退出
2025-07-31 16:06:41,389 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:06:41,391 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:45,411 - INFO - 全屏演示已退出
2025-07-31 16:06:46,432 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:48,444 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:06:48,446 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:51,465 - INFO - 全屏演示已退出
2025-07-31 16:06:52,475 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:06:57,490 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:06:57,491 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:07:01,543 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:07:06,570 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:07:07,558 - INFO - 全屏演示已退出
2025-07-31 16:08:18,679 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:10:34,238 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:10:34,240 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:10:45,991 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:10:58,987 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:10:59,999 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:11:05,023 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:11:05,025 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:11:09,056 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:11:14,072 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:11:15,062 - INFO - 全屏演示已退出
2025-07-31 16:11:25,417 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:11:25,419 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:12:41,184 - INFO - ============================================================
2025-07-31 16:12:41,184 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:12:41,185 - INFO - ============================================================
2025-07-31 16:12:41,185 - INFO - 📦 导入模块...
2025-07-31 16:12:41,217 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:12:42,312 - INFO - pygame音频系统初始化成功
2025-07-31 16:12:42,313 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:12:42,314 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:12:42,314 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:12:42,435 - INFO - 窗口检测已启动
2025-07-31 16:12:42,436 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:12:42,436 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:12:42,437 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:12:42,437 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:12:42,437 - INFO - 📋 修复后的逻辑:
2025-07-31 16:12:42,437 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:12:42,437 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:12:42,438 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:12:42,438 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:12:42,439 - INFO - ============================================================
2025-07-31 16:12:42,439 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:12:42,439 - INFO - ============================================================
2025-07-31 16:12:42,440 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:12:49,449 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:12:51,458 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:12:51,463 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:12:55,498 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:13:00,510 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:13:01,506 - INFO - 全屏演示已退出
2025-07-31 16:13:05,518 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:13:05,522 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:13:09,545 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:13:14,572 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:13:15,555 - INFO - 全屏演示已退出
2025-07-31 16:14:03,639 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:14:05,649 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:14:05,650 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:14:09,673 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:14:14,693 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:14:15,678 - INFO - 全屏演示已退出
2025-07-31 16:15:58,862 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:15:59,868 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:15:59,870 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:16:03,898 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:16:08,914 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:16:09,898 - INFO - 全屏演示已退出
2025-07-31 16:16:46,954 - INFO - 窗口检测已停止
2025-07-31 16:16:46,954 - INFO - 已停止所有音频播放
2025-07-31 16:16:47,046 - INFO - 音频资源清理完成
2025-07-31 16:19:27,254 - INFO - ============================================================
2025-07-31 16:19:27,255 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:19:27,255 - INFO - ============================================================
2025-07-31 16:19:27,256 - INFO - 📦 导入模块...
2025-07-31 16:19:27,302 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:19:27,312 - ERROR - ✗ ToolClock启动失败: invalid syntax (window_detector_fixed.py, line 408)
2025-07-31 16:19:27,317 - ERROR - Traceback (most recent call last):
  File "D:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from gui.timer_window import TimerWindow
  File "D:\trae\flyclock_tool\gui\timer_window.py", line 35, in <module>
    from window_detector_fixed import get_window_detector_fixed as get_window_detector
  File "D:\trae\flyclock_tool\window_detector_fixed.py", line 408
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

2025-07-31 16:31:04,657 - INFO - ============================================================
2025-07-31 16:31:04,658 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:31:04,658 - INFO - ============================================================
2025-07-31 16:31:04,658 - INFO - 📦 导入模块...
2025-07-31 16:31:04,694 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:31:04,701 - ERROR - ✗ ToolClock启动失败: invalid syntax (window_detector_fixed.py, line 408)
2025-07-31 16:31:04,711 - ERROR - Traceback (most recent call last):
  File "D:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from gui.timer_window import TimerWindow
  File "D:\trae\flyclock_tool\gui\timer_window.py", line 35, in <module>
    from window_detector_fixed import get_window_detector_fixed as get_window_detector
  File "D:\trae\flyclock_tool\window_detector_fixed.py", line 408
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

2025-07-31 16:33:32,586 - INFO - ============================================================
2025-07-31 16:33:32,587 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:33:32,587 - INFO - ============================================================
2025-07-31 16:33:32,587 - INFO - 📦 导入模块...
2025-07-31 16:33:32,631 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:33:32,640 - ERROR - ✗ ToolClock启动失败: invalid syntax (window_detector_fixed.py, line 408)
2025-07-31 16:33:32,645 - ERROR - Traceback (most recent call last):
  File "D:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from gui.timer_window import TimerWindow
  File "D:\trae\flyclock_tool\gui\timer_window.py", line 35, in <module>
    from window_detector_fixed import get_window_detector_fixed as get_window_detector
  File "D:\trae\flyclock_tool\window_detector_fixed.py", line 408
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

2025-07-31 16:36:37,771 - INFO - ============================================================
2025-07-31 16:36:37,772 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:36:37,772 - INFO - ============================================================
2025-07-31 16:36:37,773 - INFO - 📦 导入模块...
2025-07-31 16:36:37,813 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:36:37,821 - ERROR - ✗ ToolClock启动失败: invalid syntax (window_detector_fixed.py, line 408)
2025-07-31 16:36:37,829 - ERROR - Traceback (most recent call last):
  File "D:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from gui.timer_window import TimerWindow
  File "D:\trae\flyclock_tool\gui\timer_window.py", line 35, in <module>
    from window_detector_fixed import get_window_detector_fixed as get_window_detector
  File "D:\trae\flyclock_tool\window_detector_fixed.py", line 408
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

2025-07-31 16:39:26,324 - INFO - ============================================================
2025-07-31 16:39:26,325 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:39:26,325 - INFO - ============================================================
2025-07-31 16:39:26,325 - INFO - 📦 导入模块...
2025-07-31 16:39:26,355 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:39:27,540 - INFO - pygame音频系统初始化成功
2025-07-31 16:39:27,540 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:39:27,540 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:39:27,540 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:39:27,892 - INFO - 窗口检测已启动
2025-07-31 16:39:27,892 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:39:27,892 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:39:27,893 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:39:27,893 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:39:27,893 - INFO - 📋 修复后的逻辑:
2025-07-31 16:39:27,894 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:39:27,894 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:39:27,894 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:39:27,895 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:39:27,895 - INFO - ============================================================
2025-07-31 16:39:27,895 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:39:27,895 - INFO - ============================================================
2025-07-31 16:39:27,896 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:39:34,905 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:39:35,911 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:39:35,912 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:39:39,934 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:39:44,951 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:39:45,940 - INFO - 全屏演示已退出
2025-07-31 16:39:47,952 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:39:47,953 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:39:54,967 - INFO - 全屏演示已退出
2025-07-31 16:39:55,969 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:39:55,970 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:39:56,980 - INFO - 全屏演示已退出
2025-07-31 16:40:00,985 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:40:00,986 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:03,997 - INFO - 全屏演示已退出
2025-07-31 16:40:08,013 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:40:08,022 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:09,038 - INFO - 全屏演示已退出
2025-07-31 16:40:31,071 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:32,078 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:40:32,079 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:34,088 - INFO - 全屏演示已退出
2025-07-31 16:40:37,106 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:40:37,108 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:38,119 - INFO - 全屏演示已退出
2025-07-31 16:40:39,123 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:40:39,124 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:43,139 - INFO - 全屏演示已退出
2025-07-31 16:40:44,146 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:45,155 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:40:45,157 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:40:49,174 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:40:54,198 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:40:55,181 - INFO - 全屏演示已退出
2025-07-31 16:41:07,204 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:43:33,666 - INFO - ============================================================
2025-07-31 16:43:33,666 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:43:33,666 - INFO - ============================================================
2025-07-31 16:43:33,667 - INFO - 📦 导入模块...
2025-07-31 16:43:33,722 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:43:34,983 - INFO - pygame音频系统初始化成功
2025-07-31 16:43:34,983 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:43:34,984 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:43:34,984 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:43:35,385 - INFO - 窗口检测已启动
2025-07-31 16:43:35,385 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:43:35,386 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:43:35,386 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:43:35,386 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:43:35,387 - INFO - 📋 修复后的逻辑:
2025-07-31 16:43:35,387 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:43:35,387 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:43:35,387 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:43:35,387 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:43:35,388 - INFO - ============================================================
2025-07-31 16:43:35,388 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:43:35,388 - INFO - ============================================================
2025-07-31 16:43:35,388 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:43:39,392 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:43:41,404 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:43:41,408 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:43:45,434 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:43:50,451 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:43:51,443 - INFO - 全屏演示已退出
2025-07-31 16:43:53,455 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:43:53,456 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:44:02,473 - INFO - 全屏演示已退出
2025-07-31 16:44:03,478 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:44:07,495 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:44:07,496 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:44:12,512 - INFO - 全屏演示已退出
2025-07-31 16:45:35,477 - INFO - ============================================================
2025-07-31 16:45:35,478 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:45:35,478 - INFO - ============================================================
2025-07-31 16:45:35,479 - INFO - 📦 导入模块...
2025-07-31 16:45:35,542 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:45:36,747 - INFO - pygame音频系统初始化成功
2025-07-31 16:45:36,747 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:45:36,748 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:45:36,748 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:45:37,170 - INFO - 窗口检测已启动
2025-07-31 16:45:37,170 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:45:37,171 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:45:37,171 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:45:37,171 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:45:37,171 - INFO - 📋 修复后的逻辑:
2025-07-31 16:45:37,172 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:45:37,172 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:45:37,172 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:45:37,172 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:45:37,172 - INFO - ============================================================
2025-07-31 16:45:37,173 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:45:37,173 - INFO - ============================================================
2025-07-31 16:45:37,173 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:45:39,175 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:45:41,184 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:45:41,189 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:45:45,219 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:45:50,240 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:45:51,225 - INFO - 全屏演示已退出
2025-07-31 16:45:55,237 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:45:55,240 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:45:57,317 - INFO - 全屏演示已退出
2025-07-31 16:45:59,037 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:45:59,040 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:46:03,327 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:46:29,373 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:46:29,375 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:46:31,388 - INFO - 全屏演示已退出
2025-07-31 16:46:32,393 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:11,872 - INFO - ============================================================
2025-07-31 16:47:11,872 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:47:11,872 - INFO - ============================================================
2025-07-31 16:47:11,873 - INFO - 📦 导入模块...
2025-07-31 16:47:11,918 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:12,319 - INFO - pygame音频系统初始化成功
2025-07-31 16:47:12,319 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:47:12,320 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:47:12,320 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:47:12,453 - INFO - 窗口检测已启动
2025-07-31 16:47:12,453 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:47:12,453 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:47:12,454 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:47:12,454 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:47:12,454 - INFO - 📋 修复后的逻辑:
2025-07-31 16:47:12,454 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:47:12,454 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:47:12,454 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:47:12,454 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:47:12,455 - INFO - ============================================================
2025-07-31 16:47:12,455 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:47:12,455 - INFO - ============================================================
2025-07-31 16:47:12,455 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:47:21,462 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:47:21,465 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:21,465 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:47:21,467 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:25,487 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:47:30,498 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:47:31,493 - INFO - 全屏演示已退出
2025-07-31 16:47:31,493 - INFO - 全屏演示已退出
2025-07-31 16:47:34,497 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:47:34,500 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:34,502 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:47:34,507 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:37,517 - INFO - 全屏演示已退出
2025-07-31 16:47:37,520 - INFO - 全屏演示已退出
2025-07-31 16:47:38,521 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:47:38,523 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:50:29,975 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:50:29,978 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:50:47,342 - INFO - ============================================================
2025-07-31 16:50:47,342 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:50:47,342 - INFO - ============================================================
2025-07-31 16:50:47,343 - INFO - 📦 导入模块...
2025-07-31 16:50:47,409 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:50:47,761 - INFO - pygame音频系统初始化成功
2025-07-31 16:50:47,761 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:50:47,762 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:50:47,762 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:50:47,877 - INFO - 窗口检测已启动
2025-07-31 16:50:47,877 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:50:47,878 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:50:47,878 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:50:47,878 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:50:47,878 - INFO - 📋 修复后的逻辑:
2025-07-31 16:50:47,879 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:50:47,879 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:50:47,879 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:50:47,879 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:50:47,879 - INFO - ============================================================
2025-07-31 16:50:47,880 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:50:47,880 - INFO - ============================================================
2025-07-31 16:50:47,881 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:50:54,891 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:50:57,812 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:50:57,816 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:50:57,901 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:50:57,902 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:01,920 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:51:06,940 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:51:07,845 - INFO - 全屏演示已退出
2025-07-31 16:51:07,927 - INFO - 全屏演示已退出
2025-07-31 16:51:09,849 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:09,933 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:51:09,935 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:10,856 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:51:10,859 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:17,881 - INFO - 全屏演示已退出
2025-07-31 16:51:17,957 - INFO - 全屏演示已退出
2025-07-31 16:51:18,885 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:18,964 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:20,894 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:51:20,895 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:20,974 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:51:20,976 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:27,916 - INFO - 全屏演示已退出
2025-07-31 16:51:27,996 - INFO - 全屏演示已退出
2025-07-31 16:51:57,981 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:51:58,045 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:33,874 - INFO - ============================================================
2025-07-31 16:52:33,874 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:52:33,874 - INFO - ============================================================
2025-07-31 16:52:33,874 - INFO - 📦 导入模块...
2025-07-31 16:52:33,938 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:34,032 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:52:34,034 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:34,387 - INFO - pygame音频系统初始化成功
2025-07-31 16:52:34,387 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:52:34,388 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:52:34,388 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:52:34,552 - INFO - 窗口检测已启动
2025-07-31 16:52:34,552 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:52:34,553 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:52:34,553 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:52:34,554 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:52:34,555 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:52:34,555 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:34,555 - INFO - 📋 修复后的逻辑:
2025-07-31 16:52:34,556 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:52:34,557 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:52:34,558 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:52:34,559 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:52:34,559 - INFO - ============================================================
2025-07-31 16:52:34,560 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:52:34,560 - INFO - ============================================================
2025-07-31 16:52:34,560 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:52:35,040 - INFO - 全屏演示已退出
2025-07-31 16:52:35,673 - INFO - 全屏演示已退出
2025-07-31 16:52:43,693 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:52:43,697 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:44,053 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:52:44,055 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:47,735 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:52:52,753 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:52:53,076 - INFO - 全屏演示已退出
2025-07-31 16:52:53,739 - INFO - 全屏演示已退出
2025-07-31 16:52:56,084 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:56,750 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:58,763 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:52:58,766 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:52:59,092 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:52:59,095 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:53:07,809 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:53:08,122 - INFO - 全屏演示已退出
2025-07-31 16:53:08,797 - INFO - 全屏演示已退出
2025-07-31 16:54:30,943 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:54:31,249 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:55:43,772 - INFO - ============================================================
2025-07-31 16:55:43,772 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:55:43,772 - INFO - ============================================================
2025-07-31 16:55:43,772 - INFO - 📦 导入模块...
2025-07-31 16:55:43,821 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:55:44,149 - INFO - pygame音频系统初始化成功
2025-07-31 16:55:44,150 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:55:44,150 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:55:44,151 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:55:44,286 - INFO - 窗口检测已启动
2025-07-31 16:55:44,287 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:55:44,287 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:55:44,287 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:55:44,288 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:55:44,288 - INFO - 📋 修复后的逻辑:
2025-07-31 16:55:44,288 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:55:44,288 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:55:44,289 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:55:44,289 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:55:44,289 - INFO - ============================================================
2025-07-31 16:55:44,290 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:55:44,290 - INFO - ============================================================
2025-07-31 16:55:44,290 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:55:49,297 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:55:51,303 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:55:51,305 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:55:51,367 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:55:51,368 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:55:55,332 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:56:00,341 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:56:00,398 - INFO - 全屏演示已退出
2025-07-31 16:56:01,337 - INFO - 全屏演示已退出
2025-07-31 16:56:05,351 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:05,407 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:06,359 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:56:06,361 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:06,416 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:56:06,418 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:23,397 - INFO - 全屏演示已退出
2025-07-31 16:56:23,458 - INFO - 全屏演示已退出
2025-07-31 16:56:24,419 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:24,463 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:26,428 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:56:26,430 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:26,468 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:56:26,470 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:30,458 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:56:35,482 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:56:36,456 - INFO - 全屏演示已退出
2025-07-31 16:56:36,493 - INFO - 全屏演示已退出
2025-07-31 16:56:37,467 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:37,495 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:38,476 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:56:38,480 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:38,501 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:56:38,503 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:56:40,497 - INFO - 全屏演示已退出
2025-07-31 16:56:40,512 - INFO - 全屏演示已退出
2025-07-31 16:57:20,565 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:57:20,572 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:13,341 - INFO - ============================================================
2025-07-31 16:58:13,342 - INFO - 🔧 ToolClock 启动中...
2025-07-31 16:58:13,342 - INFO - ============================================================
2025-07-31 16:58:13,342 - INFO - 📦 导入模块...
2025-07-31 16:58:13,421 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:13,974 - INFO - pygame音频系统初始化成功
2025-07-31 16:58:13,974 - INFO - ✓ TimerWindow 导入成功
2025-07-31 16:58:13,975 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 16:58:13,975 - INFO - 🚀 创建计时器窗口...
2025-07-31 16:58:14,257 - INFO - 窗口检测已启动
2025-07-31 16:58:14,257 - INFO - ✓ 计时器窗口创建成功
2025-07-31 16:58:14,257 - INFO - 🔍 创建窗口检测器...
2025-07-31 16:58:14,257 - INFO - ✓ 窗口检测器创建成功
2025-07-31 16:58:14,257 - INFO - 🎯 ToolClock已启动！
2025-07-31 16:58:14,258 - INFO - 📋 修复后的逻辑:
2025-07-31 16:58:14,258 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 16:58:14,258 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 16:58:14,258 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 16:58:14,258 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 16:58:14,258 - INFO - ============================================================
2025-07-31 16:58:14,259 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 16:58:14,259 - INFO - ============================================================
2025-07-31 16:58:14,259 - INFO - ▶️ 启动GUI主循环...
2025-07-31 16:58:15,264 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:16,655 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:58:16,656 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:17,273 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:58:17,277 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:21,300 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:58:26,318 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 16:58:26,681 - INFO - 全屏演示已退出
2025-07-31 16:58:27,307 - INFO - 全屏演示已退出
2025-07-31 16:58:28,684 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:58:28,685 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:29,312 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 16:58:29,313 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 16:58:40,714 - INFO - 全屏演示已退出
2025-07-31 16:58:41,348 - INFO - 全屏演示已退出
2025-07-31 16:58:41,719 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:01:08,291 - INFO - ============================================================
2025-07-31 17:01:08,291 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:01:08,292 - INFO - ============================================================
2025-07-31 17:01:08,292 - INFO - 📦 导入模块...
2025-07-31 17:01:08,365 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:01:08,868 - INFO - pygame音频系统初始化成功
2025-07-31 17:01:08,868 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:01:08,869 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:01:08,869 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:01:09,024 - INFO - 窗口检测已启动
2025-07-31 17:01:09,024 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:01:09,025 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:01:09,025 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:01:09,026 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:01:09,026 - INFO - 📋 修复后的逻辑:
2025-07-31 17:01:09,026 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:01:09,027 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:01:09,027 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:01:09,028 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:01:09,028 - INFO - ============================================================
2025-07-31 17:01:09,028 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:01:09,028 - INFO - ============================================================
2025-07-31 17:01:09,028 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:04:56,861 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:04:56,864 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:04:59,440 - INFO - 窗口检测已停止
2025-07-31 17:04:59,440 - INFO - 已停止所有音频播放
2025-07-31 17:04:59,490 - INFO - 音频资源清理完成
2025-07-31 17:05:09,372 - INFO - 窗口检测已停止
2025-07-31 17:05:09,373 - INFO - 已停止所有音频播放
2025-07-31 17:05:09,467 - INFO - 音频资源清理完成
2025-07-31 17:12:27,977 - INFO - ============================================================
2025-07-31 17:12:27,978 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:12:27,978 - INFO - ============================================================
2025-07-31 17:12:27,978 - INFO - 📦 导入模块...
2025-07-31 17:12:28,028 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:12:28,651 - INFO - pygame音频系统初始化成功
2025-07-31 17:12:28,652 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:12:28,652 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:12:28,653 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:12:28,779 - INFO - 窗口检测已启动
2025-07-31 17:12:28,780 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:12:28,781 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:12:28,782 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:12:28,782 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:12:28,782 - INFO - 📋 修复后的逻辑:
2025-07-31 17:12:28,783 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:12:28,783 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:12:28,783 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:12:28,784 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:12:28,784 - INFO - ============================================================
2025-07-31 17:12:28,784 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:12:28,784 - INFO - ============================================================
2025-07-31 17:12:28,785 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:12:32,787 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:12:36,801 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:12:41,813 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:12:41,815 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:12:41,823 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:12:41,824 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 17:12:45,840 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:12:50,854 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:12:51,846 - INFO - 全屏演示已退出
2025-07-31 17:12:53,853 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:12:54,860 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:12:54,864 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:12:54,877 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:12:54,880 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:13:04,901 - INFO - 全屏演示已退出
2025-07-31 17:13:05,905 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:14,302 - INFO - ============================================================
2025-07-31 17:15:14,303 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:15:14,303 - INFO - ============================================================
2025-07-31 17:15:14,303 - INFO - 📦 导入模块...
2025-07-31 17:15:14,336 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:15,584 - INFO - pygame音频系统初始化成功
2025-07-31 17:15:15,585 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:15:15,586 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:15:15,586 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:15:15,712 - INFO - 窗口检测已启动
2025-07-31 17:15:15,712 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:15:15,713 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:15:15,713 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:15:15,713 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:15:15,714 - INFO - 📋 修复后的逻辑:
2025-07-31 17:15:15,714 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:15:15,715 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:15:15,716 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:15:15,716 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:15:15,716 - INFO - ============================================================
2025-07-31 17:15:15,717 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:15:15,718 - INFO - ============================================================
2025-07-31 17:15:15,718 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:15:19,723 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:21,731 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:15:21,735 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:21,745 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 17:15:25,758 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:15:30,779 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:15:31,768 - INFO - 全屏演示已退出
2025-07-31 17:15:34,781 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:15:34,785 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:34,803 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:15:44,822 - INFO - 全屏演示已退出
2025-07-31 17:15:45,824 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:48,836 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:15:48,837 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:48,842 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:15:53,855 - INFO - 全屏演示已退出
2025-07-31 17:15:54,858 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:58,874 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:15:58,877 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:15:58,884 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:16:00,896 - INFO - 全屏演示已退出
2025-07-31 17:16:01,900 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:03,911 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:05,926 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:11,945 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:13,952 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:15,957 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:16:15,960 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:15,968 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:16:17,977 - INFO - 全屏演示已退出
2025-07-31 17:16:18,980 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:20,990 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [年中工作总结-李宇锜（2025年）.pptx] (WPP.EXE)
2025-07-31 17:16:20,994 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:21,004 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [年中工作总结-李宇锜（2025年）.pptx]
2025-07-31 17:16:21,005 - INFO - DEBUG: timer_finished_confirmed=True, current_time=-3
2025-07-31 17:16:24,012 - INFO - 全屏演示已退出
2025-07-31 17:16:25,016 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:28,033 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:32,044 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:16:32,045 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:32,049 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:16:32,050 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 17:16:36,068 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:16:41,090 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:16:42,071 - INFO - 全屏演示已退出
2025-07-31 17:16:44,080 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:16:44,082 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:16:44,090 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:16:44,091 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:16:55,113 - INFO - 全屏演示已退出
2025-07-31 17:16:56,116 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:27:43,080 - INFO - 窗口检测已停止
2025-07-31 17:27:43,081 - INFO - 已停止所有音频播放
2025-07-31 17:27:43,169 - INFO - 音频资源清理完成
