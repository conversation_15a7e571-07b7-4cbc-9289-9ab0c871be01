#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建独立exe程序的脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查并安装PyInstaller"""
    try:
        import PyInstaller
        print("PyInstaller已安装")
        return True
    except ImportError:
        print("PyInstaller未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("PyInstaller安装失败")
            return False

def install_dependencies():
    """安装项目依赖"""
    print("正在安装项目依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装成功")
        return True
    except subprocess.CalledProcessError:
        print("依赖安装失败")
        return False

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'ttkbootstrap',
        'ttkbootstrap.themes',
        'ttkbootstrap.constants',
        'pygetwindow',
        'psutil',
        'win32gui',
        'win32process',
        'win32con',
        'win32api',
        'pywintypes',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='窗口切换工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('window_switcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("Spec文件创建成功")

def build_exe():
    """构建exe文件"""
    print("正在构建exe文件...")
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller", 
            "--clean",
            "window_switcher.spec"
        ])
        print("exe文件构建成功")
        return True
    except subprocess.CalledProcessError:
        print("exe文件构建失败")
        return False

def cleanup_build_files():
    """清理构建临时文件"""
    print("正在清理临时文件...")
    
    # 要删除的目录和文件
    cleanup_items = [
        'build',
        '__pycache__',
        'window_switcher.spec'
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"删除目录: {item}")
            else:
                os.remove(item)
                print(f"删除文件: {item}")

def main():
    """主函数"""
    print("=== 窗口切换工具 - exe构建脚本 ===")
    print()
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("错误：未找到main.py文件，请在process目录下运行此脚本")
        return False
    
    # 步骤1：检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 步骤2：安装依赖
    if not install_dependencies():
        return False
    
    # 步骤3：创建spec文件
    create_spec_file()
    
    # 步骤4：构建exe
    if not build_exe():
        return False
    
    # 步骤5：清理临时文件
    cleanup_build_files()
    
    print()
    print("=== 构建完成 ===")
    print("exe文件位置: dist/窗口切换工具.exe")
    print("可以将此exe文件复制到任何Windows电脑上运行")
    print()
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("构建成功！")
    else:
        print("构建失败！")
        sys.exit(1)