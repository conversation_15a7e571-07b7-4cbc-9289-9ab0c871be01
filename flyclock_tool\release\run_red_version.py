#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 红色背景持续蜂鸣版本启动脚本
"""

import os
import sys
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    log_file = Path("toolclock_red_debug.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=" * 60)
    logger.info("🔴 ToolClock 红色背景持续蜂鸣版本启动")
    logger.info("=" * 60)
    
    return logger

def check_dependencies():
    """检查依赖"""
    required_modules = [
        'tkinter',
        'threading',
        'time',
        'json',
        'pathlib',
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        return False
    
    return True

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    
    try:
        # 检查依赖
        if not check_dependencies():
            logger.error("依赖检查失败")
            return
        
        # 设置路径
        current_dir = Path(__file__).parent
        parent_dir = current_dir.parent
        
        # 添加路径到sys.path
        sys.path.insert(0, str(current_dir))
        sys.path.insert(0, str(parent_dir))
        
        logger.info(f"当前目录: {current_dir}")
        logger.info(f"父目录: {parent_dir}")
        
        # 导入并启动红色版本
        from timer_window_red import TimerWindowRed
        
        logger.info("🔴 启动红色背景持续蜂鸣版本...")
        
        # 创建并运行应用
        app = TimerWindowRed()
        app.run()
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        logger.error("请确保所有必要的文件都在正确的位置")
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        logger.info("🔴 红色版本程序结束")

if __name__ == "__main__":
    main()
