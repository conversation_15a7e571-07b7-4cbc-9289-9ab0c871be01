#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的计时器功能测试
"""

import tkinter as tk
import threading
import time

class SimpleTimer:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("Timer Test")
        self.window.geometry("200x100")
        
        self.is_running = False
        self.current_time = 10  # 10秒倒计时
        
        # 显示标签
        self.time_label = tk.Label(self.window, text="10", font=("Arial", 20))
        self.time_label.pack(pady=10)
        
        # 开始按钮
        self.start_button = tk.Button(self.window, text="开始", command=self.start_timer)
        self.start_button.pack(pady=5)
        
    def start_timer(self):
        if not self.is_running:
            self.is_running = True
            self.start_button.configure(text="运行中", state="disabled")
            
            # 启动计时线程
            timer_thread = threading.Thread(target=self.timer_loop, daemon=True)
            timer_thread.start()
    
    def timer_loop(self):
        while self.is_running and self.current_time > 0:
            time.sleep(1)
            self.current_time -= 1
            
            # 更新UI
            self.window.after(0, self.update_display)
        
        if self.current_time <= 0:
            self.window.after(0, self.timer_finished)
    
    def update_display(self):
        self.time_label.configure(text=str(self.current_time))
    
    def timer_finished(self):
        self.time_label.configure(text="完成!", fg="red")
        self.start_button.configure(text="完成", state="normal")
        self.is_running = False
    
    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = SimpleTimer()
    app.run()