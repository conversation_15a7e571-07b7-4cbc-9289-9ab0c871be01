{"permissions": {"allow": ["Bash(echo $HOME)", "<PERSON><PERSON>(python:*)", "Bash(timeout 10s python -m src.mcp_memory_service.server:*)", "<PERSON><PERSON>(uv:*)", "Bash(GTK_IM_MODULE=\"\" python -m src.mcp_memory_service.server --debug)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(chmod:*)", "<PERSON>sh(gh issue close:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(gh issue list:*)", "<PERSON><PERSON>(gh issue comment:*)", "WebFetch(domain:glama.ai)", "WebFetch(domain:alexgarcia.xyz)", "Bash(rg:*)", "Bash(/bin/bash -c \"git status\")", "Bash(ls:*)", "Bash(bash -c 'export PATH=\"\"/usr/bin:/bin:$PATH\"\" && git status')", "Bash(bash -l -c 'git status')", "Bash(pytest:*)", "Bash(docker build:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(uv run:*)", "Bash(npx @modelcontextprotocol/inspector uv:*)", "Bash(git commit:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(export MCP_MEMORY_STORAGE_BACKEND=sqlite_vec)", "Bash(./start_sqlite_vec.sh:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./setup_claude_mcp.sh:*)", "Bash(git fetch:*)", "Bash(git rebase:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "<PERSON>sh(start /B python:*)", "Bash(where uv)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git merge-base:*)", "Bash(git fetch:*)", "Bash(git rebase:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(setx:*)", "Bash(set MCP_MEMORY_STORAGE_BACKEND=sqlite_vec)", "Bash(set MCP_MEMORY_HTTP_AUTO_START=true)", "Bash(set MCP_HTTP_PORT=8000)", "Bash(cmd /c:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(git commit:*)", "<PERSON><PERSON>(memory:*)", "Bash(cp:*)", "mcp__memory-service__check_database_health", "mcp__memory-service__store_memory", "mcp__memory-service__retrieve_memory", "Bash(MCP_MEMORY_STORAGE_BACKEND=sqlite_vec timeout 10s uv run memory --debug)", "Bash(MCP_MEMORY_STORAGE_BACKEND=sqlite_vec timeout 10s python -m src.mcp_memory_service.server --debug)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(git tag:*)", "Bash(export MCP_CONSOLIDATION_ENABLED=true)", "Bash(export MCP_MDNS_ENABLED=true)", "Bash(export MCP_HTTPS_ENABLED=true)", "Bash(export MCP_MDNS_SERVICE_NAME=\"MCP Memory Service - Consolidated\")", "Bash(export MCP_HTTP_ENABLED=true)", "Bash(export:*)", "Bash(export MCP_HTTP_PORT=8000)", "Bash(export MCP_API_KEY=\"test-key-123\")", "<PERSON>sh(sudo ufw:*)", "Bash(./install_service.sh:*)", "Bash(sudo systemctl status:*)", "Bash(git tag:*)", "Bash(avahi-browse:*)", "<PERSON><PERSON>(avahi-resolve-host-name:*)", "Bash(systemctl status:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": []}