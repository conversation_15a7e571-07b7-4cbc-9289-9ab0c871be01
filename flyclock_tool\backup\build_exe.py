#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock Tool - EXE打包脚本
使用PyInstaller将FlyClock打包为独立的exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"🧹 清理文件: {spec_file}")
        os.remove(spec_file)

def create_default_sounds():
    """创建默认音频文件（如果不存在）"""
    sounds_dir = Path("flyclock_tool/assets/sounds")
    sounds_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建简单的提示音文件（使用pygame生成）
    try:
        import pygame
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        # 创建警告音（短促的哔声）
        warning_file = sounds_dir / "warning.wav"
        if not warning_file.exists():
            print("🎵 生成默认警告音...")
            # 这里可以生成简单的音频文件，暂时创建空文件
            warning_file.touch()
        
        # 创建结束音（较长的提示音）
        end_file = sounds_dir / "end.wav"
        if not end_file.exists():
            print("🎵 生成默认结束音...")
            end_file.touch()
            
    except ImportError:
        print("⚠️ pygame未安装，跳过音频文件生成")

def build_exe():
    """使用PyInstaller构建exe文件"""
    print("🚀 开始构建FlyClock.exe...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包为单个exe文件
        '--windowed',                   # 不显示控制台窗口
        '--name=FlyClock',              # 输出文件名
        '--icon=flyclock_tool/assets/icon.ico' if os.path.exists('flyclock_tool/assets/icon.ico') else '',
        '--add-data=flyclock_tool/assets;assets',  # 添加资源文件
        '--add-data=flyclock_tool/core;core',      # 添加核心模块
        '--add-data=flyclock_tool/gui;gui',        # 添加GUI模块
        '--hidden-import=tkinter',      # 确保tkinter被包含
        '--hidden-import=pygame',       # 确保pygame被包含
        '--hidden-import=win32gui',     # 确保win32gui被包含
        '--hidden-import=win32api',     # 确保win32api被包含
        '--hidden-import=win32con',     # 确保win32con被包含
        '--collect-all=pygame',         # 收集pygame的所有文件
        '--collect-all=tkinter',        # 收集tkinter的所有文件
        'flyclock_tool/main.py'         # 主程序文件
    ]
    
    # 过滤空参数
    cmd = [arg for arg in cmd if arg]
    
    print(f"📦 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败！")
        print(f"错误代码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False

def create_spec_file():
    """创建自定义的spec文件以获得更好的控制"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['flyclock_tool/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('flyclock_tool/assets', 'assets'),
        ('flyclock_tool/core', 'core'),
        ('flyclock_tool/gui', 'gui'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'pygame',
        'win32gui',
        'win32api',
        'win32con',
        'json',
        'threading',
        'time',
        'os',
        'pathlib',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FlyClock',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('FlyClock.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("📝 已创建FlyClock.spec文件")

def build_with_spec():
    """使用spec文件构建"""
    print("🚀 使用spec文件构建FlyClock.exe...")
    
    cmd = ['pyinstaller', 'FlyClock.spec']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败！")
        print(f"错误代码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False

def post_build_tasks():
    """构建后的任务"""
    exe_path = Path("dist/FlyClock.exe")
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"📦 生成的exe文件: {exe_path}")
        print(f"📏 文件大小: {file_size:.2f} MB")
        
        # 创建发布目录
        release_dir = Path("release")
        release_dir.mkdir(exist_ok=True)
        
        # 复制exe文件到发布目录
        release_exe = release_dir / "FlyClock.exe"
        shutil.copy2(exe_path, release_exe)
        
        # 复制说明文件
        readme_content = """# FlyClock - PPT/PDF全屏放映计时辅助工具

## 使用说明

1. 双击FlyClock.exe启动程序
2. 右键点击计时器窗口打开菜单
3. 选择"本地设置"配置计时器参数
4. 打开PPT或PDF文件并进入全屏模式
5. 程序自动检测并开始计时

## 功能特性

- 自动检测PPT/PDF全屏状态
- 悬浮计时器显示
- 音频提醒功能
- 可自定义外观
- 支持快捷键操作

## 系统要求

- Windows 10/11
- 无需安装Python环境

## 版本信息

- 版本: V1.0.0
- 构建时间: 2025-07-29

如有问题请联系开发者。
"""
        
        readme_file = release_dir / "使用说明.txt"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"📁 发布文件已准备完成: {release_dir}")
        return True
    else:
        print("❌ 未找到生成的exe文件")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🏗️  FlyClock Tool - EXE构建脚本")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists('flyclock_tool/main.py'):
        print("❌ 错误：请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 步骤1：清理构建目录
    clean_build_dirs()
    
    # 步骤2：创建默认音频文件
    create_default_sounds()
    
    # 步骤3：创建spec文件
    create_spec_file()
    
    # 步骤4：构建exe
    if build_with_spec():
        # 步骤5：后处理任务
        if post_build_tasks():
            print("\n🎉 FlyClock.exe构建完成！")
            print("📁 可执行文件位置: release/FlyClock.exe")
        else:
            print("\n❌ 后处理任务失败")
            sys.exit(1)
    else:
        print("\n❌ 构建失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
