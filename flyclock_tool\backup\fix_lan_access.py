#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复FlyClock局域网访问404问题的专用脚本
"""

import sys
import os
import socket
import threading
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler

def get_local_ip():
    """获取本机IP地址"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "127.0.0.1"

def main():
    print("🔧 修复FlyClock局域网访问404问题")
    print("=" * 50)
    
    # 配置
    http_port = 8080
    web_dir = Path(__file__).parent / "assets" / "web"
    
    print(f"📁 Web目录: {web_dir}")
    print(f"📁 目录存在: {web_dir.exists()}")
    
    if not web_dir.exists():
        print("❌ Web目录不存在")
        return
    
    # 显示文件列表
    files = list(web_dir.glob("*"))
    print(f"📄 Web文件: {[f.name for f in files]}")
    
    # 网络信息
    local_ip = get_local_ip()
    print(f"🌐 本机IP: {local_ip}")
    
    # 自定义HTTP请求处理器 - 专门解决404问题
    class FixedHTTPRequestHandler(SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            # 确保使用正确的目录
            super().__init__(*args, directory=str(web_dir), **kwargs)
        
        def log_message(self, format, *args):
            # 详细记录所有请求
            client_ip = self.client_address[0]
            print(f"📡 [{client_ip}] {format % args}")
        
        def do_GET(self):
            # 记录详细的GET请求信息
            print(f"🔍 GET请求详情:")
            print(f"   路径: {self.path}")
            print(f"   客户端: {self.client_address}")
            print(f"   工作目录: {self.directory}")
            
            # 检查文件是否存在
            if self.path == '/':
                # 根路径重定向到remote_client.html
                self.send_response(302)
                self.send_header('Location', '/remote_client.html')
                self.end_headers()
                return
            
            # 构建完整文件路径
            file_path = Path(self.directory) / self.path.lstrip('/')
            print(f"   完整路径: {file_path}")
            print(f"   文件存在: {file_path.exists()}")
            
            if file_path.exists():
                print(f"✅ 文件找到，正常处理")
            else:
                print(f"❌ 文件未找到: {file_path}")
                # 列出目录内容用于调试
                if Path(self.directory).exists():
                    dir_files = list(Path(self.directory).glob("*"))
                    print(f"   目录内容: {[f.name for f in dir_files]}")
            
            # 调用父类方法处理请求
            super().do_GET()
        
        def end_headers(self):
            # 添加CORS头部
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    try:
        print(f"\n🚀 启动修复版HTTP服务器...")
        
        # 创建服务器，确保绑定到所有接口
        httpd = HTTPServer(('0.0.0.0', http_port), FixedHTTPRequestHandler)
        
        print(f"✅ HTTP服务器启动成功")
        print(f"   端口: {http_port}")
        print(f"   绑定: 0.0.0.0 (所有网络接口)")
        print(f"   工作目录: {web_dir}")
        
        print(f"\n📱 访问地址:")
        print(f"   本地: http://localhost:{http_port}/remote_client.html")
        print(f"   局域网: http://{local_ip}:{http_port}/remote_client.html")
        
        print(f"\n🔧 如果仍然无法访问，请检查:")
        print(f"   1. 防火墙设置")
        print(f"   2. 网络连接")
        print(f"   3. 杀毒软件拦截")
        
        print(f"\n🛡️ 防火墙命令 (以管理员身份运行):")
        print(f"   netsh advfirewall firewall add rule name=\"FlyClock-{http_port}\" dir=in action=allow protocol=TCP localport={http_port}")
        
        print(f"\n⏰ 服务器运行中... 按 Ctrl+C 停止")
        
        # 启动服务器
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
