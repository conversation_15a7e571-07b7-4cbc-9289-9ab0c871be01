#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试窗口列表功能
"""

import pygetwindow as gw

def test_window_list():
    """测试获取窗口列表"""
    print("测试获取窗口列表...")
    
    try:
        # 获取所有窗口
        all_windows = gw.getAllWindows()
        print(f"总窗口数: {len(all_windows)}")
        
        # 过滤出有效窗口
        valid_windows = []
        current_title = "ALT+TAB 窗口切换工具"  # 假设的当前程序标题
        
        for window in all_windows:
            try:
                # 过滤条件
                if (window.title and 
                    not window.title.isspace() and 
                    window.title != current_title and  # 排除自身窗口
                    not window.isMinimized and 
                    window.visible and
                    window.width > 50 and 
                    window.height > 50 and
                    window.title not in ['Program Manager', 'Desktop']):  # 排除系统窗口
                    
                    # 获取程序名（从窗口标题推断或使用默认值）
                    program_name = get_program_name_from_window(window)
                    
                    valid_windows.append({
                        'window': window,
                        'title': window.title,
                        'program': program_name,
                        'size': f"{window.width}x{window.height}"
                    })
                    
            except Exception as e:
                print(f"处理窗口时出错: {e}")
                continue
        
        # 按窗口标题排序
        valid_windows.sort(key=lambda x: x['title'].lower())
        
        print(f"有效窗口数: {len(valid_windows)}")
        print(f"\n前15个有效窗口:")
        for i, win_data in enumerate(valid_windows[:15]):
            # 截断过长的标题
            display_title = win_data['title']
            if len(display_title) > 50:
                display_title = display_title[:47] + "..."
            
            print(f"  {i+1:2d}. {display_title:<50} | {win_data['program']:<15} | {win_data['size']}")
        
        return len(valid_windows) > 0
        
    except Exception as e:
        print(f"获取窗口列表失败: {e}")
        return False

def get_program_name_from_window(window):
    """从窗口获取程序名"""
    try:
        # 尝试从窗口标题推断程序名
        title = window.title.lower()
        
        # 常见程序的识别规则
        if 'chrome' in title or 'google' in title:
            return 'Chrome'
        elif 'firefox' in title:
            return 'Firefox'
        elif 'edge' in title:
            return 'Edge'
        elif 'notepad' in title or '记事本' in title:
            return 'Notepad'
        elif 'word' in title:
            return 'Word'
        elif 'excel' in title:
            return 'Excel'
        elif 'powerpoint' in title:
            return 'PowerPoint'
        elif 'code' in title or 'cursor' in title:
            return 'Code Editor'
        elif 'cmd' in title or 'powershell' in title:
            return 'Terminal'
        else:
            # 默认返回窗口标题的第一部分
            parts = window.title.split(' - ')
            if len(parts) > 1:
                return parts[-1][:15]  # 通常程序名在最后，限制长度
            else:
                return parts[0][:15]  # 限制长度
    except:
        return "Unknown"

def main():
    print("=== 窗口列表测试 ===")
    result = test_window_list()
    print(f"\n测试结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    main()