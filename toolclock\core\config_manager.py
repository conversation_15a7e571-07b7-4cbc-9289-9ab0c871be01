"""
配置管理模块
负责应用配置的加载、保存、获取和设置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "timer": {
            "default_duration": "00:08:00",  # 默认计时时长
            "mode": "countdown",  # countdown: 倒计时, countup: 正计时
            "auto_start": True,  # 自动开始计时
            "warning_time": 60,  # 提前警告时间(秒)
            "custom_durations": []  # 自定义时长列表
        },
        "display": {
            "position": {"x": 100, "y": 100},  # 窗口位置
            "size": {"width": 200, "height": 80},  # 窗口大小
            "font": {
                "family": "Arial",
                "size": 24,
                "bold": True
            },
            "colors": {
                "normal": {
                    "foreground": "#FFFFFF",  # 前景色(白色)
                    "background": "#4A90E2"   # 背景色(蓝色)
                },
                "warning": {
                    "foreground": "#FFFFFF",  # 前景色(白色)
                    "background": "#FF0000"   # 背景色(红色)
                }
            },
            "transparency": 0.9,  # 透明度 (0.0-1.0)
            "always_on_top": True,  # 总是置顶
            "show_on_taskbar": False,  # 在任务栏显示
            "show_controls": True,  # 显示控制栏
            "show_in_fullscreen": True,  # 在全屏时显示
            "hide_when_no_ppt": False,  # 没有PPT/PDF文件时隐藏
            "hide_when_timer_off": False  # 计时时长为零时隐藏
        },
        "audio": {
            "enabled": True,  # 启用音频提示
            "warning_enabled": True,  # 启用警告音
            "warning_sound": "",  # 警告音文件路径
            "warning_interval": 3,  # 持续警告音间隔(秒)
            "end_sound": "",  # 结束音文件路径
            "test_sound": True  # 测试音频
        },
        "control": {
            "hotkeys": {
                "pause_resume": "F3",  # 暂停/恢复快捷键
                "reset": "F4"  # 重置快捷键
            },
            "auto_exit_delay": 0,  # 自动退出延迟(秒)，0表示不自动退出
            "remind_break_time": True,  # 提醒闲时间
            "auto_pause_resume": True,  # 允许手动暂停、重置计时
            "timer_end_action": "exit_fullscreen",  # 计时结束行为: keep_fullscreen, exit_fullscreen, black_screen
            "auto_pause_on_exit": True  # 退出全屏时自动暂停计时
        },
        "remote": {
            "enabled": False,  # 启用远程控制
            "port": 8765,  # 远程控制端口
            "password": "",  # 远程控制密码
            "auto_start": False  # 自动启动远程服务
        },
        "appearance": {
            "quiet_mode": False,  # 安静模式
            "show_machine_time": False,  # 显示机器时间
            "show_hint_without_countdown": False,  # 提示时不显示倒计时
            "background_transparent": False  # 背景透明
        },
        "files": {
            "ppt_pdf_extensions": [".ppt", ".pptx", ".pdf"],  # 支持的文件扩展名
            "auto_detect_files": True,  # 自动检测文件
            "file_specific_timers": {}  # 文件特定计时器 {文件路径: 时长}
        }
    }
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置目录路径，默认为用户目录下的.flyclock
        """
        if config_dir is None:
            self.config_dir = Path.home() / ".flyclock"
        else:
            self.config_dir = Path(config_dir)
        
        self.config_file = self.config_dir / "config.json"
        self.config_dir.mkdir(exist_ok=True)
        
        self._config = {}
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 合并默认配置和用户配置
                self._config = self._merge_config(self.DEFAULT_CONFIG.copy(), user_config)
                logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                # 使用默认配置
                self._config = self.DEFAULT_CONFIG.copy()
                self.save_config()  # 创建默认配置文件
                logger.info("使用默认配置并创建配置文件")
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._config = self.DEFAULT_CONFIG.copy()
        
        return self._config
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            保存是否成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_setting(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key_path: 配置项路径，使用点号分隔，如 'timer.duration'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self._config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
        except Exception as e:
            logger.error(f"获取配置项失败 {key_path}: {e}")
            return default
    
    def set_setting(self, key_path: str, value: Any, save: bool = True) -> bool:
        """
        设置配置项
        
        Args:
            key_path: 配置项路径，使用点号分隔
            value: 配置值
            save: 是否立即保存到文件
            
        Returns:
            设置是否成功
        """
        try:
            keys = key_path.split('.')
            config = self._config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            
            if save:
                return self.save_config()
            
            return True
        except Exception as e:
            logger.error(f"设置配置项失败 {key_path}: {e}")
            return False
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置
        
        Returns:
            配置字典
        """
        return self._config.copy()
    
    def update_config(self, config_dict: Dict[str, Any], save: bool = True) -> bool:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
            save: 是否立即保存
            
        Returns:
            更新是否成功
        """
        try:
            self._config = self._merge_config(self._config, config_dict)
            
            if save:
                return self.save_config()
            
            return True
        except Exception as e:
            logger.error(f"批量更新配置失败: {e}")
            return False
    
    def reset_to_default(self, save: bool = True) -> bool:
        """
        重置为默认配置
        
        Args:
            save: 是否立即保存
            
        Returns:
            重置是否成功
        """
        try:
            self._config = self.DEFAULT_CONFIG.copy()
            
            if save:
                return self.save_config()
            
            return True
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            return False
    
    def export_config(self, file_path: str) -> bool:
        """
        导出配置到文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置导出成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str, save: bool = True) -> bool:
        """
        从文件导入配置
        
        Args:
            file_path: 导入文件路径
            save: 是否立即保存
            
        Returns:
            导入是否成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并导入的配置
            self._config = self._merge_config(self.DEFAULT_CONFIG.copy(), imported_config)
            
            if save:
                return self.save_config()
            
            logger.info(f"配置导入成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def _merge_config(self, base_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置字典
        
        Args:
            base_config: 基础配置
            user_config: 用户配置
            
        Returns:
            合并后的配置
        """
        result = base_config.copy()
        
        for key, value in user_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_config_file_path(self) -> str:
        """
        获取配置文件路径
        
        Returns:
            配置文件路径
        """
        return str(self.config_file)
    
    def backup_config(self, backup_path: Optional[str] = None) -> bool:
        """
        备份配置文件
        
        Args:
            backup_path: 备份文件路径，默认为配置目录下的config_backup.json
            
        Returns:
            备份是否成功
        """
        try:
            if backup_path is None:
                backup_path = self.config_dir / "config_backup.json"
            
            return self.export_config(str(backup_path))
        except Exception as e:
            logger.error(f"备份配置失败: {e}")
            return False


# 全局配置管理器实例
config_manager = ConfigManager()