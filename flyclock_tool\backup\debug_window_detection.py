#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口检测调试脚本
用于诊断PPT/PDF全屏检测问题
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import win32gui
    import win32process
    import win32api
    import win32con
    HAS_WIN32 = True
    print("✓ win32gui模块可用")
except ImportError:
    HAS_WIN32 = False
    print("✗ win32gui模块不可用，请安装pywin32")
    sys.exit(1)

from core.config_manager import config_manager

def get_window_info(hwnd):
    """获取窗口详细信息"""
    try:
        # 基本窗口信息
        window_class = win32gui.GetClassName(hwnd)
        window_title = win32gui.GetWindowText(hwnd)
        window_rect = win32gui.GetWindowRect(hwnd)
        
        # 进程信息
        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
        process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, process_id)
        process_name = win32process.GetModuleFileNameEx(process_handle, 0)
        process_name = os.path.basename(process_name)
        win32api.CloseHandle(process_handle)
        
        # 窗口状态
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_enabled = win32gui.IsWindowEnabled(hwnd)
        
        return {
            'hwnd': hwnd,
            'class': window_class,
            'title': window_title,
            'rect': window_rect,
            'process': process_name,
            'visible': is_visible,
            'enabled': is_enabled
        }
    except Exception as e:
        return {'error': str(e)}

def is_fullscreen_window(hwnd):
    """判断窗口是否为全屏"""
    try:
        # 获取窗口矩形
        window_rect = win32gui.GetWindowRect(hwnd)
        
        if not isinstance(window_rect, (tuple, list)) or len(window_rect) != 4:
            return False, f"GetWindowRect返回异常值: {window_rect}"
        
        left, top, right, bottom = window_rect
        # 强制转换为整数，处理可能的字符串或浮点数
        left = int(float(str(left)))
        top = int(float(str(top)))
        right = int(float(str(right)))
        bottom = int(float(str(bottom)))
        
        # 计算窗口大小
        window_width = right - left
        window_height = bottom - top
        
        if window_width <= 0 or window_height <= 0:
            return False, f"窗口大小无效: {window_width}x{window_height}"
        
        # 获取屏幕分辨率
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        
        # 判断窗口是否占满屏幕（允许小幅偏差）
        width_match = abs(window_width - screen_width) <= 10
        height_match = abs(window_height - screen_height) <= 10
        position_match = abs(left) <= 10 and abs(top) <= 10
        
        is_fullscreen = width_match and height_match and position_match
        
        details = {
            'window_size': f"{window_width}x{window_height}",
            'screen_size': f"{screen_width}x{screen_height}",
            'position': f"({left},{top})",
            'width_match': width_match,
            'height_match': height_match,
            'position_match': position_match,
            'is_fullscreen': is_fullscreen
        }
        
        return is_fullscreen, details
        
    except Exception as e:
        return False, f"检测全屏失败: {e}"

def is_presentation_window(window_class, process_name, window_title):
    """判断是否为演示相关窗口"""
    # PPT/PDF相关的窗口类名和进程名
    ppt_classes = [
        'PPTFrameClass', 'PP12FrameClass', 'PP11FrameClass', 'PP10FrameClass',
        'screenClass', 'paneClassDC'
    ]
    
    pdf_classes = [
        'AcrobatSDIWindow', 'AdobeAcrobat', 'FoxitReader', 'SumatraPDF', 'SUMATRA_PDF_FRAME'
    ]
    
    ppt_processes = [
        'POWERPNT.EXE', 'PPTVIEW.EXE', 'wpp.exe', 'et.exe'
    ]
    
    pdf_processes = [
        'AcroRd32.exe', 'Acrobat.exe', 'FoxitReader.exe', 'SumatraPDF.exe', 'PDFXCview.exe'
    ]
    
    supported_extensions = ['.ppt', '.pptx', '.pdf', '.pps', '.ppsx']
    
    # 检查窗口类名
    for ppt_class in ppt_classes:
        if ppt_class.lower() in window_class.lower():
            return True, f"匹配PPT窗口类: {ppt_class}"
    
    for pdf_class in pdf_classes:
        if pdf_class.lower() in window_class.lower():
            return True, f"匹配PDF窗口类: {pdf_class}"
    
    # 检查进程名
    process_name_upper = process_name.upper()
    for ppt_process in ppt_processes:
        if ppt_process.upper() == process_name_upper:
            return True, f"匹配PPT进程: {ppt_process}"
    
    for pdf_process in pdf_processes:
        if pdf_process.upper() == process_name_upper:
            return True, f"匹配PDF进程: {pdf_process}"
    
    # 检查窗口标题中是否包含支持的文件扩展名
    window_title_lower = window_title.lower()
    for ext in supported_extensions:
        if ext.lower() in window_title_lower:
            return True, f"标题包含扩展名: {ext}"
    
    # 检查是否包含演示相关关键词
    presentation_keywords = [
        'powerpoint', 'ppt', 'presentation', 'slideshow',
        'pdf', 'acrobat', 'reader', 'foxit', 'sumatra',
        '演示', '幻灯片', '放映'
    ]
    
    for keyword in presentation_keywords:
        if keyword in window_title_lower:
            return True, f"标题包含关键词: {keyword}"
    
    return False, "未匹配任何演示窗口特征"

def debug_current_window():
    """调试当前前台窗口"""
    print("\n" + "="*60)
    print("调试当前前台窗口")
    print("="*60)
    
    try:
        # 获取前台窗口
        foreground_window = win32gui.GetForegroundWindow()
        if not foreground_window:
            print("✗ 无法获取前台窗口")
            return
        
        print(f"前台窗口句柄: {foreground_window}")
        
        # 获取窗口信息
        window_info = get_window_info(foreground_window)
        if 'error' in window_info:
            print(f"✗ 获取窗口信息失败: {window_info['error']}")
            return
        
        print(f"窗口类名: {window_info['class']}")
        print(f"窗口标题: {window_info['title']}")
        print(f"窗口矩形: {window_info['rect']}")
        print(f"进程名: {window_info['process']}")
        print(f"可见: {window_info['visible']}")
        print(f"启用: {window_info['enabled']}")
        
        # 检查是否全屏
        is_fullscreen, fullscreen_details = is_fullscreen_window(foreground_window)
        print(f"\n全屏检测结果: {is_fullscreen}")
        if isinstance(fullscreen_details, dict):
            for key, value in fullscreen_details.items():
                print(f"  {key}: {value}")
        else:
            print(f"  详情: {fullscreen_details}")
        
        # 检查是否为演示窗口
        is_presentation, presentation_reason = is_presentation_window(
            window_info['class'], window_info['process'], window_info['title']
        )
        print(f"\n演示窗口检测结果: {is_presentation}")
        print(f"  原因: {presentation_reason}")
        
        # 综合判断
        should_start_timer = is_fullscreen and is_presentation
        print(f"\n应该启动计时器: {should_start_timer}")
        
        if should_start_timer:
            print("✓ 满足自动启动计时器的条件")
        else:
            print("✗ 不满足自动启动计时器的条件")
            if not is_fullscreen:
                print("  - 窗口不是全屏")
            if not is_presentation:
                print("  - 窗口不是演示窗口")
        
    except Exception as e:
        print(f"✗ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("ToolClock 窗口检测调试工具")
    print("请打开PPT或PDF文件并进入全屏模式，然后按回车键进行检测...")
    
    while True:
        try:
            input("\n按回车键检测当前窗口（输入 'q' 退出）: ")
            user_input = input().strip().lower()
            if user_input == 'q':
                break
            
            debug_current_window()
            
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
