<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlyClock 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FlyClock 远程控制连接测试</h1>
        
        <div id="status" class="status info">
            准备测试连接...
        </div>
        
        <div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <div id="log"></div>
    </div>

    <script>
        let ws = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testConnection() {
            log('开始连接测试...');
            
            // 获取当前页面的主机和端口
            const host = window.location.hostname || 'localhost';
            const httpPort = parseInt(window.location.port) || 6665;
            const wsPort = httpPort + 1; // WebSocket端口 = HTTP端口 + 1
            
            log(`HTTP端口: ${httpPort}`);
            log(`WebSocket端口: ${wsPort}`);
            log(`主机: ${host}`);
            
            const wsUrl = `ws://${host}:${wsPort}`;
            log(`尝试连接到: ${wsUrl}`);
            
            updateStatus('连接中...', 'info');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket连接成功！');
                    updateStatus('连接成功！', 'success');
                    
                    // 发送测试消息
                    const testMessage = {
                        type: 'ping',
                        timestamp: Date.now()
                    };
                    ws.send(JSON.stringify(testMessage));
                    log('发送测试消息: ' + JSON.stringify(testMessage));
                };
                
                ws.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'pong') {
                            log('✅ 服务器响应正常！');
                        }
                    } catch (e) {
                        log('消息解析失败: ' + e.message);
                    }
                };
                
                ws.onerror = function(error) {
                    log('❌ WebSocket错误: ' + error);
                    updateStatus('连接错误', 'error');
                };
                
                ws.onclose = function(event) {
                    log(`连接关闭: 代码=${event.code}, 原因=${event.reason}`);
                    updateStatus('连接已关闭', 'error');
                };
                
            } catch (error) {
                log('❌ 连接异常: ' + error.message);
                updateStatus('连接失败', 'error');
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成');
            log('当前URL: ' + window.location.href);
        };
    </script>
</body>
</html>
