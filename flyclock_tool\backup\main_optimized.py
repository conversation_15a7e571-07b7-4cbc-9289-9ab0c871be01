#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock Tool - 完全优化版本
解决全屏放映倒计时问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
import time
from pathlib import Path

# 禁用字节码缓存
sys.dont_write_bytecode = True

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 强制清理所有相关模块
modules_to_remove = []
for module_name in list(sys.modules.keys()):
    if any(module_name.startswith(prefix) for prefix in ['core.', 'gui.', 'remote.', 'window_detector']):
        modules_to_remove.append(module_name)

for module_name in modules_to_remove:
    if module_name in sys.modules:
        del sys.modules[module_name]

print("已强制清理所有相关模块缓存")

# 现在导入配置管理器
from core.config_manager import config_manager

class OptimizedTimerWindow:
    """完全优化的计时器窗口"""
    
    def __init__(self):
        self.is_running = False
        self.is_paused = False
        self.current_time = 480  # 8分钟
        self.total_time = 480
        
        # 创建窗口
        self.window = tk.Tk()
        self.window.title("FlyClock - 优化版")
        self.window.geometry("200x80+100+100")
        self.window.attributes('-topmost', True)
        self.window.overrideredirect(True)
        
        # 时间显示
        self.time_label = tk.Label(
            self.window,
            text="08:00",
            font=("Arial", 24, "bold"),
            fg="#FFFFFF",
            bg="#4A90E2"
        )
        self.time_label.pack(expand=True)
        
        # 绑定拖拽事件
        self.time_label.bind("<Button-1>", self.start_drag)
        self.time_label.bind("<B1-Motion>", self.on_drag)
        self.time_label.bind("<ButtonRelease-1>", self.end_drag)
        
        # 绑定右键菜单
        self.time_label.bind("<Button-3>", self.show_menu)
        
        # 创建右键菜单
        self.menu = tk.Menu(self.window, tearoff=0)
        self.menu.add_command(label="开始计时", command=self.start_timer)
        self.menu.add_command(label="暂停计时", command=self.pause_timer)
        self.menu.add_command(label="重置计时", command=self.reset_timer)
        self.menu.add_separator()
        self.menu.add_command(label="退出", command=self.quit_app)
        
        # 启动优化版窗口检测
        self.start_window_detection()
        
        print("✓ 优化版计时器窗口创建成功")
    
    def start_window_detection(self):
        """启动优化版窗口检测"""
        try:
            # 使用修复版的窗口检测器
            from window_detector_fixed import get_window_detector_fixed
            self.window_detector = get_window_detector_fixed(self)
            
            if self.window_detector.start_detection():
                print("✓ 优化版PPT/PDF全屏检测已启动")
            else:
                print("✗ 优化版PPT/PDF全屏检测启动失败")
        except Exception as e:
            print(f"✗ 启动优化版窗口检测失败: {e}")
            import traceback
            traceback.print_exc()
    
    def start_timer(self):
        """开始计时"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            print("✓ 优化版计时器已启动")
            threading.Thread(target=self.timer_loop, daemon=True).start()
    
    def pause_timer(self):
        """暂停计时"""
        if self.is_running:
            self.is_paused = not self.is_paused
            if self.is_paused:
                print("✓ 优化版计时器已暂停")
            else:
                print("✓ 优化版计时器已恢复")
    
    def reset_timer(self):
        """重置计时"""
        self.is_running = False
        self.is_paused = False
        self.current_time = self.total_time
        self.update_display()
        print("✓ 优化版计时器已重置")
    
    def set_duration(self, duration_str):
        """设置计时时长"""
        print(f"✓ 设置计时时长: {duration_str}")
    
    def timer_loop(self):
        """计时循环"""
        while self.is_running and self.current_time > 0:
            if not self.is_paused:
                self.current_time -= 1
                self.window.after(0, self.update_display)
            time.sleep(1)
        
        if self.current_time <= 0:
            print("⏰ 计时结束！")
            self.is_running = False
    
    def update_display(self):
        """更新时间显示"""
        minutes = self.current_time // 60
        seconds = self.current_time % 60
        time_str = f"{minutes:02d}:{seconds:02d}"
        self.time_label.configure(text=time_str)
        
        # 根据时间改变颜色
        if self.current_time <= 0:
            self.time_label.configure(bg="#FF0000")  # 红色
        elif self.current_time <= 60:
            self.time_label.configure(bg="#FF6B6B")  # 警告色
        else:
            self.time_label.configure(bg="#4A90E2")  # 正常色
    
    def start_drag(self, event):
        """开始拖拽"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
    
    def on_drag(self, event):
        """拖拽中"""
        x = self.window.winfo_x() + event.x - self.drag_start_x
        y = self.window.winfo_y() + event.y - self.drag_start_y
        self.window.geometry(f"+{x}+{y}")
    
    def end_drag(self, event):
        """结束拖拽"""
        pass
    
    def show_menu(self, event):
        """显示右键菜单"""
        try:
            self.menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.menu.grab_release()
    
    def quit_app(self):
        """退出应用"""
        if messagebox.askyesno("确认", "确定要退出 FlyClock 吗？"):
            self.window.quit()
    
    def run(self):
        """运行窗口"""
        print("✓ 优化版计时器窗口开始运行...")
        self.window.mainloop()

def main():
    """主函数"""
    print("=" * 60)
    print("FlyClock - 完全优化版本")
    print("版本: V1.0.2 - 解决全屏放映倒计时问题")
    print("=" * 60)
    
    print("\n🔧 优化内容:")
    print("   ✅ 使用修复版窗口检测器（window_detector_fixed.py）")
    print("   ✅ 强制清理模块缓存，避免旧代码干扰")
    print("   ✅ 完全独立的启动流程")
    print("   ✅ 增强的错误处理和调试信息")
    
    print("\n📋 使用说明:")
    print("   1. 打开PPT或PDF文件")
    print("   2. 按F5进入全屏模式")
    print("   3. 程序将自动检测并开始倒计时")
    print("   4. 右键点击计时器可打开菜单")
    print("   5. 可以拖拽移动计时器位置")
    
    print("\n" + "-" * 60)
    
    try:
        # 创建并运行优化版计时器窗口
        timer_window = OptimizedTimerWindow()
        timer_window.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"✗ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
