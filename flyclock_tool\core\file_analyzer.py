#!/usr/bin/env python3
"""
文件分析器 - 智能分析PPT/PDF文件并建议计时时间
"""

import os
import logging
from pathlib import Path
from typing import Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class FileAnalyzer:
    """文件分析器类"""
    
    def __init__(self):
        """初始化文件分析器"""
        self.supported_extensions = {'.pptx', '.ppt', '.pdf'}
        
        # 计时建议配置（秒）
        self.timing_config = {
            'ppt': {
                'per_slide_min': 120,  # 每张幻灯片最少2分钟
                'per_slide_max': 180,  # 每张幻灯片最多3分钟
                'base_time': 300,      # 基础时间5分钟
            },
            'pdf': {
                'per_page_min': 60,    # 每页最少1分钟
                'per_page_max': 120,   # 每页最多2分钟
                'base_time': 300,      # 基础时间5分钟
            }
        }
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """
        分析文件并返回分析结果
        
        Args:
            file_path: 文件路径
            
        Returns:
            分析结果字典，包含页数、建议时间等信息
        """
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return self._get_default_result()
        
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext not in self.supported_extensions:
            logger.warning(f"不支持的文件类型: {file_ext}")
            return self._get_default_result()
        
        try:
            if file_ext in ['.pptx', '.ppt']:
                return self._analyze_ppt(file_path)
            elif file_ext == '.pdf':
                return self._analyze_pdf(file_path)
        except Exception as e:
            logger.error(f"分析文件失败 {file_path}: {e}")
            return self._get_default_result()
        
        return self._get_default_result()
    
    def _analyze_ppt(self, file_path: str) -> Dict[str, Any]:
        """分析PPT文件"""
        prs = None
        try:
            from pptx import Presentation

            prs = Presentation(file_path)
            slide_count = len(prs.slides)

            # 计算建议时间
            suggested_time = self._calculate_ppt_time(slide_count)

            logger.info(f"PPT分析完成: {slide_count}张幻灯片, 建议时间: {suggested_time}秒")

            result = {
                'file_type': 'ppt',
                'page_count': slide_count,
                'suggested_time': suggested_time,
                'analysis_success': True,
                'details': {
                    'slides': slide_count,
                    'time_per_slide': suggested_time // slide_count if slide_count > 0 else 0
                }
            }

            # 显式清理资源
            prs = None
            return result

        except ImportError:
            logger.error("python-pptx库未安装，无法分析PPT文件")
            return self._get_default_result()
        except Exception as e:
            logger.error(f"分析PPT文件失败: {e}")
            return self._get_default_result()
        finally:
            # 确保资源被释放
            if prs is not None:
                try:
                    prs = None
                except:
                    pass
    
    def _analyze_pdf(self, file_path: str) -> Dict[str, Any]:
        """分析PDF文件"""
        reader = None
        try:
            from pypdf import PdfReader

            reader = PdfReader(file_path)
            page_count = len(reader.pages)

            # 计算建议时间
            suggested_time = self._calculate_pdf_time(page_count)

            logger.info(f"PDF分析完成: {page_count}页, 建议时间: {suggested_time}秒")

            result = {
                'file_type': 'pdf',
                'page_count': page_count,
                'suggested_time': suggested_time,
                'analysis_success': True,
                'details': {
                    'pages': page_count,
                    'time_per_page': suggested_time // page_count if page_count > 0 else 0
                }
            }

            # 显式清理资源
            reader = None
            return result

        except ImportError:
            logger.error("pypdf库未安装，无法分析PDF文件")
            return self._get_default_result()
        except Exception as e:
            logger.error(f"分析PDF文件失败: {e}")
            return self._get_default_result()
        finally:
            # 确保资源被释放
            if reader is not None:
                try:
                    reader = None
                except:
                    pass
    
    def _calculate_ppt_time(self, slide_count: int) -> int:
        """计算PPT建议时间"""
        if slide_count <= 0:
            return self.timing_config['ppt']['base_time']
        
        # 根据幻灯片数量计算时间
        if slide_count <= 5:
            time_per_slide = self.timing_config['ppt']['per_slide_max']
        elif slide_count <= 15:
            time_per_slide = (self.timing_config['ppt']['per_slide_min'] + 
                            self.timing_config['ppt']['per_slide_max']) // 2
        else:
            time_per_slide = self.timing_config['ppt']['per_slide_min']
        
        total_time = slide_count * time_per_slide
        
        # 确保最少有基础时间
        return max(total_time, self.timing_config['ppt']['base_time'])
    
    def _calculate_pdf_time(self, page_count: int) -> int:
        """计算PDF建议时间"""
        if page_count <= 0:
            return self.timing_config['pdf']['base_time']
        
        # 根据页数计算时间
        if page_count <= 10:
            time_per_page = self.timing_config['pdf']['per_page_max']
        elif page_count <= 30:
            time_per_page = (self.timing_config['pdf']['per_page_min'] + 
                           self.timing_config['pdf']['per_page_max']) // 2
        else:
            time_per_page = self.timing_config['pdf']['per_page_min']
        
        total_time = page_count * time_per_page
        
        # 确保最少有基础时间
        return max(total_time, self.timing_config['pdf']['base_time'])
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认分析结果"""
        return {
            'file_type': 'unknown',
            'page_count': 0,
            'suggested_time': 1800,  # 默认30分钟
            'analysis_success': False,
            'details': {}
        }
    
    def format_time(self, seconds: int) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds == 0:
                return f"{minutes}分钟"
            else:
                return f"{minutes}分{remaining_seconds}秒"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes == 0:
                return f"{hours}小时"
            else:
                return f"{hours}小时{remaining_minutes}分钟"


# 全局文件分析器实例
file_analyzer = FileAnalyzer()


def analyze_presentation_file(file_path: str) -> Dict[str, Any]:
    """
    分析演示文件的便捷函数
    
    Args:
        file_path: 文件路径
        
    Returns:
        分析结果
    """
    return file_analyzer.analyze_file(file_path)


def get_suggested_timer_duration(file_path: str) -> int:
    """
    获取建议的计时器时长
    
    Args:
        file_path: 文件路径
        
    Returns:
        建议的时长（秒）
    """
    result = analyze_presentation_file(file_path)
    return result.get('suggested_time', 1800)


if __name__ == "__main__":
    # 测试代码
    import sys
    
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
        result = analyze_presentation_file(test_file)
        print(f"分析结果: {result}")
        print(f"建议时间: {file_analyzer.format_time(result['suggested_time'])}")
    else:
        print("用法: python file_analyzer.py <文件路径>")
