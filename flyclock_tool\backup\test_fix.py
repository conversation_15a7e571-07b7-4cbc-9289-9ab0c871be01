#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import win32gui
    import win32api
    import win32con
    print("✓ win32gui模块可用")
except ImportError:
    print("✗ win32gui模块不可用")
    sys.exit(1)

def test_type_conversion():
    """测试类型转换修复"""
    print("\n测试类型转换修复...")
    
    # 模拟可能的返回值类型
    test_cases = [
        (0, 0, 1920, 1080),      # 正常整数
        ('0', '0', '1920', '1080'),  # 字符串
        (0.0, 0.0, 1920.0, 1080.0),  # 浮点数
    ]
    
    for i, window_rect in enumerate(test_cases):
        try:
            left, top, right, bottom = window_rect
            # 使用修复后的转换逻辑
            left = int(float(str(left)))
            top = int(float(str(top)))
            right = int(float(str(right)))
            bottom = int(float(str(bottom)))
            
            print(f"✓ 测试用例 {i+1}: {window_rect} -> ({left}, {top}, {right}, {bottom})")
        except Exception as e:
            print(f"✗ 测试用例 {i+1} 失败: {e}")

def test_current_window():
    """测试当前窗口检测"""
    print("\n测试当前窗口检测...")
    
    try:
        # 获取前台窗口
        foreground_window = win32gui.GetForegroundWindow()
        if not foreground_window:
            print("✗ 无法获取前台窗口")
            return
        
        # 获取窗口矩形
        window_rect = win32gui.GetWindowRect(foreground_window)
        print(f"原始窗口矩形: {window_rect} (类型: {type(window_rect[0])})")
        
        # 使用修复后的转换逻辑
        left, top, right, bottom = window_rect
        left = int(float(str(left)))
        top = int(float(str(top)))
        right = int(float(str(right)))
        bottom = int(float(str(bottom)))
        
        print(f"转换后坐标: ({left}, {top}, {right}, {bottom})")
        
        # 计算窗口大小
        window_width = right - left
        window_height = bottom - top
        print(f"窗口大小: {window_width}x{window_height}")
        
        # 获取屏幕分辨率
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        print(f"屏幕大小: {screen_width}x{screen_height}")
        
        # 判断是否全屏
        width_match = abs(window_width - screen_width) <= 10
        height_match = abs(window_height - screen_height) <= 10
        position_match = abs(left) <= 10 and abs(top) <= 10
        
        print(f"全屏检测: 宽度匹配={width_match}, 高度匹配={height_match}, 位置匹配={position_match}")
        
        is_fullscreen = width_match and height_match and position_match
        print(f"是否全屏: {is_fullscreen}")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("FlyClock 修复效果测试")
    print("="*40)
    
    test_type_conversion()
    test_current_window()
    
    print("\n测试完成！")
