#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试窗口切换功能
"""

import pygetwindow as gw
import time

def test_window_switching():
    """测试窗口切换"""
    print("测试窗口切换功能...")
    
    try:
        # 获取所有窗口
        windows = gw.getAllWindows()
        
        # 过滤出有标题的可见窗口
        visible_windows = []
        for window in windows:
            if (window.title and 
                not window.title.isspace() and 
                not window.isMinimized and 
                window.visible and
                window.width > 100 and 
                window.height > 100):
                visible_windows.append(window)
        
        print(f"找到 {len(visible_windows)} 个可见窗口:")
        for i, window in enumerate(visible_windows[:10]):
            print(f"  {i+1}. {window.title[:50]}")
        
        # 测试切换前3个窗口
        if len(visible_windows) >= 2:
            print(f"\n开始测试切换...")
            for i in range(min(3, len(visible_windows))):
                window = visible_windows[i]
                print(f"切换到: {window.title[:30]}")
                try:
                    window.activate()
                    time.sleep(2)  # 等待2秒
                except Exception as e:
                    print(f"  切换失败: {e}")
            
            print("窗口切换测试完成")
            return True
        else:
            print("可见窗口不足，无法测试切换")
            return False
            
    except Exception as e:
        print(f"窗口切换测试失败: {e}")
        return False

def main():
    print("=== 窗口切换测试 ===")
    result = test_window_switching()
    print(f"\n测试结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    main()