# 窗口切换工具

一个简洁高效的Windows窗口管理工具，支持快速切换和管理多个应用程序窗口。

## 功能特性

- 🖥️ **窗口列表显示**：以表格形式显示所有打开的窗口
- 📋 **详细信息**：显示窗口标题、程序名和进程名
- ✅ **批量选择**：支持多选窗口进行批量操作
- 🔄 **快速切换**：一键切换到选中的窗口
- 🎯 **智能过滤**：自动过滤系统窗口，只显示用户窗口
- 🎨 **现代界面**：使用ttkbootstrap提供美观的现代化界面

## 使用方法

### 运行程序
1. **开发环境运行**：
   ```bash
   cd process
   python main.py
   ```

2. **独立exe运行**：
   - 直接双击 `dist/窗口切换工具.exe`
   - 或在命令行中运行：`.\窗口切换工具.exe`

### 界面操作
1. **查看窗口列表**：程序启动后自动显示所有打开的窗口
2. **选择窗口**：勾选要操作的窗口复选框
3. **切换窗口**：点击"切换到选中窗口"按钮
4. **刷新列表**：点击"刷新窗口列表"按钮更新窗口信息
5. **批量选择**：使用"全选"和"取消全选"按钮

### 表格列说明
- **选择**：复选框，用于选择要操作的窗口
- **窗口标题**：显示窗口的完整标题
- **程序名**：显示应用程序的名称
- **进程名**：显示具体的进程文件名

## 系统要求

- Windows 10/11
- 无需安装Python环境（exe版本）

## 依赖库（开发环境）

```
ttkbootstrap>=1.10.1    # 现代化UI框架
pygetwindow>=0.0.9      # 窗口管理
psutil>=5.9.0           # 进程信息
pywin32>=306            # Windows API
pillow>=9.0.0           # 图像处理
```

## 构建exe文件

如需重新构建exe文件：

```bash
cd process
python build_exe.py
```

构建完成后，exe文件位于 `dist/窗口切换工具.exe`

## 文件结构

```
process/
├── main.py                 # 主程序文件
├── build_exe.py           # exe构建脚本
├── requirements.txt       # 依赖列表
├── README.md             # 使用说明
└── dist/
    └── 窗口切换工具.exe   # 独立可执行文件
```

## 特色功能

### 智能窗口过滤
- 自动过滤掉系统窗口和隐藏窗口
- 只显示用户可见的应用程序窗口
- 支持最小化窗口的显示和切换

### 进程信息显示
- 显示窗口对应的进程名称
- 帮助识别相同程序的不同窗口
- 便于进行精确的窗口管理

### 现代化界面
- 使用cosmo主题，界面简洁美观
- 表格式布局，信息显示清晰
- 响应式设计，支持窗口缩放

## 注意事项

1. 程序需要管理员权限来访问某些系统窗口
2. 部分系统保护的窗口可能无法切换
3. 建议定期刷新窗口列表以获取最新状态

## 版本信息

- 版本：1.0.0
- 开发语言：Python 3.13
- UI框架：ttkbootstrap
- 打包工具：PyInstaller

---

**享受高效的窗口管理体验！** 🚀