#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证ALT+TAB功能和进程列表
"""

import psutil
import pyautogui
import os

def test_alt_tab():
    """测试ALT+TAB功能"""
    print("测试ALT+TAB功能...")
    try:
        pyautogui.hotkey('alt', 'tab')
        print("ALT+TAB发送成功")
        return True
    except Exception as e:
        print(f"ALT+TAB发送失败: {e}")
        return False

def test_process_list():
    """测试进程列表获取和过滤"""
    print("\n测试进程列表获取...")
    current_pid = os.getpid()
    current_name = None
    
    try:
        current_proc = psutil.Process(current_pid)
        current_name = current_proc.name()
        print(f"当前进程: {current_name} (PID: {current_pid})")
    except:
        current_name = "python.exe"
    
    processes = []
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            name = proc.info['name']
            pid = proc.info['pid']
            
            # 过滤条件：exe文件，排除自身
            if (name and name.lower().endswith('.exe') and 
                pid != current_pid and name != current_name):
                processes.append({'name': name, 'pid': pid})
        except:
            continue
    
    # 去重
    seen_names = set()
    unique_processes = []
    for proc in sorted(processes, key=lambda x: x['name'].lower()):
        if proc['name'] not in seen_names:
            seen_names.add(proc['name'])
            unique_processes.append(proc)
    
    print(f"找到 {len(unique_processes)} 个唯一进程")
    print("前10个进程:")
    for i, proc in enumerate(unique_processes[:10]):
        print(f"  {i+1}. {proc['name']} (PID: {proc['pid']})")
    
    return len(unique_processes) > 0

def main():
    print("=== ALT+TAB工具简单测试 ===")
    
    # 测试ALT+TAB
    alt_tab_ok = test_alt_tab()
    
    # 测试进程列表
    process_ok = test_process_list()
    
    print(f"\n=== 测试结果 ===")
    print(f"ALT+TAB功能: {'正常' if alt_tab_ok else '异常'}")
    print(f"进程列表获取: {'正常' if process_ok else '异常'}")

    if alt_tab_ok and process_ok:
        print("所有核心功能正常，可以启动主程序")
    else:
        print("存在问题，需要检查")

if __name__ == "__main__":
    main()