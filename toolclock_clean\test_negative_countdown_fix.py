#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试二次倒计时（负数倒计时）修复效果
验证问题：计时结束 → 退出全屏 → 点击确定 → 重新进入全屏 → 应该显示红色背景负数倒计时
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from gui.timer_window import TimerWindow

def test_negative_countdown_fix():
    """测试二次倒计时修复"""
    print("=" * 80)
    print("测试二次倒计时（负数倒计时）修复效果")
    print("=" * 80)
    
    # 创建计时器窗口
    timer_window = TimerWindow()
    print("✓ 计时器窗口已创建")
    
    # 测试场景：模拟完整的二次倒计时流程
    print("\n🎯 测试场景：完整的二次倒计时流程")
    print("步骤1：设置短时间计时（2秒）")
    timer_window.set_duration("00:02")
    print(f"   当前时间: {timer_window.current_time}秒")
    print(f"   警告状态: {timer_window.is_warning}")
    print(f"   确认状态: {timer_window.timer_finished_confirmed}")
    
    # 启动计时器
    print("\n步骤2：启动计时器")
    timer_window.start_timer()
    print("   计时器已启动")
    
    # 等待计时器结束
    print("\n步骤3：等待计时器结束...")
    time.sleep(3)  # 等待计时器结束
    print(f"   计时结束后 - 当前时间: {timer_window.current_time}秒")
    print(f"   确认状态: {timer_window.timer_finished_confirmed}")
    
    # 模拟用户点击确定
    print("\n步骤4：模拟用户点击确定")
    timer_window.timer_finished_confirmed = True
    print(f"   设置确认状态: {timer_window.timer_finished_confirmed}")
    
    # 强制更新显示为红色背景
    timer_window.update_display()
    print("   已更新显示为红色背景和负数时间")
    
    # 等待一下让负数倒计时运行
    time.sleep(2)
    print(f"   负数倒计时中 - 当前时间: {timer_window.current_time}秒")
    
    # 关键测试：模拟重新进入全屏（这会触发set_duration）
    print("\n步骤5：🔑 关键测试 - 模拟重新进入全屏")
    print("   这会触发window_detector的_on_fullscreen_enter方法")
    
    # 保存当前状态用于对比
    before_confirmed = timer_window.timer_finished_confirmed
    before_time = timer_window.current_time
    print(f"   重新进入全屏前 - 确认状态: {before_confirmed}")
    print(f"   重新进入全屏前 - 当前时间: {before_time}秒")
    
    # 模拟window_detector的逻辑：保存状态 → set_duration → 恢复状态
    print("\n   模拟window_detector逻辑:")
    print("   a) 保存timer_finished_confirmed状态")
    was_timer_finished = timer_window.timer_finished_confirmed
    print(f"      保存的状态: {was_timer_finished}")
    
    print("   b) 调用set_duration（这会重置状态）")
    timer_window.set_duration("00:02")  # 重新设置相同时间
    print(f"      set_duration后 - 确认状态: {timer_window.timer_finished_confirmed}")
    
    print("   c) 恢复timer_finished_confirmed状态")
    if was_timer_finished:
        timer_window.timer_finished_confirmed = True
        print(f"      恢复后 - 确认状态: {timer_window.timer_finished_confirmed}")
    
    # 验证修复效果
    print("\n步骤6：🔍 验证修复效果")
    after_confirmed = timer_window.timer_finished_confirmed
    
    if after_confirmed and was_timer_finished:
        print("✅ 修复成功：timer_finished_confirmed状态已正确保持")
        print("   重新进入全屏后应该显示红色背景和负数倒计时")
        
        # 强制更新显示
        timer_window.update_display()
        print("   已强制更新显示")
        
        # 检查颜色状态
        if timer_window.is_warning:
            print("✅ 颜色状态正确：is_warning = True（红色背景）")
        else:
            print("❌ 颜色状态错误：is_warning = False（应该是红色背景）")
            
    else:
        print("❌ 修复失败：timer_finished_confirmed状态未正确保持")
        print(f"   期望: True, 实际: {after_confirmed}")
    
    # 测试负数时间显示
    print("\n步骤7：测试负数时间显示")
    timer_window.current_time = -5  # 设置为负数
    timer_window.update_display()
    print("   已设置当前时间为-5秒并更新显示")
    print("   应该显示 -00:05")
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)
    
    # 显示最终状态
    print(f"最终状态:")
    print(f"  - timer_finished_confirmed: {timer_window.timer_finished_confirmed}")
    print(f"  - is_warning: {timer_window.is_warning}")
    print(f"  - current_time: {timer_window.current_time}秒")
    
    # 关闭窗口
    timer_window.window.after(3000, timer_window.window.quit)
    timer_window.window.mainloop()

if __name__ == "__main__":
    test_negative_countdown_fix()
