#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 干净版本打包脚本
确保不包含任何特殊Unicode字符
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")

def check_pyinstaller():
    """检查PyInstaller"""
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("请先安装PyInstaller: pip install pyinstaller")
        return False

def create_clean_main():
    """创建完全干净的main.py副本"""
    clean_main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock - PPT/PDF 全屏放映计时辅助工具
干净版本主入口文件
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('toolclock_debug.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """ToolClock主函数"""
    print("=" * 60)
    print("ToolClock - PPT/PDF 全屏放映计时辅助工具")
    print("版本：V2.0.0 (干净版)")
    print("=" * 60)
    
    logger.info("ToolClock 启动中...")
    
    try:
        logger.info("导入核心模块...")
        
        # 导入GUI模块
        from gui.timer_window import TimerWindow
        logger.info("TimerWindow 导入成功")
        
        # 导入窗口检测器
        from window_detector_fixed import WindowDetectorFixed
        logger.info("WindowDetectorFixed 导入成功")
        
        logger.info("创建计时器窗口...")
        
        # 创建计时器窗口
        timer_window = TimerWindow()
        logger.info("计时器窗口创建成功")
        
        logger.info("创建窗口检测器...")
        
        # 创建窗口检测器
        detector = WindowDetectorFixed(timer_window)
        logger.info("窗口检测器创建成功")
        
        logger.info("ToolClock已启动！")
        
        print("\\n使用说明:")
        print("  1. 打开PPT或PDF文件")
        print("  2. 按F5进入全屏模式 -> 自动开始倒计时")
        print("  3. 退出全屏 -> 自动停止倒计时")
        print("  4. 右键点击计时器 -> 调整当前文件时长")
        print("  5. 可以拖拽移动计时器位置")
        print("=" * 60)
        
        # 启动GUI主循环
        logger.info("启动GUI主循环...")
        timer_window.run()
        
    except KeyboardInterrupt:
        logger.info("ToolClock已停止")
        print("\\nToolClock已停止")
    except Exception as e:
        logger.error(f"ToolClock启动失败: {e}")
        print(f"ToolClock启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    with open('main_clean.py', 'w', encoding='utf-8') as f:
        f.write(clean_main_content)
    
    print("已创建干净的main_clean.py")

def build_clean_exe():
    """构建干净的exe文件"""
    print("\\n构建ToolClock干净版exe...")
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'ToolClock_Clean',
            '--add-data', 'gui;gui',
            '--add-data', 'core;core',
            '--add-data', 'assets;assets',
            '--add-data', 'window_detector_fixed.py;.',
            'main_clean.py'
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("ToolClock干净版exe构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("ToolClock 干净版本打包工具")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建干净的main.py
    create_clean_main()
    
    # 构建exe
    success = build_clean_exe()
    
    # 清理临时文件（保留dist目录）
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
    if os.path.exists('main_clean.py'):
        os.remove('main_clean.py')
    
    # 总结
    print("\\n" + "=" * 60)
    print("构建总结")
    print("=" * 60)
    print(f"ToolClock干净版: {'成功' if success else '失败'}")
    
    if success:
        print("\\nToolClock干净版构建成功！")
        print("输出文件: dist/ToolClock_Clean.exe")
    else:
        print("\\n构建失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()