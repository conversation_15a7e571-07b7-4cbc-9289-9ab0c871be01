#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 简化打包脚本
生成单个exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")

def check_pyinstaller():
    """检查PyInstaller"""
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("请先安装PyInstaller: pip install pyinstaller")
        return False

def build_exe():
    """构建exe文件"""
    print("\n构建ToolClock exe...")
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'ToolClock_V2.0.0',
            '--add-data', 'gui;gui',
            '--add-data', 'core;core',
            '--add-data', 'assets;assets',
            '--add-data', 'window_detector_fixed.py;.',
            'main.py'
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("ToolClock exe构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("ToolClock V2.0.0 打包工具")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建exe
    success = build_exe()
    
    # 清理临时文件
    clean_build_dirs()
    
    # 总结
    print("\n" + "=" * 60)
    print("构建总结")
    print("=" * 60)
    print(f"ToolClock: {'成功' if success else '失败'}")
    
    if success:
        print("\nToolClock构建成功！")
        print("输出文件: dist/ToolClock_V2.0.0.exe")
    else:
        print("\n构建失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()