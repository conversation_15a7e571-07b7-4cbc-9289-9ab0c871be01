"""
悬浮计时器窗口模块
显示计时器的主要悬浮窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from pathlib import Path
import threading
import time
from datetime import datetime, timedelta

# 智能导入配置管理器
try:
    from core.config_manager import config_manager
except ImportError:
    try:
        from flyclock_tool.core.config_manager import config_manager
    except ImportError:
        # 创建简单配置管理器
        import json
        class SimpleConfig:
            def __init__(self):
                self.config = {}
            def get_setting(self, key, default=None):
                return self.config.get(key, default)
            def set_setting(self, key, value):
                self.config[key] = value
        config_manager = SimpleConfig()

# 智能导入窗口检测器
try:
    from window_detector_fixed import get_window_detector_fixed as get_window_detector
    print("✓ 使用修复版窗口检测器")
except ImportError:
    try:
        from core.window_detector import get_window_detector
        print("⚠ 使用原始窗口检测器")
    except ImportError:
        try:
            from flyclock_tool.core.window_detector import get_window_detector
            print("⚠ 使用原始窗口检测器")
        except ImportError:
            # 创建简单的窗口检测器
            def get_window_detector():
                class SimpleDetector:
                    def is_fullscreen_presentation(self):
                        return False
                    def get_current_presentation_info(self):
                        return None
                return SimpleDetector()

# 智能导入音频播放器
try:
    from core.audio_player import get_audio_player
except ImportError:
    try:
        from flyclock_tool.core.audio_player import get_audio_player
    except ImportError:
        # 创建简单音频播放器
        def get_audio_player():
            class SimpleAudio:
                def play_warning_sound(self):
                    pass
                def play_end_sound(self):
                    pass
            return SimpleAudio()


class TimerWindow:
    """悬浮计时器窗口类"""
    
    def __init__(self):
        """初始化计时器窗口"""
        self.config_manager = config_manager
        self.is_running = False
        self.is_paused = False
        self.current_time = 0  # 当前时间（秒）
        self.total_time = 0   # 总时间（秒）
        self.timer_mode = 'countdown'  # 计时模式：countdown 或 countup
        self.timer_thread = None
        self.is_warning = False
        self.warning_active = False  # 持续警告音状态
        self.timer_finished_confirmed = False  # 用户是否已确认计时结束对话框

        # 窗口检测器
        self.window_detector = get_window_detector(self)

        # 音频播放器
        self.audio_player = get_audio_player()
        
        # 创建窗口
        self.window = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_config()
        
        # 绑定事件
        self.bind_events()
        
        # 创建右键菜单
        self.create_context_menu()

        # 启动窗口检测
        self.start_window_detection()
    
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("ToolClock")
        self.window.overrideredirect(True)  # 无边框窗口
        self.window.attributes('-topmost', True)  # 总是置顶
        
        # 设置窗口大小和位置
        width = self.config_manager.get_setting('display.size.width', 200)
        height = self.config_manager.get_setting('display.size.height', 80)
        x = self.config_manager.get_setting('display.position.x', 100)
        y = self.config_manager.get_setting('display.position.y', 100)
        
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置透明度
        transparency = self.config_manager.get_setting('display.transparency', 0.9)
        self.window.attributes('-alpha', transparency)
        
        # 设置背景色
        bg_color = self.config_manager.get_setting('display.colors.normal.background', '#4A90E2')
        self.window.configure(bg=bg_color)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = tk.Frame(self.window)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 时间显示标签
        self.time_label = tk.Label(
            self.main_frame,
            text="08:00",
            font=("Arial", 24, "bold"),
            fg="#FFFFFF",
            bg="#4A90E2"
        )
        self.time_label.pack(expand=True)
        
        # 控制按钮框架（可选显示）
        self.control_frame = tk.Frame(self.main_frame)
        
        # 控制按钮
        self.start_button = tk.Button(
            self.control_frame,
            text="开始",
            command=self.start_timer,
            font=("Arial", 8),
            width=6
        )
        self.start_button.pack(side=tk.LEFT, padx=2)
        
        self.pause_button = tk.Button(
            self.control_frame,
            text="暂停",
            command=self.pause_timer,
            font=("Arial", 8),
            width=6
        )
        self.pause_button.pack(side=tk.LEFT, padx=2)
        
        self.reset_button = tk.Button(
            self.control_frame,
            text="重置",
            command=self.reset_timer,
            font=("Arial", 8),
            width=6
        )
        self.reset_button.pack(side=tk.LEFT, padx=2)
        
        # 根据配置决定是否显示控制栏
        if self.config_manager.get_setting('display.show_controls', True):
            self.control_frame.pack(side=tk.BOTTOM, pady=2)
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        
        # 本地设置
        self.context_menu.add_command(label="本地设置", command=self.open_settings)
        self.context_menu.add_separator()
        

        
        # 关于ToolClock
        self.context_menu.add_command(label="关于ToolClock", command=self.show_about)
        self.context_menu.add_separator()
        
        # 调整当前文件计时时长
        self.context_menu.add_command(label="调整当前文件计时时长", command=self.adjust_current_file_timer)
        self.context_menu.add_separator()
        
        # 建议/问题
        self.context_menu.add_command(label="建议/问题", command=self.show_feedback)
        self.context_menu.add_separator()
        
        # 退出
        self.context_menu.add_command(label="退出", command=self.quit_application)
    
    def bind_events(self):
        """绑定事件"""
        # 右键菜单
        self.window.bind("<Button-3>", self.show_context_menu)
        
        # 窗口拖拽
        self.time_label.bind("<Button-1>", self.start_drag)
        self.time_label.bind("<B1-Motion>", self.on_drag)
        self.time_label.bind("<ButtonRelease-1>", self.end_drag)
        
        # 双击事件
        self.time_label.bind("<Double-Button-1>", self.on_double_click)
        
        # 键盘快捷键
        self.window.bind("<Key>", self.on_key_press)
        self.window.focus_set()  # 设置焦点以接收键盘事件
    
    def load_config(self):
        """加载配置"""
        try:
            # 加载计时器设置
            duration_str = self.config_manager.get_setting('timer.default_duration', '00:08:00')
            # 也尝试从timer.duration加载（优先级更高）
            duration_from_setting = self.config_manager.get_setting('timer.duration', None)
            if duration_from_setting:
                if isinstance(duration_from_setting, (int, float)):
                    self.total_time = int(duration_from_setting)
                else:
                    self.total_time = self.parse_duration(str(duration_from_setting))
            else:
                self.total_time = self.parse_duration(duration_str)

            # 加载计时模式
            self.timer_mode = self.config_manager.get_setting('timer.mode', 'countdown')

            # 根据模式设置当前时间
            if self.timer_mode == 'countdown':
                self.current_time = self.total_time
            else:
                self.current_time = 0

            # 更新显示
            self.update_display()

            # 加载字体设置
            font_family = self.config_manager.get_setting('display.font.family', 'Arial')
            font_size = self.config_manager.get_setting('display.font.size', 24)
            font_bold = self.config_manager.get_setting('display.font.bold', True)

            font_weight = "bold" if font_bold else "normal"
            self.time_label.configure(font=(font_family, font_size, font_weight))

            # 加载颜色设置
            self.update_colors()

            print(f"✓ 配置加载完成: 模式={self.timer_mode}, 时长={self.total_time}秒")

        except Exception as e:
            print(f"加载配置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def parse_duration(self, duration_str):
        """解析时长字符串为秒数"""
        try:
            # 移除空格
            duration_str = duration_str.strip()
            parts = duration_str.split(':')

            if len(parts) == 2:
                # MM:SS格式
                minutes, seconds = map(int, parts)
                return minutes * 60 + seconds
            elif len(parts) == 3:
                # HH:MM:SS格式
                hours, minutes, seconds = map(int, parts)
                return hours * 3600 + minutes * 60 + seconds
            else:
                return 0
        except Exception as e:
            print(f"解析时间失败: {duration_str}, 错误: {e}")
            return 0
    
    def format_time(self, seconds):
        """格式化时间显示"""
        # 处理负数时间
        is_negative = seconds < 0
        abs_seconds = abs(seconds)

        hours = abs_seconds // 3600
        minutes = (abs_seconds % 3600) // 60
        secs = abs_seconds % 60

        # 总是显示分:秒格式，除非超过1小时
        if hours > 0:
            time_str = f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            time_str = f"{minutes:02d}:{secs:02d}"

        # 如果是负数，添加负号
        if is_negative:
            time_str = "-" + time_str

        return time_str

    def format_time_full(self, seconds):
        """格式化时间为完整的HH:MM:SS格式（用于对话框）"""
        # 处理负数时间
        is_negative = seconds < 0
        abs_seconds = abs(seconds)

        hours = abs_seconds // 3600
        minutes = (abs_seconds % 3600) // 60
        secs = abs_seconds % 60

        # 总是显示完整的HH:MM:SS格式
        time_str = f"{hours:02d}:{minutes:02d}:{secs:02d}"

        # 如果是负数，添加负号
        if is_negative:
            time_str = "-" + time_str

        return time_str

    def update_display(self):
        """更新时间显示"""
        mode = self.config_manager.get_setting('timer.mode', 'countdown')

        if mode == 'countdown':
            # 倒计时模式
            display_time = self.current_time
        else:
            # 正计时模式
            display_time = self.total_time - self.current_time

        # 特殊处理：如果用户已确认计时结束对话框，显示负数格式
        if self.timer_finished_confirmed and self.current_time <= 0:
            # 显示负数时间格式，如 -00:01
            if self.current_time < 0:
                abs_time = abs(self.current_time)
                time_str = "-" + self.format_time(abs_time)
            else:
                # 如果时间是0，显示-00:00
                time_str = "-" + self.format_time(0)
        else:
            time_str = self.format_time(display_time)

        self.time_label.configure(text=time_str)

        # 检查显示状态
        if self.timer_finished_confirmed:
            # 用户已确认计时结束，显示红色背景
            if not self.is_warning:
                self.is_warning = True
                self.update_colors(warning=True, time_up=True)
                print("🔴 显示红色背景和负数时间")
        elif self.current_time <= self.config_manager.get_setting('timer.warning_time', 60):
            # 警告时间，显示警告色
            if not self.is_warning:
                self.is_warning = True
                self.update_colors(warning=True)
                # 播放警告音
                if self.config_manager.get_setting('audio.warning_enabled', True):
                    self.audio_player.play_warning_sound()
        else:
            # 正常时间，显示正常色
            if self.is_warning:
                self.is_warning = False
                self.update_colors(warning=False)
    
    def update_colors(self, warning=False, time_up=False):
        """更新颜色"""
        if time_up:
            # 时间结束状态颜色（红色背景）
            fg_color = '#FFFFFF'  # 白色文字
            bg_color = '#FF0000'  # 纯红色
        elif warning:
            # 警告状态颜色
            fg_color = self.config_manager.get_setting('display.colors.warning.foreground', '#FFFFFF')
            bg_color = self.config_manager.get_setting('display.colors.warning.background', '#FF6B6B')
        else:
            # 正常状态颜色
            fg_color = self.config_manager.get_setting('display.colors.normal.foreground', '#FFFFFF')
            bg_color = self.config_manager.get_setting('display.colors.normal.background', '#4A90E2')

        self.time_label.configure(fg=fg_color, bg=bg_color)
        self.main_frame.configure(bg=bg_color)
        self.window.configure(bg=bg_color)
    
    def start_timer(self):
        """开始计时"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            self.start_button.configure(text="运行中", state="disabled")
            self.pause_button.configure(state="normal")
            
            # 启动计时线程
            self.timer_thread = threading.Thread(target=self.timer_loop, daemon=True)
            self.timer_thread.start()
    
    def pause_timer(self):
        """暂停/恢复计时"""
        if self.is_running:
            self.is_paused = not self.is_paused
            if self.is_paused:
                self.pause_button.configure(text="恢复")
                print("✓ 计时器已暂停")
            else:
                self.pause_button.configure(text="暂停")
                print("✓ 计时器已恢复")
    
    def reset_timer(self):
        """重置计时"""
        self.is_running = False
        self.is_paused = False
        self.current_time = self.total_time
        self.is_warning = False
        self.timer_finished_confirmed = False  # 重置确认标志

        # 停止持续警告音
        if hasattr(self, 'warning_active'):
            self.stop_continuous_warning()

        self.start_button.configure(text="开始", state="normal")
        self.pause_button.configure(text="暂停", state="normal")

        self.update_display()
        self.update_colors(warning=False)
        print("🔄 计时器已重置，确认标志已清除")
    
    def timer_loop(self):
        """计时循环 - 改进的线程安全版本"""
        timer_finished_triggered = False
        print("✓ 计时器线程已启动")

        while self.is_running:
            if not self.is_paused:
                # 检查是否即将结束（在减1之前检查）
                # 只有在用户未确认的情况下才触发结束事件
                if self.current_time <= 1 and not timer_finished_triggered and not self.timer_finished_confirmed:
                    timer_finished_triggered = True
                    print("⏰ 计时结束！")
                    # 先触发结束事件，但不停止计时器
                    self._safe_ui_update(self.timer_finished)

                self.current_time -= 1
                print(f"⏱️ 倒计时: {self.current_time}秒")

                # 安全地更新UI
                self._safe_ui_update(self.update_display)

            # 广播状态给远程客户端（每秒同步）
            self._safe_ui_update(self._broadcast_timer_status)

            time.sleep(1)

        print("✓ 计时器线程已结束")

    def _safe_ui_update(self, callback):
        """安全地更新UI"""
        try:
            # 尝试使用after方法在主线程中执行
            self.window.after(0, callback)
        except RuntimeError as e:
            if "main thread is not in main loop" in str(e):
                # 如果主线程不在主循环中，尝试直接调用
                try:
                    callback()
                except Exception as callback_error:
                    print(f"⚠️ UI更新失败: {callback_error}")
            else:
                print(f"⚠️ UI更新错误: {e}")
        except Exception as e:
            print(f"⚠️ 未知UI更新错误: {e}")

    def _broadcast_timer_status(self):
        """广播计时器状态给远程客户端 - 已禁用"""
        # 远程控制功能已移除，此方法保留为空以保持兼容性
        pass

    def timer_finished(self):
        """计时结束处理"""
        print("⏰ 计时结束！")

        # 执行结束行为
        end_action = self.config_manager.get_setting('control.timer_end_action', 'exit_fullscreen')

        if end_action == 'exit_fullscreen':
            # 退出全屏
            self.exit_fullscreen()
            message = "演示时间已到！\n\n已自动退出全屏模式。"
        elif end_action == 'black_screen':
            # 黑屏处理
            self.show_black_screen()
            message = "演示时间已到！\n\n已切换到黑屏模式。"
        else:
            # 保持全屏
            message = "演示时间已到！\n\n请注意时间控制。"

        # 播放持续警告音
        if self.config_manager.get_setting('audio.enabled', True):
            self.start_continuous_warning()

        # 显示提示对话框
        messagebox.showinfo("计时结束", message)

        # 用户点击确定后，设置确认标志并停止警告音
        self.timer_finished_confirmed = True
        print("✅ 用户已确认计时结束对话框")

        # 停止持续警告音
        self.stop_continuous_warning()
        print("🔇 已停止持续警告音")

        # 不停止计时器运行，让它继续到负数时间
        # self.is_running = False  # 注释掉这行，让计时器继续运行
        print("⏹️ 计时器继续运行到负数时间状态")
    
    def exit_fullscreen(self):
        """退出全屏模式"""
        try:
            # 发送ESC键退出全屏
            import win32api
            import win32con

            # 模拟按下ESC键
            win32api.keybd_event(win32con.VK_ESCAPE, 0, 0, 0)
            win32api.keybd_event(win32con.VK_ESCAPE, 0, win32con.KEYEVENTF_KEYUP, 0)

            print("✓ 已发送ESC键退出全屏")

        except ImportError:
            # 如果没有win32api，尝试其他方法
            try:
                import subprocess
                # 使用PowerShell发送ESC键
                subprocess.run([
                    'powershell', '-Command',
                    'Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait("{ESC}")'
                ], capture_output=True)
                print("✓ 已通过PowerShell发送ESC键退出全屏")
            except Exception as e:
                print(f"✗ 退出全屏失败: {e}")
        except Exception as e:
            print(f"✗ 退出全屏失败: {e}")

    def show_black_screen(self):
        """显示黑屏"""
        # 这里可以实现黑屏功能
        print("ℹ 黑屏功能待实现")

    def start_continuous_warning(self):
        """开始持续警告音"""
        try:
            # 启动持续警告音线程
            self.warning_thread = threading.Thread(target=self.continuous_warning_loop, daemon=True)
            self.warning_active = True
            self.warning_thread.start()
            print("✓ 开始播放持续警告音")
        except Exception as e:
            print(f"✗ 启动持续警告音失败: {e}")

    def continuous_warning_loop(self):
        """持续警告音循环"""
        warning_interval = self.config_manager.get_setting('audio.warning_interval', 3)  # 3秒间隔

        while self.warning_active:
            try:
                self.audio_player.play_warning_sound()
                time.sleep(warning_interval)
            except Exception as e:
                print(f"播放警告音失败: {e}")
                break

    def stop_continuous_warning(self):
        """停止持续警告音"""
        self.warning_active = False
        print("✓ 已停止持续警告音")

    def play_end_sound(self):
        """播放结束音"""
        try:
            self.audio_player.play_end_sound()
        except Exception as e:
            print(f"播放结束音失败: {e}")
    
    def start_drag(self, event):
        """开始拖拽"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
    
    def on_drag(self, event):
        """拖拽中"""
        x = self.window.winfo_x() + event.x - self.drag_start_x
        y = self.window.winfo_y() + event.y - self.drag_start_y
        self.window.geometry(f"+{x}+{y}")
    
    def end_drag(self, event):
        """结束拖拽"""
        # 保存新位置到配置
        x = self.window.winfo_x()
        y = self.window.winfo_y()
        self.config_manager.set_setting('display.position.x', x)
        self.config_manager.set_setting('display.position.y', y)
    
    def on_double_click(self, event):
        """双击事件"""
        self.open_settings()
    
    def on_key_press(self, event):
        """键盘按键事件"""
        pause_key = self.config_manager.get_setting('control.hotkeys.pause_resume', 'F3')
        reset_key = self.config_manager.get_setting('control.hotkeys.reset', 'F4')
        
        if event.keysym == pause_key:
            self.pause_timer()
        elif event.keysym == reset_key:
            self.reset_timer()
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def open_settings(self):
        """打开设置窗口"""
        try:
            # 智能导入设置窗口
            try:
                from gui.settings_window import SettingsWindow
            except ImportError:
                from flyclock_tool.gui.settings_window import SettingsWindow

            settings = SettingsWindow(self.window)
            # 设置窗口关闭后重新加载配置
            self.window.after(100, self.load_config)
            # 重新加载计时器设置
            self.window.after(200, self.reload_timer_settings)
        except Exception as e:
            messagebox.showerror("错误", f"打开设置失败: {e}")
            import traceback
            traceback.print_exc()

    def reload_timer_settings(self):
        """重新加载计时器设置"""
        try:
            # 重新加载计时模式和时长
            self.timer_mode = self.config_manager.get_setting('timer.mode', 'countdown')

            # 从配置加载时长
            duration_from_setting = self.config_manager.get_setting('timer.duration', None)
            if duration_from_setting:
                if isinstance(duration_from_setting, (int, float)):
                    self.total_time = int(duration_from_setting)
                else:
                    duration_str = self.config_manager.get_setting('timer.default_duration', '00:08:00')
                    self.total_time = self.parse_duration(duration_str)
            else:
                duration_str = self.config_manager.get_setting('timer.default_duration', '00:08:00')
                self.total_time = self.parse_duration(duration_str)

            # 如果当前没有在计时，重置显示
            if not self.is_running:
                if self.timer_mode == 'countdown':
                    self.current_time = self.total_time
                else:
                    self.current_time = 0
                self.update_display()

            print(f"✓ 重新加载计时器设置: 模式={self.timer_mode}, 时长={self.total_time}秒")
        except Exception as e:
            print(f"⚠ 重新加载计时器设置失败: {e}")
            import traceback
            traceback.print_exc()

    def show_about(self):
        """显示关于信息"""
        # 获取当前演示状态
        presentation_info = self.get_presentation_status()
        status_text = ""
        if presentation_info:
            status_text = f"\n当前检测到: {presentation_info['file_name']}"

        about_text = f"""ToolClock V1.0.0

ToolClock 是一个用于 PPT 和 PDF 文件的全自动
计时器。支持悬浮显示、音频提示、智能检测等功能。

珠海分行智能科技部
开发者: 黎民浩{status_text}"""

        messagebox.showinfo("关于 ToolClock", about_text)

    def adjust_current_file_timer(self):
        """调整当前文件计时时长 - 改进版"""
        presentation_info = self.get_presentation_status()

        if not presentation_info:
            # 如果没有检测到演示文件，提供手动选择选项
            result = messagebox.askyesno(
                "未检测到文件",
                "当前没有检测到PPT/PDF文件。\n\n是否要手动添加文件计时器？"
            )
            if result:
                self.open_settings()  # 打开设置窗口
            return

        file_name = presentation_info['file_name']
        is_fullscreen = presentation_info.get('is_fullscreen', False)

        # 显示当前状态
        status_text = "全屏演示中" if is_fullscreen else "窗口模式"
        print(f"📄 当前文件: {file_name} ({status_text})")

        # 打开文件计时器调整对话框
        from gui.settings_window import FileTimerDialog
        dialog = FileTimerDialog(
            self.window,
            f"调整文件计时时长 ({status_text})",
            file_name,
            self.format_time_full(self.total_time)  # 使用完整格式
        )

        if dialog.result:
            file_path, duration = dialog.result

            # 保存到文件特定计时器配置
            file_timers = self.config_manager.get_setting('files.file_specific_timers', {})
            file_timers[file_path] = duration
            self.config_manager.set_setting('files.file_specific_timers', file_timers)

            print(f"✅ 已保存文件计时器: {file_path} -> {duration}")

            # 更新当前计时器（如果当前正在使用这个文件）
            self.set_duration(duration)

            # 显示成功消息
            messagebox.showinfo(
                "设置成功",
                f"已为文件设置计时时长: {duration}\n\n"
                f"文件: {os.path.basename(file_path)}\n"
                f"状态: {status_text}"
            )

    def show_feedback(self):
        """显示反馈"""
        feedback_text = """感谢使用 ToolClock！

如果您有任何建议或问题，请通过以下方式联系我们：

珠海分行智能科技部

您的反馈对我们非常重要！"""

        messagebox.showinfo("建议/问题", feedback_text)
    
    def quit_application(self):
        """退出应用"""
        if messagebox.askyesno("确认", "确定要退出 ToolClock 吗？"):
            # 停止持续警告音
            if hasattr(self, 'warning_active'):
                self.stop_continuous_warning()

            # 停止窗口检测
            if hasattr(self, 'window_detector'):
                self.window_detector.stop_detection()

            # 清理音频资源
            if hasattr(self, 'audio_player'):
                self.audio_player.cleanup()

            self.window.quit()

    def start_window_detection(self):
        """启动窗口检测"""
        try:
            if self.window_detector.start_detection():
                print("✓ PPT/PDF全屏检测已启动")
            else:
                print("✗ PPT/PDF全屏检测启动失败")
        except Exception as e:
            print(f"✗ 启动窗口检测失败: {e}")

    def set_duration(self, duration_str):
        """设置计时时长"""
        try:
            new_time = self.parse_duration(duration_str)
            if new_time > 0:
                self.total_time = new_time
                self.current_time = new_time

                # 重置所有状态标志，确保颜色和显示正确
                self.is_warning = False
                self.timer_finished_confirmed = False

                # 停止持续警告音（如果有的话）
                if hasattr(self, 'warning_active'):
                    self.stop_continuous_warning()

                # 同时更新配置文件中的timer.duration，确保重启后保持一致
                self.config_manager.set_setting('timer.duration', duration_str)
                print(f"⏱️ 计时时长已设置为: {duration_str} (已保存到配置)")

                # 更新显示和颜色
                self.update_display()
                self.update_colors(warning=False)  # 强制重置为正常颜色
                print(f"🎨 已重置颜色状态为正常模式")
        except Exception as e:
            print(f"❌ 设置计时时长失败: {e}")

    def get_presentation_status(self):
        """获取当前演示状态"""
        if hasattr(self, 'window_detector'):
            return self.window_detector.get_current_presentation_info()
        return None
    


    def run(self):
        """运行窗口"""
        self.window.mainloop()


def main():
    """主函数"""
    timer_window = TimerWindow()
    timer_window.run()


if __name__ == "__main__":
    main()
