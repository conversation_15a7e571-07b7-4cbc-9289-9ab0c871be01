2025-07-31 11:41:56,735 - INFO - ============================================================
2025-07-31 11:41:56,736 - INFO - 🔧 <PERSON><PERSON><PERSON>lock 启动中...
2025-07-31 11:41:56,736 - INFO - ============================================================
2025-07-31 11:41:56,736 - INFO - 📦 导入模块...
2025-07-31 11:41:56,737 - ERROR - ✗ <PERSON><PERSON><PERSON>lock启动失败: No module named 'flyclock_tool'
2025-07-31 11:41:56,744 - ERROR - Traceback (most recent call last):
  File "d:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from flyclock_tool.gui.timer_window import TimerWindow
ModuleNotFoundError: No module named 'flyclock_tool'

2025-07-31 11:45:18,687 - INFO - ============================================================
2025-07-31 11:45:18,687 - INFO - 🔧 ToolClock 启动中...
2025-07-31 11:45:18,688 - INFO - ============================================================
2025-07-31 11:45:18,688 - INFO - 📦 导入模块...
2025-07-31 11:45:18,690 - ERROR - ✗ ToolClock启动失败: No module named 'flyclock_tool'
2025-07-31 11:45:18,699 - ERROR - Traceback (most recent call last):
  File "d:\trae\flyclock_tool\run_with_log.py", line 37, in main
    from flyclock_tool.gui.timer_window import TimerWindow
ModuleNotFoundError: No module named 'flyclock_tool'

2025-07-31 17:09:18,229 - INFO - ============================================================
2025-07-31 17:09:18,230 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:09:18,230 - INFO - ============================================================
2025-07-31 17:09:18,231 - INFO - 📦 导入模块...
2025-07-31 17:09:18,279 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:09:19,567 - INFO - pygame音频系统初始化成功
2025-07-31 17:09:19,567 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:09:19,568 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:09:19,568 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:09:19,672 - INFO - 窗口检测已启动
2025-07-31 17:09:19,672 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:09:19,672 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:09:19,673 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:09:19,673 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:09:19,673 - INFO - 📋 修复后的逻辑:
2025-07-31 17:09:19,673 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:09:19,673 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:09:19,673 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:09:19,673 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:09:19,674 - INFO - ============================================================
2025-07-31 17:09:19,674 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:09:19,674 - INFO - ============================================================
2025-07-31 17:09:19,674 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:09:23,679 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:09:25,685 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:09:25,687 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:09:25,693 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 17:09:29,716 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:09:34,735 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:09:35,710 - INFO - 全屏演示已退出
2025-07-31 17:09:37,718 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:09:37,722 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:09:37,733 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:09:49,761 - INFO - 全屏演示已退出
2025-07-31 17:12:26,987 - INFO - 窗口检测已停止
2025-07-31 17:12:26,987 - INFO - 已停止所有音频播放
2025-07-31 17:12:27,105 - INFO - 音频资源清理完成
2025-07-31 17:51:08,317 - INFO - ============================================================
2025-07-31 17:51:08,318 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:51:08,318 - INFO - ============================================================
2025-07-31 17:51:08,319 - INFO - 📦 导入模块...
2025-07-31 17:51:08,368 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:51:09,585 - INFO - pygame音频系统初始化成功
2025-07-31 17:51:09,585 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:51:09,586 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:51:09,586 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:51:09,712 - INFO - 窗口检测已启动
2025-07-31 17:51:09,713 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:51:09,714 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:51:09,714 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:51:09,714 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:51:09,714 - INFO - 📋 修复后的逻辑:
2025-07-31 17:51:09,715 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:51:09,715 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:51:09,715 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:51:09,716 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:51:09,716 - INFO - ============================================================
2025-07-31 17:51:09,716 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:51:09,716 - INFO - ============================================================
2025-07-31 17:51:09,716 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:52:24,819 - INFO - 窗口检测已停止
2025-07-31 17:52:24,820 - INFO - 已停止所有音频播放
2025-07-31 17:52:24,897 - INFO - 音频资源清理完成
2025-07-31 17:53:00,099 - INFO - ============================================================
2025-07-31 17:53:00,099 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:53:00,099 - INFO - ============================================================
2025-07-31 17:53:00,100 - INFO - 📦 导入模块...
2025-07-31 17:53:00,211 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:53:02,668 - INFO - pygame音频系统初始化成功
2025-07-31 17:53:02,668 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:53:02,669 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:53:02,669 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:53:02,787 - INFO - 窗口检测已启动
2025-07-31 17:53:02,788 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:53:02,789 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:53:02,789 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:53:02,789 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:53:02,789 - INFO - 📋 修复后的逻辑:
2025-07-31 17:53:02,790 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:53:02,790 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:53:02,790 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:53:02,790 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:53:02,791 - INFO - ============================================================
2025-07-31 17:53:02,791 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:53:02,791 - INFO - ============================================================
2025-07-31 17:53:02,791 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:53:06,805 - INFO - 窗口检测已停止
2025-07-31 17:53:06,805 - INFO - 已停止所有音频播放
2025-07-31 17:53:06,905 - INFO - 音频资源清理完成
2025-07-31 17:54:43,263 - INFO - ============================================================
2025-07-31 17:54:43,264 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:54:43,264 - INFO - ============================================================
2025-07-31 17:54:43,264 - INFO - 📦 导入核心模块...
2025-07-31 17:54:43,325 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:54:44,584 - INFO - pygame音频系统初始化成功
2025-07-31 17:54:44,584 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:54:44,585 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:54:44,586 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:54:44,703 - INFO - 窗口检测已启动
2025-07-31 17:54:44,704 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:54:44,704 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:54:44,704 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:54:44,705 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:54:44,705 - INFO - 📋 核心功能:
2025-07-31 17:54:44,705 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:54:44,706 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:54:44,706 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:54:44,706 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:54:44,706 - INFO -   ✅ 右键调整 → 根据窗体自动增加带润文档后缀
2025-07-31 17:54:44,706 - INFO - ============================================================
2025-07-31 17:54:44,707 - INFO - 🔍 实时监控开始:
2025-07-31 17:54:44,707 - INFO - ============================================================
2025-07-31 17:54:44,708 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:54:57,723 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:55:10,751 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:55:10,754 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:55:10,760 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:55:10,763 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 17:55:18,786 - INFO - 全屏演示已退出
2025-07-31 17:55:19,793 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:55:59,853 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:55:59,857 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:55:59,864 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:55:59,866 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 17:56:02,872 - INFO - 全屏演示已退出
2025-07-31 17:56:05,879 - INFO - 窗口检测已停止
2025-07-31 17:56:05,880 - INFO - 已停止所有音频播放
2025-07-31 17:56:05,953 - INFO - 音频资源清理完成
2025-07-31 17:56:14,579 - INFO - ============================================================
2025-07-31 17:56:14,580 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:56:14,580 - INFO - ============================================================
2025-07-31 17:56:14,580 - INFO - 📦 导入模块...
2025-07-31 17:56:14,649 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:15,869 - INFO - pygame音频系统初始化成功
2025-07-31 17:56:15,870 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:56:15,870 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:56:15,871 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:56:15,967 - INFO - 窗口检测已启动
2025-07-31 17:56:15,968 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:56:15,968 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:56:15,968 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:15,969 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:56:15,969 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:56:15,970 - INFO - 📋 修复后的逻辑:
2025-07-31 17:56:15,971 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:56:15,972 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:56:15,972 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:56:15,973 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:56:15,974 - INFO - ============================================================
2025-07-31 17:56:15,974 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 17:56:15,975 - INFO - ============================================================
2025-07-31 17:56:15,975 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:56:18,980 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 17:56:18,983 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:18,989 - INFO - 无法找到对应的文件进行分析: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx]
2025-07-31 17:56:18,991 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 17:56:20,995 - INFO - 全屏演示已退出
2025-07-31 17:56:22,007 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:22,009 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:24,866 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:31,011 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:32,024 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:56:52,061 - INFO - 窗口检测已停止
2025-07-31 17:56:52,061 - INFO - 已停止所有音频播放
2025-07-31 17:56:52,158 - INFO - 音频资源清理完成
2025-07-31 17:57:26,133 - INFO - ============================================================
2025-07-31 17:57:26,133 - INFO - 🔧 ToolClock 启动中...
2025-07-31 17:57:26,134 - INFO - ============================================================
2025-07-31 17:57:26,134 - INFO - 📦 导入核心模块...
2025-07-31 17:57:26,186 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:27,415 - INFO - pygame音频系统初始化成功
2025-07-31 17:57:27,416 - INFO - ✓ TimerWindow 导入成功
2025-07-31 17:57:27,416 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 17:57:27,417 - INFO - 🚀 创建计时器窗口...
2025-07-31 17:57:27,530 - INFO - 窗口检测已启动
2025-07-31 17:57:27,531 - INFO - ✓ 计时器窗口创建成功
2025-07-31 17:57:27,531 - INFO - 🔍 创建窗口检测器...
2025-07-31 17:57:27,532 - INFO - ✓ 窗口检测器创建成功
2025-07-31 17:57:27,532 - INFO - 🎯 ToolClock已启动！
2025-07-31 17:57:27,532 - INFO - 📋 核心功能:
2025-07-31 17:57:27,532 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 17:57:27,533 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 17:57:27,533 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 17:57:27,533 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 17:57:27,533 - INFO -   ✅ 右键调整 → 根据窗体自动增加带润文档后缀
2025-07-31 17:57:27,534 - INFO - ============================================================
2025-07-31 17:57:27,534 - INFO - 🔍 实时监控开始:
2025-07-31 17:57:27,534 - INFO - ============================================================
2025-07-31 17:57:27,536 - INFO - ▶️ 启动GUI主循环...
2025-07-31 17:57:33,541 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:40,561 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:42,577 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:44,589 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:46,597 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:57:46,598 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:46,608 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 17:57:50,630 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:57:55,640 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 17:57:56,626 - INFO - 全屏演示已退出
2025-07-31 17:57:58,632 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 17:57:58,633 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 17:57:58,640 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 17:58:00,647 - INFO - 全屏演示已退出
2025-07-31 18:00:38,645 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:00:38,648 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:04:21,311 - INFO - 窗口检测已停止
2025-07-31 18:04:21,312 - INFO - 已停止所有音频播放
2025-07-31 18:04:21,392 - INFO - 音频资源清理完成
2025-07-31 18:07:23,643 - INFO - ============================================================
2025-07-31 18:07:23,644 - INFO - 🔧 ToolClock 启动中...
2025-07-31 18:07:23,644 - INFO - ============================================================
2025-07-31 18:07:23,644 - INFO - 📦 导入核心模块...
2025-07-31 18:07:23,699 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:07:24,917 - INFO - pygame音频系统初始化成功
2025-07-31 18:07:24,917 - INFO - ✓ TimerWindow 导入成功
2025-07-31 18:07:24,918 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 18:07:24,918 - INFO - 🚀 创建计时器窗口...
2025-07-31 18:07:25,048 - INFO - 窗口检测已启动
2025-07-31 18:07:25,049 - INFO - ✓ 计时器窗口创建成功
2025-07-31 18:07:25,049 - INFO - 🔍 创建窗口检测器...
2025-07-31 18:07:25,049 - INFO - ✓ 窗口检测器创建成功
2025-07-31 18:07:25,050 - INFO - 🎯 ToolClock已启动！
2025-07-31 18:07:25,050 - INFO - 📋 核心功能:
2025-07-31 18:07:25,050 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 18:07:25,050 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 18:07:25,051 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 18:07:25,051 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 18:07:25,051 - INFO -   ✅ 右键调整 → 根据窗体自动增加带润文档后缀
2025-07-31 18:07:25,051 - INFO - ============================================================
2025-07-31 18:07:25,052 - INFO - 🔍 实时监控开始:
2025-07-31 18:07:25,052 - INFO - ============================================================
2025-07-31 18:07:25,054 - INFO - ▶️ 启动GUI主循环...
2025-07-31 18:07:42,073 - INFO - 窗口检测已停止
2025-07-31 18:07:42,074 - INFO - 已停止所有音频播放
2025-07-31 18:07:42,124 - INFO - 音频资源清理完成
2025-07-31 18:25:24,431 - INFO - ============================================================
2025-07-31 18:25:24,431 - INFO - 🔧 ToolClock 启动中...
2025-07-31 18:25:24,431 - INFO - ============================================================
2025-07-31 18:25:24,432 - INFO - 📦 导入模块...
2025-07-31 18:25:24,483 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:25,241 - INFO - pygame音频系统初始化成功
2025-07-31 18:25:25,241 - INFO - ✓ TimerWindow 导入成功
2025-07-31 18:25:25,242 - INFO - ✓ WindowDetectorFixed 导入成功
2025-07-31 18:25:25,242 - INFO - 🚀 创建计时器窗口...
2025-07-31 18:25:25,368 - INFO - 窗口检测已启动
2025-07-31 18:25:25,369 - INFO - ✓ 计时器窗口创建成功
2025-07-31 18:25:25,369 - INFO - 🔍 创建窗口检测器...
2025-07-31 18:25:25,369 - INFO - ✓ 窗口检测器创建成功
2025-07-31 18:25:25,370 - INFO - 🎯 ToolClock已启动！
2025-07-31 18:25:25,370 - INFO - 📋 修复后的逻辑:
2025-07-31 18:25:25,371 - INFO -   ✅ 打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 18:25:25,371 - INFO -   ✅ 按F5或全屏放映 → 自动开始倒计时
2025-07-31 18:25:25,371 - INFO -   ✅ 退出全屏 → 自动停止倒计时
2025-07-31 18:25:25,371 - INFO -   ✅ 切换文件 → 更新时间设置（不启动）
2025-07-31 18:25:25,372 - INFO - ============================================================
2025-07-31 18:25:25,372 - INFO - 🔍 实时监控开始（请进行测试）:
2025-07-31 18:25:25,372 - INFO - ============================================================
2025-07-31 18:25:25,372 - INFO - ▶️ 启动GUI主循环...
2025-07-31 18:25:26,371 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:30,386 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:33,401 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:43,423 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:45,438 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:46,449 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:49,467 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:51,837 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:51,839 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:56,489 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:25:58,499 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:26:37,558 - INFO - 窗口检测已停止
2025-07-31 18:26:37,559 - INFO - 已停止所有音频播放
2025-07-31 18:26:37,662 - INFO - 音频资源清理完成
2025-07-31 18:33:43,648 - INFO - ============================================================
2025-07-31 18:33:43,648 - INFO -  ToolClock 启动中...
2025-07-31 18:33:43,649 - INFO - ============================================================
2025-07-31 18:33:43,649 - INFO -  导入模块...
2025-07-31 18:33:43,709 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:44,390 - INFO - pygame音频系统初始化成功
2025-07-31 18:33:44,390 - INFO -  TimerWindow 导入成功
2025-07-31 18:33:44,391 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 18:33:44,391 - INFO -  创建计时器窗口...
2025-07-31 18:33:44,519 - INFO - 窗口检测已启动
2025-07-31 18:33:44,520 - INFO -  计时器窗口创建成功
2025-07-31 18:33:44,520 - INFO -  创建窗口检测器...
2025-07-31 18:33:44,520 - INFO -  窗口检测器创建成功
2025-07-31 18:33:44,520 - INFO -  ToolClock已启动！
2025-07-31 18:33:44,520 - INFO -  修复后的逻辑:
2025-07-31 18:33:44,521 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 18:33:44,521 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 18:33:44,521 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 18:33:44,521 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 18:33:44,522 - INFO - ============================================================
2025-07-31 18:33:44,522 - INFO -  实时监控开始（请进行测试）:
2025-07-31 18:33:44,522 - INFO - ============================================================
2025-07-31 18:33:44,523 - INFO -  启动GUI主循环...
2025-07-31 18:33:51,535 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:53,547 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:33:55,560 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 18:34:00,573 - INFO - 窗口检测已停止
2025-07-31 18:34:00,574 - INFO - 已停止所有音频播放
2025-07-31 18:34:00,649 - INFO - 音频资源清理完成
2025-07-31 19:38:05,990 - INFO - ============================================================
2025-07-31 19:38:05,990 - INFO -  ToolClock 启动中...
2025-07-31 19:38:05,990 - INFO - ============================================================
2025-07-31 19:38:05,990 - INFO -  导入模块...
2025-07-31 19:38:06,027 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:07,143 - INFO - pygame音频系统初始化成功
2025-07-31 19:38:07,143 - INFO -  TimerWindow 导入成功
2025-07-31 19:38:07,143 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 19:38:07,144 - INFO -  创建计时器窗口...
2025-07-31 19:38:07,265 - INFO - 窗口检测已启动
2025-07-31 19:38:07,266 - INFO -  计时器窗口创建成功
2025-07-31 19:38:07,266 - INFO -  创建窗口检测器...
2025-07-31 19:38:07,266 - INFO -  窗口检测器创建成功
2025-07-31 19:38:07,266 - INFO -  启动窗口检测...
2025-07-31 19:38:07,266 - INFO - 窗口检测已启动
2025-07-31 19:38:07,266 - INFO -  窗口检测启动成功
2025-07-31 19:38:07,266 - INFO -  ToolClock已启动！
2025-07-31 19:38:07,267 - INFO -  修复后的逻辑:
2025-07-31 19:38:07,267 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 19:38:07,267 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 19:38:07,267 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 19:38:07,267 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 19:38:07,267 - INFO - ============================================================
2025-07-31 19:38:07,267 - INFO -  实时监控开始（请进行测试）:
2025-07-31 19:38:07,267 - INFO - ============================================================
2025-07-31 19:38:07,267 - INFO -  启动GUI主循环...
2025-07-31 19:38:11,273 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:11,274 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:11,275 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:11,276 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:14,280 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:14,281 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:14,281 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:14,282 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:18,285 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:18,286 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:18,290 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:18,291 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:18,292 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:18,293 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:18,298 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:38:18,301 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:38:20,315 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:38:22,330 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:38:23,307 - INFO - 全屏演示已退出
2025-07-31 19:38:23,309 - INFO - 全屏演示已退出
2025-07-31 19:38:25,311 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:25,312 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:25,314 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:25,316 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:25,317 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:25,317 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:38:25,318 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:25,320 - INFO - DEBUG: timer_finished_confirmed=True, current_time=-1
2025-07-31 19:38:30,325 - INFO - 全屏演示已退出
2025-07-31 19:38:30,326 - INFO - 全屏演示已退出
2025-07-31 19:38:30,672 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:30,674 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:33,305 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:33,306 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:35,331 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:35,332 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:35,334 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:35,335 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:35,337 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:35,338 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:35,339 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:38:35,339 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:38:38,344 - INFO - 全屏演示已退出
2025-07-31 19:38:38,345 - INFO - 全屏演示已退出
2025-07-31 19:38:41,350 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:41,353 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:41,354 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:41,356 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:43,357 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:43,358 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:43,358 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:43,359 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:43,359 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:43,360 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:43,361 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:38:43,364 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:38:47,375 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:38:52,392 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:38:53,376 - INFO - 全屏演示已退出
2025-07-31 19:38:53,379 - INFO - 全屏演示已退出
2025-07-31 19:38:56,380 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:56,382 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:56,382 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:38:56,383 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:56,384 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:56,385 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:38:56,386 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:38:56,387 - INFO - DEBUG: timer_finished_confirmed=True, current_time=0
2025-07-31 19:39:07,405 - INFO - 全屏演示已退出
2025-07-31 19:39:07,406 - INFO - 全屏演示已退出
2025-07-31 19:39:08,406 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:08,407 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:08,408 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:08,410 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:11,412 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:11,413 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:11,413 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:11,414 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:14,416 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:39:14,417 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:39:14,420 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:14,421 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:14,423 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:14,424 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:14,426 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:39:14,429 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:39:19,433 - INFO - 全屏演示已退出
2025-07-31 19:39:19,437 - INFO - 全屏演示已退出
2025-07-31 19:39:20,436 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:20,437 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:20,439 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:20,440 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:22,441 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:22,444 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:22,445 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:22,446 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:22,447 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:22,447 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:22,448 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:22,450 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:24,457 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:39:26,472 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:39:27,457 - INFO - 全屏演示已退出
2025-07-31 19:39:27,457 - INFO - 全屏演示已退出
2025-07-31 19:39:29,460 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:29,461 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:29,464 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:29,466 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:30,468 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:30,470 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:30,471 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:30,472 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:30,473 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:30,474 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:30,475 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:30,479 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:34,498 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:39:35,483 - INFO - 全屏演示已退出
2025-07-31 19:39:35,490 - INFO - 全屏演示已退出
2025-07-31 19:39:36,491 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:36,492 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:36,493 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:36,494 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:37,496 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:37,497 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:37,499 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:37,501 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:37,502 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:37,503 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:37,505 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:37,505 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:41,520 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:39:42,510 - INFO - 全屏演示已退出
2025-07-31 19:39:43,517 - INFO - 全屏演示已退出
2025-07-31 19:39:43,666 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:43,668 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:44,518 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:44,519 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:44,519 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:44,522 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:44,522 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:44,523 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:44,524 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:44,528 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:45,525 - INFO - 全屏演示已退出
2025-07-31 19:39:45,531 - INFO - 全屏演示已退出
2025-07-31 19:39:46,530 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:46,531 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:46,533 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:46,533 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:51,539 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:51,539 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:51,540 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:51,540 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:51,540 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:51,540 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:51,547 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:51,547 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:55,571 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:39:56,552 - INFO - 全屏演示已退出
2025-07-31 19:39:56,560 - INFO - 全屏演示已退出
2025-07-31 19:39:57,559 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:57,560 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:57,562 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:57,563 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:58,562 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:58,563 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:58,563 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:58,567 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:39:58,568 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:39:58,570 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:58,572 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:39:58,573 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:40:00,578 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:03,593 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:03,597 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:04,579 - INFO - 全屏演示已退出
2025-07-31 19:40:04,581 - INFO - 全屏演示已退出
2025-07-31 19:40:07,585 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:07,585 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:07,586 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:07,587 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:40:07,591 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:07,591 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:07,592 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:07,593 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:40:10,596 - INFO - 全屏演示已退出
2025-07-31 19:40:10,600 - INFO - 全屏演示已退出
2025-07-31 19:40:11,598 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:11,600 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:11,603 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:11,604 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:14,606 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:14,606 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:14,608 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:14,609 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:14,611 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:14,611 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:14,612 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:40:14,614 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:40:16,621 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:18,639 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:19,620 - INFO - 全屏演示已退出
2025-07-31 19:40:19,620 - INFO - 全屏演示已退出
2025-07-31 19:40:21,622 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:21,622 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:21,623 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:21,624 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:40:21,629 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:21,630 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:21,630 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:21,632 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:40:27,634 - INFO - 全屏演示已退出
2025-07-31 19:40:27,643 - INFO - 全屏演示已退出
2025-07-31 19:40:28,636 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:28,637 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:28,645 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:28,646 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:30,642 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:30,644 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:30,646 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:30,648 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:40:30,650 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:30,651 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:30,653 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:30,662 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:40:32,677 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:35,673 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:35,692 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:40:36,658 - INFO - 全屏演示已退出
2025-07-31 19:40:36,675 - INFO - 全屏演示已退出
2025-07-31 19:40:38,661 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:38,662 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:38,663 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:38,667 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:40:38,677 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:40:38,682 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:38,683 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:38,685 - INFO - DEBUG: timer_finished_confirmed=True, current_time=10
2025-07-31 19:40:42,673 - INFO - 全屏演示已退出
2025-07-31 19:40:42,690 - INFO - 全屏演示已退出
2025-07-31 19:40:43,675 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:43,675 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:43,692 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:43,693 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:44,677 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:40:44,678 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:44,680 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:44,681 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:40:44,694 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:40:44,695 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:44,696 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:40:44,701 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:40:49,689 - INFO - 全屏演示已退出
2025-07-31 19:40:49,708 - INFO - 全屏演示已退出
2025-07-31 19:41:13,720 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:13,721 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:13,732 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:13,733 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:18,727 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:18,728 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:18,738 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:18,739 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:25,739 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:41:25,740 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:25,741 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:25,745 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:41:25,748 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:41:25,749 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:25,750 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:25,755 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:41:26,748 - INFO - 全屏演示已退出
2025-07-31 19:41:26,760 - INFO - 全屏演示已退出
2025-07-31 19:41:49,792 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:41:49,793 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:49,794 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:49,795 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:41:49,797 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:41:49,798 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:49,799 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:49,801 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:41:50,065 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:50,066 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:50,797 - INFO - 全屏演示已退出
2025-07-31 19:41:50,804 - INFO - 全屏演示已退出
2025-07-31 19:41:53,811 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:41:53,812 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025上半年工作总结-黎快人.pptx] (WPP.EXE)
2025-07-31 19:41:53,813 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:53,814 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:53,815 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:53,816 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:53,819 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:41:53,819 - INFO - DEBUG: timer_finished_confirmed=False, current_time=480
2025-07-31 19:41:54,820 - INFO - 全屏演示已退出
2025-07-31 19:41:54,823 - INFO - 全屏演示已退出
2025-07-31 19:41:55,825 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:55,826 - ERROR - 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:41:55,826 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:55,828 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:41:58,829 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:41:58,831 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:58,832 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:41:58,834 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:58,834 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:58,836 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:41:58,836 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:41:58,842 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:02,855 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:07,879 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:08,850 - INFO - 全屏演示已退出
2025-07-31 19:42:08,854 - INFO - 全屏演示已退出
2025-07-31 19:42:10,852 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:10,852 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:10,857 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:10,858 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:24,867 - INFO - 窗口检测已停止
2025-07-31 19:42:24,867 - INFO - 已停止所有音频播放
2025-07-31 19:42:24,945 - INFO - 音频资源清理完成
2025-07-31 19:42:25,258 - INFO - ============================================================
2025-07-31 19:42:25,258 - INFO -  ToolClock 启动中...
2025-07-31 19:42:25,258 - INFO - ============================================================
2025-07-31 19:42:25,258 - INFO -  导入模块...
2025-07-31 19:42:25,301 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:25,832 - INFO - pygame音频系统初始化成功
2025-07-31 19:42:25,833 - INFO -  TimerWindow 导入成功
2025-07-31 19:42:25,833 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 19:42:25,833 - INFO -  创建计时器窗口...
2025-07-31 19:42:26,274 - INFO - 窗口检测已启动
2025-07-31 19:42:26,274 - INFO -  计时器窗口创建成功
2025-07-31 19:42:26,274 - INFO -  创建窗口检测器...
2025-07-31 19:42:26,274 - INFO -  窗口检测器创建成功
2025-07-31 19:42:26,275 - INFO -  启动窗口检测...
2025-07-31 19:42:26,275 - INFO - 窗口检测已启动
2025-07-31 19:42:26,275 - INFO -  窗口检测启动成功
2025-07-31 19:42:26,275 - INFO -  ToolClock已启动！
2025-07-31 19:42:26,275 - INFO -  修复后的逻辑:
2025-07-31 19:42:26,275 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 19:42:26,275 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 19:42:26,276 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 19:42:26,276 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 19:42:26,276 - INFO - ============================================================
2025-07-31 19:42:26,276 - INFO -  实时监控开始（请进行测试）:
2025-07-31 19:42:26,276 - INFO - ============================================================
2025-07-31 19:42:26,276 - INFO -  启动GUI主循环...
2025-07-31 19:42:29,292 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:29,295 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:29,299 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:29,301 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:32,302 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:42:32,303 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:32,304 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:42:32,304 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:32,304 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:32,305 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:32,305 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:32,311 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:36,319 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:41,332 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:42,320 - INFO - 全屏演示已退出
2025-07-31 19:42:42,321 - INFO - 全屏演示已退出
2025-07-31 19:42:44,324 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:42:44,325 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:44,326 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:42:44,326 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:44,327 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:44,327 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:44,328 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:44,331 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:46,341 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:49,347 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:49,351 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:50,340 - INFO - 全屏演示已退出
2025-07-31 19:42:50,343 - INFO - 全屏演示已退出
2025-07-31 19:42:52,344 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:42:52,344 - INFO - 检测到全屏演示: WPS Presentation Slide Show - [2025年上半年11工作总结与展望-李超.pptx] (WPP.EXE)
2025-07-31 19:42:52,345 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:52,346 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:52,346 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:52,347 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:52,348 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:52,348 - INFO - DEBUG: timer_finished_confirmed=False, current_time=10
2025-07-31 19:42:56,370 - INFO - 播放系统蜂鸣音: 800Hz, 500ms
2025-07-31 19:42:57,353 - INFO - 全屏演示已退出
2025-07-31 19:42:57,356 - INFO - 全屏演示已退出
2025-07-31 19:42:58,357 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:58,358 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:58,358 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:58,359 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:59,361 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:59,362 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:59,363 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:42:59,364 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:04,681 - INFO - ============================================================
2025-07-31 19:43:04,681 - INFO -  ToolClock 启动中...
2025-07-31 19:43:04,681 - INFO - ============================================================
2025-07-31 19:43:04,681 - INFO -  导入模块...
2025-07-31 19:43:04,720 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:04,923 - INFO - pygame音频系统初始化成功
2025-07-31 19:43:04,923 - INFO -  TimerWindow 导入成功
2025-07-31 19:43:04,923 - INFO -  WindowDetectorFixed 导入成功
2025-07-31 19:43:04,923 - INFO -  创建计时器窗口...
2025-07-31 19:43:05,082 - INFO - 窗口检测已启动
2025-07-31 19:43:05,082 - INFO -  计时器窗口创建成功
2025-07-31 19:43:05,082 - INFO -  创建窗口检测器...
2025-07-31 19:43:05,082 - INFO -  窗口检测器创建成功
2025-07-31 19:43:05,082 - INFO -  启动窗口检测...
2025-07-31 19:43:05,083 - INFO - 窗口检测已启动
2025-07-31 19:43:05,083 - INFO -  窗口检测启动成功
2025-07-31 19:43:05,083 - INFO -  ToolClock已启动！
2025-07-31 19:43:05,083 - INFO -  修复后的逻辑:
2025-07-31 19:43:05,083 - INFO -    打开PPT/PDF文件 → 识别文件并准备时间（不启动）
2025-07-31 19:43:05,083 - INFO -    按F5或全屏放映 → 自动开始倒计时
2025-07-31 19:43:05,083 - INFO -    退出全屏 → 自动停止倒计时
2025-07-31 19:43:05,083 - INFO -    切换文件 → 更新时间设置（不启动）
2025-07-31 19:43:05,083 - INFO - ============================================================
2025-07-31 19:43:05,083 - INFO -  实时监控开始（请进行测试）:
2025-07-31 19:43:05,083 - INFO - ============================================================
2025-07-31 19:43:05,083 - INFO -  启动GUI主循环...
2025-07-31 19:43:10,094 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:10,096 - INFO - 配置文件加载成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:10,098 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:10,098 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:23,931 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:23,932 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:23,933 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:23,933 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:27,792 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:27,793 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:27,802 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:27,803 - INFO - 配置文件保存成功: C:\Users\<USER>\.flyclock\config.json
2025-07-31 19:43:37,140 - ERROR - python-pptx库未安装，无法分析PPT文件
2025-07-31 19:46:16,301 - INFO - 窗口检测已停止
2025-07-31 19:46:16,301 - INFO - 已停止所有音频播放
2025-07-31 19:46:16,353 - INFO - 音频资源清理完成
2025-07-31 19:46:19,546 - INFO - 窗口检测已停止
2025-07-31 19:46:19,546 - INFO - 已停止所有音频播放
2025-07-31 19:46:19,627 - INFO - 音频资源清理完成
