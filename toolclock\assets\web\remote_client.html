<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <title>FlyClock 远程控制</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            margin: 5px;
        }

        .status-label {
            font-weight: bold;
            margin-right: 10px;
        }

        .status-value {
            padding: 5px 10px;
            background: #4A90E2;
            color: white;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-bottom: 20px;
        }

        .preview-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 20px;
            min-height: 500px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 20px;
        }

        .preview-area {
            width: 100%;
            height: 400px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9f9f9;
            margin-bottom: 15px;
        }

        .preview-image {
            max-width: 100%;
            max-height: 100%;
            border-radius: 5px;
        }

        .preview-placeholder {
            color: #999;
            font-size: 1.2em;
        }

        .page-info {
            text-align: center;
            font-size: 1.1em;
            color: #666;
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section h3 {
            margin-bottom: 15px;
            color: #4A90E2;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 5px;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 80px;
        }

        .btn-primary {
            background: #4A90E2;
            color: white;
        }

        .btn-primary:hover {
            background: #357ABD;
        }

        .btn-success {
            background: #5CB85C;
            color: white;
        }

        .btn-success:hover {
            background: #449D44;
        }

        .btn-warning {
            background: #F0AD4E;
            color: white;
        }

        .btn-warning:hover {
            background: #EC971F;
        }

        .btn-danger {
            background: #D9534F;
            color: white;
        }

        .btn-danger:hover {
            background: #C9302C;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .file-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }

        .file-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .file-item:hover {
            background: #f5f5f5;
        }

        .file-item.selected {
            background: #4A90E2;
            color: white;
        }

        .file-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .file-info {
            font-size: 0.8em;
            color: #666;
        }

        .file-item.selected .file-info {
            color: #ccc;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
        }

        .connected {
            background: #5CB85C;
        }

        .disconnected {
            background: #D9534F;
        }

        .connecting {
            background: #F0AD4E;
        }

        .auth-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .auth-form {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            min-width: 300px;
        }

        .auth-form h2 {
            margin-bottom: 20px;
            color: #4A90E2;
        }

        .auth-form input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 1em;
        }

        .timer-display {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .timer-normal {
            background: #4A90E2;
            color: white;
        }

        .timer-warning {
            background: #FF6B6B;
            color: white;
        }

        .timer-finished {
            background: #FF0000;
            color: white;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .status-bar {
                flex-direction: column;
                align-items: stretch;
                padding: 10px;
            }

            .status-item {
                justify-content: space-between;
                margin: 5px 0;
                padding: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 5px;
            }

            .preview-panel {
                order: 1;  /* 预览面板在顶部 */
                padding: 15px;
            }

            .control-panel {
                order: 2;  /* 控制面板在底部 */
                padding: 15px;
            }

            .preview-area {
                height: 250px;
            }

            .button-group {
                flex-direction: row;
                gap: 8px;
            }

            .btn {
                flex: 1;
                padding: 15px 10px;
                font-size: 16px; /* 防止iOS缩放 */
                font-weight: bold;
                min-height: 50px;
                touch-action: manipulation;
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
            }

            /* 翻页控制在移动端固定在底部 */
            .control-section:last-child {
                position: sticky;
                bottom: 0;
                background: rgba(255, 255, 255, 0.95);
                padding: 15px;
                margin: 0 -15px -15px -15px;
                border-top: 1px solid #ddd;
                z-index: 100;
            }

            .timer-display {
                font-size: 2.5em;
                padding: 20px;
                margin-bottom: 20px;
            }

            .control-section {
                margin-bottom: 20px;
            }

            .control-section h3 {
                font-size: 1.2em;
                margin-bottom: 12px;
            }

            .file-list {
                max-height: 200px;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            .file-item {
                padding: 15px;
                border-bottom: 2px solid #eee;
            }

            .file-name {
                font-size: 1.1em;
            }

            .connection-status {
                top: 10px;
                right: 10px;
                font-size: 0.9em;
                padding: 8px 12px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em;
            }

            .timer-display {
                font-size: 2em;
                padding: 15px;
            }

            .btn {
                padding: 12px 8px;
                font-size: 0.9em;
                min-height: 45px;
            }

            .preview-area {
                height: 350px;  /* 增大移动设备预览高度 */
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn:hover {
                transform: none;
            }

            .btn:active {
                transform: scale(0.95);
                transition: transform 0.1s ease;
            }

            .file-item:hover {
                background: #f5f5f5;
            }

            .file-item:active {
                background: #e0e0e0;
            }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 连接状态指示器 -->
    <div id="connectionStatus" class="connection-status disconnected">
        未连接
    </div>

    <!-- 认证模态框 -->
    <div id="authModal" class="auth-modal hidden">
        <div class="auth-form">
            <h2>身份验证</h2>
            <p>请输入访问密码：</p>
            <input type="password" id="passwordInput" placeholder="密码">
            <div class="button-group">
                <button class="btn btn-primary" onclick="authenticate()">连接</button>
                <button class="btn btn-danger" onclick="cancelAuth()">取消</button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>📱 FlyClock 远程控制</h1>
            <p>PPT/PDF 演示远程控制面板 - 支持手机操作</p>
            <div style="font-size: 0.9em; margin-top: 10px; opacity: 0.8;">
                💡 提示: 在预览区域滑动翻页，点击播放/暂停
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-item">
                <span class="status-label">当前文件:</span>
                <span class="status-value" id="currentFile">未选择</span>
            </div>
            <div class="status-item">
                <span class="status-label">页面:</span>
                <span class="status-value" id="currentPage">-/-</span>
            </div>
            <div class="status-item">
                <span class="status-label">状态:</span>
                <span class="status-value" id="playStatus">停止</span>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 预览面板 -->
            <div class="preview-panel">
                <h3>文件预览</h3>
                <div class="preview-area" id="previewArea">
                    <div class="preview-placeholder">选择文件以显示预览</div>
                </div>
                <div class="page-info" id="pageInfo">-/-</div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <!-- 计时器显示 -->
                <div class="control-section">
                    <h3>计时器</h3>
                    <div class="timer-display timer-normal" id="timerDisplay">00:00</div>
                    <div class="button-group">
                        <button class="btn btn-success" onclick="playPresentation()">播放</button>
                        <button class="btn btn-warning" onclick="pausePresentation()">暂停</button>
                        <button class="btn btn-danger" onclick="resetTimer()">重置</button>
                    </div>
                </div>

                <!-- 翻页控制 -->
                <div class="control-section">
                    <h3>翻页控制</h3>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="prevPage()">⬅️ 上一页</button>
                        <button class="btn btn-primary" onclick="nextPage()">下一页 ➡️</button>
                    </div>
                </div>

                <!-- 全屏控制 -->
                <div class="control-section">
                    <h3>全屏控制</h3>
                    <div class="button-group">
                        <button class="btn btn-success" onclick="enterFullscreen()">🖥️ 进入全屏</button>
                        <button class="btn btn-warning" onclick="exitFullscreen()">📱 退出全屏</button>
                    </div>
                </div>

                <!-- 文件选择 -->
                <div class="control-section">
                    <h3>文件列表</h3>
                    <button class="btn btn-primary" onclick="refreshFiles()" style="width: 100%; margin-bottom: 10px;">刷新文件</button>
                    <div class="file-list" id="fileList">
                        <div style="padding: 20px; text-align: center; color: #999;">
                            点击刷新文件加载文件列表
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="remote_client.js"></script>
</body>
</html>
