#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 构建脚本
使用PyInstaller打包为exe文件
"""

import os
import sys
import shutil
from pathlib import Path

def build_toolclock():
    """构建ToolClock exe文件"""
    print("=" * 50)
    print("ToolClock 构建工具")
    print("=" * 50)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
    except ImportError:
        print("✗ 未安装PyInstaller，正在安装...")
        os.system("pip install pyinstaller")
    
    # 清理旧的构建文件
    build_dirs = ['build', 'dist', '__pycache__']
    for dir_name in build_dirs:
        if os.path.exists(dir_name):
            print(f"✓ 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 删除旧的spec文件
    spec_files = ['toolclock_main.spec', 'ToolClock.spec']
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            print(f"✓ 删除旧spec文件: {spec_file}")
            os.remove(spec_file)
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件模式
        "--windowed",                   # 无控制台窗口
        "--name=ToolClock",             # 输出文件名
        "--icon=flyclock_tool/assets/icon.ico" if os.path.exists("flyclock_tool/assets/icon.ico") else "",
        "--add-data=flyclock_tool;flyclock_tool",  # 添加数据文件
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=win32gui",
        "--hidden-import=win32process",
        "--hidden-import=win32api",
        "--hidden-import=win32con",
        "--hidden-import=pygame",
        "--hidden-import=playsound",
        "--clean",                      # 清理临时文件
        "toolclock_main.py"
    ]
    
    # 过滤空参数
    cmd = [arg for arg in cmd if arg]
    
    print("✓ 开始构建...")
    print(f"命令: {' '.join(cmd)}")
    
    # 执行构建
    result = os.system(' '.join(cmd))
    
    if result == 0:
        print("✓ 构建成功！")
        
        # 检查输出文件
        exe_path = "dist/ToolClock.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"✓ 输出文件: {exe_path}")
            print(f"✓ 文件大小: {file_size:.1f} MB")
            
            # 复制到根目录
            root_exe = "ToolClock.exe"
            if os.path.exists(root_exe):
                os.remove(root_exe)
            shutil.copy2(exe_path, root_exe)
            print(f"✓ 已复制到: {root_exe}")
        else:
            print("✗ 未找到输出文件")
    else:
        print("✗ 构建失败")
    
    # 清理构建文件
    print("✓ 清理构建文件...")
    for dir_name in ['build']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    print("=" * 50)
    print("构建完成")

if __name__ == "__main__":
    build_toolclock()
