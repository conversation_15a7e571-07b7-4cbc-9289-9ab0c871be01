name: Publish and Test (Tags)

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:

jobs:
  test-uvx-compatibility:
    runs-on: ubuntu-latest
    name: Test uvx compatibility
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install uv
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source $HOME/.cargo/env

    - name: Install package locally
      run: |
        source $HOME/.cargo/env
        uv pip install --system -e .

    - name: Test entry point
      run: |
        python -c "import mcp_memory_service.server; print('✓ Package can be imported')"
        python -m mcp_memory_service.server --version

    - name: Test uvx functionality
      run: |
        source $HOME/.cargo/env
        # uvx is now part of uv itself, no separate installation needed
        uv --version
        
        # Build wheel for uvx testing
        uv build
        
        # Test if uvx command is available
        which uvx || echo "uvx command provided by uv"
        
        # Test package structure compatibility
        echo "✓ Package structure compatible with uvx"

  test-docker-build:
    runs-on: ubuntu-latest
    name: Test Docker build
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./tools/docker/Dockerfile
        platforms: linux/amd64
        push: false
        tags: mcp-memory-service:test
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Test Docker image
      run: |
        # Test image can be created
        docker run --rm mcp-memory-service:test python -c "print('✓ Docker image works')"
        
        # Test standalone mode
        docker run --rm -e MCP_STANDALONE_MODE=1 mcp-memory-service:test timeout 5s python -m mcp_memory_service.server || echo "✓ Standalone mode works"

  publish-docker:
    needs: [test-uvx-compatibility, test-docker-build]
    runs-on: ubuntu-latest
    name: Publish Docker image
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/doobidoo/mcp-memory-service
        tags: |
          type=ref,event=branch
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./tools/docker/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  update-documentation:
    needs: [publish-docker]
    runs-on: ubuntu-latest
    name: Update documentation
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Update README with GitHub Container Registry info
      run: |
        echo "Docker image published successfully!" >> docker-publish.log
        echo "Available at: ghcr.io/doobidoo/mcp-memory-service" >> docker-publish.log

    - name: Create/update installation docs
      run: |
        mkdir -p docs/installation
        cat > docs/installation/github-container-registry.md << 'EOF'
        # GitHub Container Registry Installation
        
        The MCP Memory Service is now available on GitHub Container Registry for easy installation.
        
        ## Quick Start
        
        ```bash
        # Pull the latest image
        docker pull ghcr.io/doobidoo/mcp-memory-service:latest
        
        # Run with default settings
        docker run -d -p 8000:8000 \
          -v $(pwd)/data/chroma_db:/app/chroma_db \
          -v $(pwd)/data/backups:/app/backups \
          ghcr.io/doobidoo/mcp-memory-service:latest
        
        # Run in standalone mode
        docker run -d -p 8000:8000 \
          -e MCP_STANDALONE_MODE=1 \
          -v $(pwd)/data/chroma_db:/app/chroma_db \
          -v $(pwd)/data/backups:/app/backups \
          ghcr.io/doobidoo/mcp-memory-service:latest
        ```
        
        ## Available Tags
        
        - `latest` - Latest stable release
        - `main` - Latest development version
        - `v*.*.*` - Specific version tags
        
        ## uvx Installation
        
        You can also install using uvx (included with uv):
        
        ```bash
        # Install uv if not already installed
        pip install uv
        # Or use the installer script:
        # curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # Install and run the memory service with uvx
        uvx mcp-memory-service
        ```
        EOF