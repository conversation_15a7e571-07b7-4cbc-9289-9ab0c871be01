"""
设置窗口模块
提供图形化的配置管理界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import os
import sys
import time
from pathlib import Path

# 智能导入模块
try:
    from core.config_manager import config_manager
except ImportError:
    try:
        from flyclock_tool.core.config_manager import config_manager
    except ImportError:
        import json
        class SimpleConfig:
            def __init__(self):
                self.config = {}
            def get_setting(self, key, default=None):
                return self.config.get(key, default)
            def set_setting(self, key, value):
                self.config[key] = value
            def save_config(self):
                pass
        config_manager = SimpleConfig()

try:
    from core.audio_player import get_audio_player
except ImportError:
    try:
        from flyclock_tool.core.audio_player import get_audio_player
    except ImportError:
        def get_audio_player():
            class SimpleAudio:
                def play_warning_sound(self):
                    pass
                def play_end_sound(self):
                    pass
            return SimpleAudio()

try:
    from core.file_analyzer import analyze_presentation_file
except ImportError:
    try:
        from flyclock_tool.core.file_analyzer import analyze_presentation_file
    except ImportError:
        def analyze_presentation_file(file_path):
            return {"pages": 1, "duration": 0}


class SettingsWindow:
    """设置窗口类"""
    
    def __init__(self, parent=None):
        """
        初始化设置窗口
        
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.config_manager = config_manager
        self.audio_player = get_audio_player()
        
        # 创建设置窗口
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("设置")
        self.window.geometry("800x650")
        self.window.minsize(750, 600)  # 设置最小尺寸
        self.window.resizable(True, True)
        
        # 设置窗口图标和属性
        self.window.transient(parent)
        self.window.grab_set()
        
        # 创建界面
        self.create_widgets()
        
        # 加载当前配置
        self.load_current_config()
        
        # 居中显示
        self.center_window()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_timer_tab()
        self.create_control_tab()
        self.create_appearance_tab()
        self.create_audio_tab()
        
        # 创建底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 底部按钮
        ttk.Button(button_frame, text="导出", command=self.export_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导入", command=self.import_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置", command=self.reset_config).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Button(button_frame, text="确定", command=self.apply_and_close).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="应用", command=self.apply_config).pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_timer_tab(self):
        """创建时长设置标签页"""
        timer_frame = ttk.Frame(self.notebook)
        self.notebook.add(timer_frame, text="时长设置")
        
        # 默认计时时长
        ttk.Label(timer_frame, text="默认计时时长:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.default_duration_var = tk.StringVar()
        duration_frame = ttk.Frame(timer_frame)
        duration_frame.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Entry(duration_frame, textvariable=self.default_duration_var, width=10).pack(side=tk.LEFT)
        ttk.Label(duration_frame, text="(格式: HH:MM:SS)").pack(side=tk.LEFT, padx=(5, 0))
        
        # 计时模式
        ttk.Label(timer_frame, text="计时模式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        mode_frame = ttk.Frame(timer_frame)
        mode_frame.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.timer_mode_var = tk.StringVar()
        ttk.Radiobutton(mode_frame, text="倒计时", variable=self.timer_mode_var, 
                       value="countdown").pack(side=tk.LEFT)
        ttk.Radiobutton(mode_frame, text="正计时", variable=self.timer_mode_var, 
                       value="countup").pack(side=tk.LEFT, padx=(10, 0))
        
        # 自动开始
        self.auto_start_var = tk.BooleanVar()
        ttk.Checkbutton(timer_frame, text="自动开始计时", 
                       variable=self.auto_start_var).grid(row=2, column=0, columnspan=2, 
                                                         sticky=tk.W, padx=5, pady=5)
        
        # 警告时间
        ttk.Label(timer_frame, text="提前警告时间:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        warning_frame = ttk.Frame(timer_frame)
        warning_frame.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.warning_time_var = tk.IntVar()
        ttk.Spinbox(warning_frame, from_=0, to=300, textvariable=self.warning_time_var, 
                   width=8).pack(side=tk.LEFT)
        ttk.Label(warning_frame, text="秒").pack(side=tk.LEFT, padx=(5, 0))
        
        # 独立文件计时时长设置区域
        separator = ttk.Separator(timer_frame, orient=tk.HORIZONTAL)
        separator.grid(row=4, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=10)
        
        ttk.Label(timer_frame, text="独立文件计时时长设置清单").grid(row=5, column=0, columnspan=2, 
                                                        sticky=tk.W, padx=5, pady=5)
        
        # 文件列表框架
        list_frame = ttk.Frame(timer_frame)
        list_frame.grid(row=6, column=0, columnspan=2, sticky=tk.NSEW, padx=5, pady=5)
        
        # 配置网格权重
        timer_frame.grid_rowconfigure(6, weight=1)
        timer_frame.grid_columnconfigure(1, weight=1)
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 创建文件列表
        columns = ("文件", "计时时长", "来源")
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=8)

        # 设置列标题和排序功能
        self.sort_column = None
        self.sort_reverse = False

        self.file_tree.heading("文件", text="PPT/PDF 文件 ▲▼",
                              command=lambda: self.sort_file_list("文件"))
        self.file_tree.heading("计时时长", text="计时时长 ▲▼",
                              command=lambda: self.sort_file_list("计时时长"))
        self.file_tree.heading("来源", text="来源 ▲▼",
                              command=lambda: self.sort_file_list("来源"))
        
        # 设置列宽
        self.file_tree.column("文件", width=250)
        self.file_tree.column("计时时长", width=100)
        self.file_tree.column("来源", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.file_tree.grid(row=0, column=0, sticky=tk.NSEW)
        scrollbar.grid(row=0, column=1, sticky=tk.NS)
        
        # 按钮框架
        button_frame = ttk.Frame(list_frame)
        button_frame.grid(row=0, column=2, sticky=tk.NS, padx=(5, 0))
        
        ttk.Button(button_frame, text="新增", command=self.add_file_timer).pack(pady=(0, 5))
        ttk.Button(button_frame, text="修改", command=self.edit_file_timer).pack(pady=(0, 5))
        ttk.Button(button_frame, text="删除", command=self.delete_file_timer).pack(pady=(0, 5))
        ttk.Button(button_frame, text="智能分析", command=self.smart_analyze_file).pack(pady=(0, 5))
        ttk.Button(button_frame, text="上移", command=self.move_file_up).pack(pady=(0, 5))
        ttk.Button(button_frame, text="下移", command=self.move_file_down).pack()

        # 绑定拖拽事件
        self.file_tree.bind("<Button-1>", self.on_file_click)
        self.file_tree.bind("<B1-Motion>", self.on_file_drag)
        self.file_tree.bind("<ButtonRelease-1>", self.on_file_drop)

        # 拖拽相关变量
        self.drag_item = None
        self.drag_start_y = None
    
    def create_control_tab(self):
        """创建控制设置标签页"""
        control_frame = ttk.Frame(self.notebook)
        self.notebook.add(control_frame, text="控制设置")
        
        # 计时模式选择
        mode_group = ttk.LabelFrame(control_frame, text="计时模式")
        mode_group.pack(fill=tk.X, padx=5, pady=5)
        
        self.control_mode_var = tk.StringVar()
        ttk.Radiobutton(mode_group, text="倒计时", variable=self.control_mode_var, 
                       value="countdown").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(mode_group, text="正计时", variable=self.control_mode_var, 
                       value="countup").pack(anchor=tk.W, padx=5, pady=2)
        
        # 计时结束前设置
        before_group = ttk.LabelFrame(control_frame, text="在计时结束前")
        before_group.pack(fill=tk.X, padx=5, pady=5)
        
        before_frame = ttk.Frame(before_group)
        before_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.before_time_var = tk.StringVar()
        ttk.Entry(before_frame, textvariable=self.before_time_var, width=10).pack(side=tk.LEFT)
        ttk.Label(before_frame, text="播放音乐:").pack(side=tk.LEFT, padx=(5, 0))
        
        self.before_sound_var = tk.StringVar()
        ttk.Entry(before_frame, textvariable=self.before_sound_var, width=30).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(before_frame, text="浏览", command=self.browse_before_sound).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(before_frame, text="试听", command=self.test_before_sound).pack(side=tk.LEFT, padx=(5, 0))
        
        # 计时时间到设置
        end_group = ttk.LabelFrame(control_frame, text="计时时间到")
        end_group.pack(fill=tk.X, padx=5, pady=5)
        
        # 结束行为
        self.end_action_var = tk.StringVar()
        ttk.Radiobutton(end_group, text="保持全屏", variable=self.end_action_var, 
                       value="keep_fullscreen").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(end_group, text="退出全屏", variable=self.end_action_var, 
                       value="exit_fullscreen").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(end_group, text="黑屏", variable=self.end_action_var, 
                       value="black_screen").pack(anchor=tk.W, padx=5, pady=2)
        
        # 播放音乐设置
        self.end_sound_enabled_var = tk.BooleanVar()
        sound_frame = ttk.Frame(end_group)
        sound_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Checkbutton(sound_frame, text="播放音乐:", 
                       variable=self.end_sound_enabled_var).pack(side=tk.LEFT)
        
        self.end_sound_var = tk.StringVar()
        ttk.Entry(sound_frame, textvariable=self.end_sound_var, width=30).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(sound_frame, text="浏览", command=self.browse_end_sound).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(sound_frame, text="试听", command=self.test_end_sound).pack(side=tk.LEFT, padx=(5, 0))
        
        # 其他设置
        other_group = ttk.LabelFrame(control_frame, text="其他设置")
        other_group.pack(fill=tk.X, padx=5, pady=5)
        
        # 自动退出延迟
        delay_frame = ttk.Frame(other_group)
        delay_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(delay_frame, text="在").pack(side=tk.LEFT)
        self.auto_exit_delay_var = tk.IntVar()
        ttk.Spinbox(delay_frame, from_=0, to=300, textvariable=self.auto_exit_delay_var, 
                   width=5).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(delay_frame, text="秒内，意外退出不中断计时").pack(side=tk.LEFT, padx=(5, 0))
        
        # 其他选项
        self.remind_break_var = tk.BooleanVar()
        ttk.Checkbutton(other_group, text="提醒闲时间", 
                       variable=self.remind_break_var).pack(anchor=tk.W, padx=5, pady=2)
        
        self.auto_pause_resume_var = tk.BooleanVar()
        ttk.Checkbutton(other_group, text="允许手动暂停、重置计时", 
                       variable=self.auto_pause_resume_var).pack(anchor=tk.W, padx=5, pady=2)
        
        # 快捷键设置
        hotkey_frame = ttk.Frame(other_group)
        hotkey_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(hotkey_frame, text="暂停快捷键:").pack(side=tk.LEFT)
        self.pause_hotkey_var = tk.StringVar()
        ttk.Entry(hotkey_frame, textvariable=self.pause_hotkey_var, width=8).pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Label(hotkey_frame, text="重置快捷键:").pack(side=tk.LEFT, padx=(20, 0))
        self.reset_hotkey_var = tk.StringVar()
        ttk.Entry(hotkey_frame, textvariable=self.reset_hotkey_var, width=8).pack(side=tk.LEFT, padx=(5, 0))

    def create_appearance_tab(self):
        """创建外观设置标签页"""
        appearance_frame = ttk.Frame(self.notebook)
        self.notebook.add(appearance_frame, text="外观设置")

        # 颜色设置
        color_group = ttk.LabelFrame(appearance_frame, text="颜色设置")
        color_group.pack(fill=tk.X, padx=5, pady=5)

        # 正常状态颜色
        normal_frame = ttk.Frame(color_group)
        normal_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(normal_frame, text="正常").pack(side=tk.LEFT)

        # 前景色
        ttk.Label(normal_frame, text="前景:").pack(side=tk.LEFT, padx=(20, 5))
        self.normal_fg_var = tk.StringVar()
        self.normal_fg_button = tk.Button(normal_frame, width=8, height=1,
                                         command=lambda: self.choose_color(self.normal_fg_var, self.normal_fg_button))
        self.normal_fg_button.pack(side=tk.LEFT, padx=(0, 10))

        # 背景色
        ttk.Label(normal_frame, text="背景:").pack(side=tk.LEFT, padx=(10, 5))
        self.normal_bg_var = tk.StringVar()
        self.normal_bg_button = tk.Button(normal_frame, width=8, height=1,
                                         command=lambda: self.choose_color(self.normal_bg_var, self.normal_bg_button))
        self.normal_bg_button.pack(side=tk.LEFT)

        # 提示状态颜色
        warning_frame = ttk.Frame(color_group)
        warning_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(warning_frame, text="提示").pack(side=tk.LEFT)

        # 前景色
        ttk.Label(warning_frame, text="前景:").pack(side=tk.LEFT, padx=(20, 5))
        self.warning_fg_var = tk.StringVar()
        self.warning_fg_button = tk.Button(warning_frame, width=8, height=1,
                                          command=lambda: self.choose_color(self.warning_fg_var, self.warning_fg_button))
        self.warning_fg_button.pack(side=tk.LEFT, padx=(0, 10))

        # 背景色
        ttk.Label(warning_frame, text="背景:").pack(side=tk.LEFT, padx=(10, 5))
        self.warning_bg_var = tk.StringVar()
        self.warning_bg_button = tk.Button(warning_frame, width=8, height=1,
                                          command=lambda: self.choose_color(self.warning_bg_var, self.warning_bg_button))
        self.warning_bg_button.pack(side=tk.LEFT)

        # 字体设置
        font_group = ttk.LabelFrame(appearance_frame, text="字体设置")
        font_group.pack(fill=tk.X, padx=5, pady=5)

        font_frame = ttk.Frame(font_group)
        font_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(font_frame, text="调整字体").pack(side=tk.LEFT)
        ttk.Button(font_frame, text="放大字体").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(font_frame, text="缩小字体").pack(side=tk.LEFT, padx=(10, 0))

        # 透明度设置
        transparency_group = ttk.LabelFrame(appearance_frame, text="透明度设置")
        transparency_group.pack(fill=tk.X, padx=5, pady=5)

        transparency_frame = ttk.Frame(transparency_group)
        transparency_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(transparency_frame, text="提示前颜色:").pack(side=tk.LEFT)
        self.transparency_var = tk.DoubleVar()
        transparency_scale = ttk.Scale(transparency_frame, from_=0.1, to=1.0,
                                     variable=self.transparency_var, orient=tk.HORIZONTAL, length=200)
        transparency_scale.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Label(transparency_frame, text="不透明度:").pack(side=tk.LEFT, padx=(10, 0))
        self.transparency_label = ttk.Label(transparency_frame, text="90%")
        self.transparency_label.pack(side=tk.LEFT, padx=(5, 0))

        # 绑定透明度变化事件
        transparency_scale.configure(command=self.on_transparency_change)

        # 显示选项
        display_group = ttk.LabelFrame(appearance_frame, text="显示选项")
        display_group.pack(fill=tk.X, padx=5, pady=5)

        # 第一列选项
        col1_frame = ttk.Frame(display_group)
        col1_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        self.show_machine_time_var = tk.BooleanVar()
        ttk.Checkbutton(col1_frame, text="显示机器时间",
                       variable=self.show_machine_time_var).pack(anchor=tk.W, pady=2)

        self.show_hint_without_countdown_var = tk.BooleanVar()
        ttk.Checkbutton(col1_frame, text="提示时不显示倒计时",
                       variable=self.show_hint_without_countdown_var).pack(anchor=tk.W, pady=2)

        self.hide_when_no_ppt_var = tk.BooleanVar()
        ttk.Checkbutton(col1_frame, text="没有PPT/PDF文件时隐藏窗口",
                       variable=self.hide_when_no_ppt_var).pack(anchor=tk.W, pady=2)

        self.hide_when_timer_off_var = tk.BooleanVar()
        ttk.Checkbutton(col1_frame, text="计时时长为零时隐藏窗口",
                       variable=self.hide_when_timer_off_var).pack(anchor=tk.W, pady=2)

        # 第二列选项
        col2_frame = ttk.Frame(display_group)
        col2_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        self.background_transparent_var = tk.BooleanVar()
        ttk.Checkbutton(col2_frame, text="背景透明",
                       variable=self.background_transparent_var).pack(anchor=tk.W, pady=2)

        self.show_controls_var = tk.BooleanVar()
        ttk.Checkbutton(col2_frame, text="显示控制栏",
                       variable=self.show_controls_var).pack(anchor=tk.W, pady=2)

        self.show_in_fullscreen_var = tk.BooleanVar()
        ttk.Checkbutton(col2_frame, text="在全屏时显示计时器",
                       variable=self.show_in_fullscreen_var).pack(anchor=tk.W, pady=2)

    def create_audio_tab(self):
        """创建音频设置标签页"""
        audio_frame = ttk.Frame(self.notebook)
        self.notebook.add(audio_frame, text="音频设置")

        # 音频启用
        self.audio_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(audio_frame, text="启用音频提示",
                       variable=self.audio_enabled_var).pack(anchor=tk.W, padx=5, pady=5)

        # 警告音设置
        warning_group = ttk.LabelFrame(audio_frame, text="警告音设置")
        warning_group.pack(fill=tk.X, padx=5, pady=5)

        self.warning_audio_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(warning_group, text="启用警告音",
                       variable=self.warning_audio_enabled_var).pack(anchor=tk.W, padx=5, pady=2)

        warning_file_frame = ttk.Frame(warning_group)
        warning_file_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(warning_file_frame, text="警告音文件:").pack(side=tk.LEFT)
        self.warning_sound_file_var = tk.StringVar()
        ttk.Entry(warning_file_frame, textvariable=self.warning_sound_file_var, width=40).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(warning_file_frame, text="浏览", command=self.browse_warning_sound).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(warning_file_frame, text="试听", command=self.test_warning_sound).pack(side=tk.LEFT, padx=(5, 0))

        # 结束音设置
        end_group = ttk.LabelFrame(audio_frame, text="结束音设置")
        end_group.pack(fill=tk.X, padx=5, pady=5)

        end_file_frame = ttk.Frame(end_group)
        end_file_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(end_file_frame, text="结束音文件:").pack(side=tk.LEFT)
        self.end_sound_file_var = tk.StringVar()
        ttk.Entry(end_file_frame, textvariable=self.end_sound_file_var, width=40).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(end_file_frame, text="浏览", command=self.browse_end_sound_file).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(end_file_frame, text="试听", command=self.test_end_sound_file).pack(side=tk.LEFT, padx=(5, 0))



    def load_current_config(self):
        """加载当前配置到界面"""
        try:
            # 时长设置
            self.default_duration_var.set(self.config_manager.get_setting('timer.default_duration', '00:08:00'))
            self.timer_mode_var.set(self.config_manager.get_setting('timer.mode', 'countdown'))
            self.auto_start_var.set(self.config_manager.get_setting('timer.auto_start', True))
            self.warning_time_var.set(self.config_manager.get_setting('timer.warning_time', 60))

            # 控制设置
            self.control_mode_var.set(self.config_manager.get_setting('timer.mode', 'countdown'))
            self.before_time_var.set(self.config_manager.get_setting('timer.warning_time', 60))
            self.before_sound_var.set(self.config_manager.get_setting('audio.warning_sound', ''))
            self.end_action_var.set(self.config_manager.get_setting('control.timer_end_action', 'keep_fullscreen'))
            self.end_sound_enabled_var.set(self.config_manager.get_setting('audio.enabled', True))
            self.end_sound_var.set(self.config_manager.get_setting('audio.end_sound', ''))
            self.auto_exit_delay_var.set(self.config_manager.get_setting('control.auto_exit_delay', 0))
            self.remind_break_var.set(self.config_manager.get_setting('control.remind_break_time', True))
            self.auto_pause_resume_var.set(self.config_manager.get_setting('control.auto_pause_resume', True))
            self.pause_hotkey_var.set(self.config_manager.get_setting('control.hotkeys.pause_resume', 'F3'))
            self.reset_hotkey_var.set(self.config_manager.get_setting('control.hotkeys.reset', 'F4'))

            # 外观设置
            self.normal_fg_var.set(self.config_manager.get_setting('display.colors.normal.foreground', '#FFFFFF'))
            self.normal_bg_var.set(self.config_manager.get_setting('display.colors.normal.background', '#4A90E2'))
            self.warning_fg_var.set(self.config_manager.get_setting('display.colors.warning.foreground', '#FFFFFF'))
            self.warning_bg_var.set(self.config_manager.get_setting('display.colors.warning.background', '#FF0000'))
            self.transparency_var.set(self.config_manager.get_setting('display.transparency', 0.9))
            self.show_machine_time_var.set(self.config_manager.get_setting('appearance.show_machine_time', False))
            self.show_hint_without_countdown_var.set(self.config_manager.get_setting('appearance.show_hint_without_countdown', False))
            self.hide_when_no_ppt_var.set(self.config_manager.get_setting('display.hide_when_no_ppt', False))
            self.hide_when_timer_off_var.set(self.config_manager.get_setting('display.hide_when_timer_off', False))
            self.background_transparent_var.set(self.config_manager.get_setting('appearance.background_transparent', False))
            self.show_controls_var.set(self.config_manager.get_setting('display.show_controls', True))
            self.show_in_fullscreen_var.set(self.config_manager.get_setting('display.show_in_fullscreen', True))

            # 音频设置
            self.audio_enabled_var.set(self.config_manager.get_setting('audio.enabled', True))
            self.warning_audio_enabled_var.set(self.config_manager.get_setting('audio.warning_enabled', True))
            self.warning_sound_file_var.set(self.config_manager.get_setting('audio.warning_sound', ''))
            self.end_sound_file_var.set(self.config_manager.get_setting('audio.end_sound', ''))



            # 更新颜色按钮显示
            self.update_color_button(self.normal_fg_button, self.normal_fg_var.get())
            self.update_color_button(self.normal_bg_button, self.normal_bg_var.get())
            self.update_color_button(self.warning_fg_button, self.warning_fg_var.get())
            self.update_color_button(self.warning_bg_button, self.warning_bg_var.get())

            # 更新透明度标签
            self.on_transparency_change(self.transparency_var.get())

            # 加载文件特定计时器
            self.load_file_timers()

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")

    def load_file_timers(self):
        """加载文件特定计时器列表"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 获取配置数据
        file_timers = self.config_manager.get_setting('files.file_specific_timers', {})
        analysis_cache = self.config_manager.get_setting('files.analysis_cache', {})
        file_order = self.config_manager.get_setting('files.file_order', [])

        # 创建文件信息字典
        file_info = {}

        # 收集手动设置的文件
        for file_path, duration in file_timers.items():
            filename = os.path.basename(file_path)
            file_info[filename] = {
                'duration': self._format_duration_display(duration),
                'source': "手动设置"
            }

        # 收集智能分析的结果
        for file_path, cache_data in analysis_cache.items():
            filename = os.path.basename(file_path)

            # 如果没有手动设置，则使用智能分析结果
            if filename not in file_info:
                analysis_result = cache_data.get('analysis_result', {})
                suggested_time = analysis_result.get('suggested_time', 1800)
                file_type = analysis_result.get('file_type', 'unknown')
                page_count = analysis_result.get('page_count', 0)

                duration_display = self._format_duration_display(suggested_time)
                source_info = f"智能分析({page_count}{'页' if file_type == 'pdf' else '张'})"

                file_info[filename] = {
                    'duration': duration_display,
                    'source': source_info
                }

        # 按照保存的顺序插入文件
        inserted_files = set()
        for filename in file_order:
            if filename in file_info:
                info = file_info[filename]
                self.file_tree.insert('', 'end', values=(filename, info['duration'], info['source']))
                inserted_files.add(filename)

        # 插入未在顺序列表中的文件（新添加的文件）
        for filename, info in file_info.items():
            if filename not in inserted_files:
                self.file_tree.insert('', 'end', values=(filename, info['duration'], info['source']))

    def choose_color(self, color_var, button):
        """选择颜色"""
        color = colorchooser.askcolor(initialcolor=color_var.get())
        if color[1]:  # 如果用户选择了颜色
            color_var.set(color[1])
            self.update_color_button(button, color[1])

    def update_color_button(self, button, color):
        """更新颜色按钮显示"""
        try:
            button.configure(bg=color)
        except tk.TclError:
            # 如果颜色无效，使用默认颜色
            button.configure(bg='#FFFFFF')

    def on_transparency_change(self, value):
        """透明度变化事件"""
        try:
            transparency = float(value)
            percentage = int(transparency * 100)
            self.transparency_label.configure(text=f"{percentage}%")
        except:
            pass

    def add_file_timer(self):
        """添加文件计时器"""
        dialog = FileTimerDialog(self.window, "添加文件计时器")
        if dialog.result:
            file_path, duration = dialog.result
            filename = os.path.basename(file_path)
            self.file_tree.insert('', 'end', values=(filename, duration, "手动设置"))

            # 询问是否更新默认计时时长
            if messagebox.askyesno("更新默认时长",
                f"是否将默认计时时长更新为此文件的时长？\n"
                f"文件时长: {duration}\n"
                f"当前默认: {self.default_duration_var.get()}"):

                self.default_duration_var.set(duration)
                print(f" 默认计时时长已更新为: {duration}")

    def edit_file_timer(self):
        """编辑文件计时器"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的项目")
            return

        item = selection[0]
        values = self.file_tree.item(item, 'values')

        # 检查是否为智能分析的结果
        if len(values) >= 3 and "智能分析" in values[2]:
            if not messagebox.askyesno("确认", "这是智能分析的结果，编辑后将变为手动设置。是否继续？"):
                return

        dialog = FileTimerDialog(self.window, "编辑文件计时器", values[0], values[1])
        if dialog.result:
            file_path, duration = dialog.result
            filename = os.path.basename(file_path)
            self.file_tree.item(item, values=(filename, duration, "手动设置"))

    def delete_file_timer(self):
        """删除文件计时器"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的项目")
            return

        if messagebox.askyesno("确认", "确定要删除选中的项目吗？"):
            # 获取要删除的文件名
            deleted_files = []
            for item in selection:
                values = self.file_tree.item(item, 'values')
                if values:
                    filename = values[0]  # 文件名在第一列
                    deleted_files.append(filename)
                self.file_tree.delete(item)

            # 从配置中删除这些文件
            if deleted_files:
                file_timers = self.config_manager.get_setting('files.file_specific_timers', {})
                analysis_cache = self.config_manager.get_setting('files.analysis_cache', {})
                file_order = self.config_manager.get_setting('files.file_order', [])

                for filename in deleted_files:
                    # 从文件特定计时器中删除（支持文件名和文件路径两种方式）
                    keys_to_remove = []
                    for key in file_timers.keys():
                        if key == filename or os.path.basename(key) == filename:
                            keys_to_remove.append(key)

                    for key in keys_to_remove:
                        del file_timers[key]
                        print(f" 从文件特定计时器中删除: {key}")

                    # 从分析缓存中删除（支持文件名和文件路径两种方式）
                    cache_keys_to_remove = []
                    for key in analysis_cache.keys():
                        if key == filename or os.path.basename(key) == filename:
                            cache_keys_to_remove.append(key)

                    for key in cache_keys_to_remove:
                        del analysis_cache[key]
                        print(f" 从分析缓存中删除: {key}")

                    # 从文件顺序中删除
                    if filename in file_order:
                        file_order.remove(filename)
                        print(f" 从文件顺序中删除: {filename}")

                # 保存更新后的配置
                self.config_manager.set_setting('files.file_specific_timers', file_timers)
                self.config_manager.set_setting('files.analysis_cache', analysis_cache)
                self.config_manager.set_setting('files.file_order', file_order)
                self.config_manager.save_config()

                print(f" 已删除文件: {', '.join(deleted_files)}")

                # 刷新文件列表显示
                self.load_file_timers()

    def sort_file_list(self, column):
        """排序文件列表"""
        # 如果点击的是同一列，则反转排序
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False

        # 获取所有项目的数据
        items = []
        for item in self.file_tree.get_children():
            values = self.file_tree.item(item)['values']
            items.append((item, values))

        # 根据列进行排序
        column_index = {"文件": 0, "计时时长": 1, "来源": 2}[column]

        if column == "计时时长":
            # 时长排序需要特殊处理，转换为秒数
            def time_to_seconds(time_str):
                try:
                    parts = time_str.split(':')
                    if len(parts) == 3:  # HH:MM:SS
                        return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
                    elif len(parts) == 2:  # MM:SS
                        return int(parts[0]) * 60 + int(parts[1])
                    else:
                        return 0
                except:
                    return 0

            items.sort(key=lambda x: time_to_seconds(x[1][column_index]),
                      reverse=self.sort_reverse)
        else:
            # 文本排序
            items.sort(key=lambda x: x[1][column_index].lower(),
                      reverse=self.sort_reverse)

        # 重新插入排序后的项目
        for index, (item, values) in enumerate(items):
            self.file_tree.move(item, '', index)

        # 更新列标题显示排序状态
        for col in ["文件", "计时时长", "来源"]:
            if col == column:
                arrow = " ▼" if self.sort_reverse else " ▲"
                if col == "文件":
                    self.file_tree.heading(col, text=f"PPT/PDF 文件{arrow}")
                else:
                    self.file_tree.heading(col, text=f"{col}{arrow}")
            else:
                if col == "文件":
                    self.file_tree.heading(col, text="PPT/PDF 文件 ▲▼")
                else:
                    self.file_tree.heading(col, text=f"{col} ▲▼")

    def smart_analyze_file(self):
        """智能分析文件"""
        # 选择文件
        file_path = filedialog.askopenfilename(
            title="选择要分析的文件",
            filetypes=[
                ("演示文件", "*.pptx *.ppt *.pdf"),
                ("PowerPoint文件", "*.pptx *.ppt"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )

        if not file_path:
            return

        try:
            # 显示分析进度
            progress_window = tk.Toplevel(self.window)
            progress_window.title("分析中...")
            progress_window.geometry("300x100")
            progress_window.transient(self.window)
            progress_window.grab_set()

            # 居中显示
            progress_window.update_idletasks()
            x = (progress_window.winfo_screenwidth() // 2) - (300 // 2)
            y = (progress_window.winfo_screenheight() // 2) - (100 // 2)
            progress_window.geometry(f"300x100+{x}+{y}")

            ttk.Label(progress_window, text="正在分析文件，请稍候...").pack(expand=True)
            progress_window.update()

            # 执行分析
            analysis_result = analyze_presentation_file(file_path)

            # 关闭进度窗口
            progress_window.destroy()

            if analysis_result.get('analysis_success'):
                filename = os.path.basename(file_path)
                suggested_time = analysis_result.get('suggested_time', 1800)
                file_type = analysis_result.get('file_type', 'unknown')
                page_count = analysis_result.get('page_count', 0)

                # 显示分析结果
                duration_display = self._format_duration_display(suggested_time)
                source_info = f"智能分析({page_count}{'页' if file_type == 'pdf' else '张'})"

                # 检查是否已存在
                existing_item = None
                for item in self.file_tree.get_children():
                    values = self.file_tree.item(item, 'values')
                    if values[0] == filename:
                        existing_item = item
                        break

                if existing_item:
                    # 更新现有项目
                    current_values = self.file_tree.item(existing_item, 'values')
                    if "手动设置" not in current_values[2]:  # 只更新非手动设置的项目
                        self.file_tree.item(existing_item, values=(filename, duration_display, source_info))
                else:
                    # 添加新项目
                    self.file_tree.insert('', 'end', values=(filename, duration_display, source_info))

                # 保存分析结果到配置
                analysis_cache = self.config_manager.get_setting('files.analysis_cache', {})
                analysis_cache[file_path] = {
                    'analysis_result': analysis_result,
                    'analyzed_at': time.time()
                }
                self.config_manager.set_setting('files.analysis_cache', analysis_cache)

                # 询问是否更新默认计时时长
                if messagebox.askyesno("更新默认时长",
                    f"是否将默认计时时长更新为分析建议的时长？\n"
                    f"建议时长: {self._format_time_readable(suggested_time)}\n"
                    f"当前默认: {self.default_duration_var.get()}"):

                    # 更新默认计时时长
                    duration_formatted = self._format_duration_display(suggested_time)
                    self.default_duration_var.set(duration_formatted)
                    print(f" 默认计时时长已更新为: {duration_formatted}")

                messagebox.showinfo("分析完成",
                    f"文件分析完成！\n"
                    f"文件: {filename}\n"
                    f"类型: {file_type.upper()}\n"
                    f"页数: {page_count}{'页' if file_type == 'pdf' else '张'}\n"
                    f"建议时长: {self._format_time_readable(suggested_time)}")
            else:
                messagebox.showerror("分析失败", "无法分析该文件，请检查文件格式是否正确。")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("错误", f"分析文件时出错: {e}")

    def _format_duration_display(self, duration):
        """格式化时长显示（HH:MM:SS格式）"""
        if isinstance(duration, str):
            return duration

        hours = duration // 3600
        minutes = (duration % 3600) // 60
        seconds = duration % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def _format_time_readable(self, seconds):
        """格式化时间为可读格式"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds == 0:
                return f"{minutes}分钟"
            else:
                return f"{minutes}分{remaining_seconds}秒"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes == 0:
                return f"{hours}小时"
            else:
                return f"{hours}小时{remaining_minutes}分钟"

    def browse_before_sound(self):
        """浏览警告音文件"""
        filename = filedialog.askopenfilename(
            title="选择警告音文件",
            filetypes=[("音频文件", "*.wav *.mp3 *.ogg"), ("所有文件", "*.*")]
        )
        if filename:
            self.before_sound_var.set(filename)

    def test_before_sound(self):
        """试听警告音"""
        sound_file = self.before_sound_var.get()
        if sound_file and os.path.exists(sound_file):
            try:
                if self.audio_player.test_sound(sound_file):
                    messagebox.showinfo("提示", f"正在播放: {os.path.basename(sound_file)}")
                else:
                    messagebox.showerror("错误", "音频播放失败")
            except Exception as e:
                messagebox.showerror("错误", f"播放音频失败: {e}")
        else:
            messagebox.showwarning("警告", "请先选择有效的音频文件")

    def browse_end_sound(self):
        """浏览结束音文件"""
        filename = filedialog.askopenfilename(
            title="选择结束音文件",
            filetypes=[("音频文件", "*.wav *.mp3 *.ogg"), ("所有文件", "*.*")]
        )
        if filename:
            self.end_sound_var.set(filename)

    def test_end_sound(self):
        """试听结束音"""
        sound_file = self.end_sound_var.get()
        if sound_file and os.path.exists(sound_file):
            try:
                if self.audio_player.test_sound(sound_file):
                    messagebox.showinfo("提示", f"正在播放: {os.path.basename(sound_file)}")
                else:
                    messagebox.showerror("错误", "音频播放失败")
            except Exception as e:
                messagebox.showerror("错误", f"播放音频失败: {e}")
        else:
            messagebox.showwarning("警告", "请先选择有效的音频文件")

    def browse_warning_sound(self):
        """浏览警告音文件"""
        # 获取支持的音频格式
        supported_formats = self.audio_player.get_supported_formats()
        format_filter = " ".join([f"*{fmt}" for fmt in supported_formats])

        filename = filedialog.askopenfilename(
            title="选择警告音文件",
            filetypes=[("音频文件", format_filter), ("所有文件", "*.*")]
        )
        if filename:
            self.warning_sound_file_var.set(filename)

    def test_warning_sound(self):
        """试听警告音"""
        sound_file = self.warning_sound_file_var.get()
        if sound_file and os.path.exists(sound_file):
            try:
                if self.audio_player.test_sound(sound_file):
                    messagebox.showinfo("提示", f"正在播放: {os.path.basename(sound_file)}")
                else:
                    messagebox.showerror("错误", "音频播放失败")
            except Exception as e:
                messagebox.showerror("错误", f"播放音频失败: {e}")
        else:
            messagebox.showwarning("警告", "请先选择有效的音频文件")

    def browse_end_sound_file(self):
        """浏览结束音文件"""
        # 获取支持的音频格式
        supported_formats = self.audio_player.get_supported_formats()
        format_filter = " ".join([f"*{fmt}" for fmt in supported_formats])

        filename = filedialog.askopenfilename(
            title="选择结束音文件",
            filetypes=[("音频文件", format_filter), ("所有文件", "*.*")]
        )
        if filename:
            self.end_sound_file_var.set(filename)

    def test_end_sound_file(self):
        """试听结束音"""
        sound_file = self.end_sound_file_var.get()
        if sound_file and os.path.exists(sound_file):
            try:
                if self.audio_player.test_sound(sound_file):
                    messagebox.showinfo("提示", f"正在播放: {os.path.basename(sound_file)}")
                else:
                    messagebox.showerror("错误", "音频播放失败")
            except Exception as e:
                messagebox.showerror("错误", f"播放音频失败: {e}")
        else:
            messagebox.showwarning("警告", "请先选择有效的音频文件")

    def apply_config(self):
        """应用配置"""
        try:
            # 收集所有配置
            config_updates = {
                'timer': {
                    'default_duration': self.default_duration_var.get(),
                    'duration': self.default_duration_var.get(),  # 同时更新duration
                    'mode': self.timer_mode_var.get(),
                    'auto_start': self.auto_start_var.get(),
                    'warning_time': self.warning_time_var.get()
                },
                'display': {
                    'colors': {
                        'normal': {
                            'foreground': self.normal_fg_var.get(),
                            'background': self.normal_bg_var.get()
                        },
                        'warning': {
                            'foreground': self.warning_fg_var.get(),
                            'background': self.warning_bg_var.get()
                        }
                    },
                    'transparency': self.transparency_var.get(),
                    'show_controls': self.show_controls_var.get(),
                    'show_in_fullscreen': self.show_in_fullscreen_var.get(),
                    'hide_when_no_ppt': self.hide_when_no_ppt_var.get(),
                    'hide_when_timer_off': self.hide_when_timer_off_var.get()
                },
                'audio': {
                    'enabled': self.audio_enabled_var.get(),
                    'warning_enabled': self.warning_audio_enabled_var.get(),
                    'warning_sound': self.warning_sound_file_var.get(),
                    'end_sound': self.end_sound_file_var.get()
                },
                'control': {
                    'timer_end_action': self.end_action_var.get(),
                    'auto_exit_delay': self.auto_exit_delay_var.get(),
                    'remind_break_time': self.remind_break_var.get(),
                    'auto_pause_resume': self.auto_pause_resume_var.get(),
                    'hotkeys': {
                        'pause_resume': self.pause_hotkey_var.get(),
                        'reset': self.reset_hotkey_var.get()
                    }
                },

                'appearance': {
                    'show_machine_time': self.show_machine_time_var.get(),
                    'show_hint_without_countdown': self.show_hint_without_countdown_var.get(),
                    'background_transparent': self.background_transparent_var.get()
                }
            }

            # 收集文件特定计时器（只保存手动设置的）
            file_timers = {}
            file_order = []  # 保存文件顺序
            for item in self.file_tree.get_children():
                values = self.file_tree.item(item, 'values')
                filename, duration, source = values
                file_order.append(filename)  # 记录文件顺序
                # 只保存手动设置的计时器
                if source == "手动设置":
                    file_timers[filename] = duration

            config_updates['files'] = {
                'file_specific_timers': file_timers,
                'file_order': file_order  # 保存文件顺序
            }

            # 更新配置
            if self.config_manager.update_config(config_updates):
                messagebox.showinfo("成功", "配置已保存")
            else:
                messagebox.showerror("错误", "保存配置失败")

        except Exception as e:
            messagebox.showerror("错误", f"应用配置失败: {e}")

    def apply_and_close(self):
        """应用配置并关闭窗口"""
        self.apply_config()
        self.window.destroy()

    def cancel(self):
        """取消并关闭窗口"""
        self.window.destroy()

    def export_config(self):
        """导出配置"""
        filename = filedialog.asksaveasfilename(
            title="导出配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            if self.config_manager.export_config(filename):
                messagebox.showinfo("成功", "配置导出成功")
            else:
                messagebox.showerror("错误", "配置导出失败")

    def import_config(self):
        """导入配置"""
        filename = filedialog.askopenfilename(
            title="导入配置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            if messagebox.askyesno("确认", "导入配置将覆盖当前设置，是否继续？"):
                if self.config_manager.import_config(filename):
                    messagebox.showinfo("成功", "配置导入成功")
                    self.load_current_config()  # 重新加载界面
                else:
                    messagebox.showerror("错误", "配置导入失败")

    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "重置将恢复所有设置为默认值，是否继续？"):
            if self.config_manager.reset_to_default():
                messagebox.showinfo("成功", "配置已重置为默认值")
                self.load_current_config()  # 重新加载界面
            else:
                messagebox.showerror("错误", "重置配置失败")

    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def move_file_up(self):
        """上移选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个文件")
            return

        item = selection[0]
        prev_item = self.file_tree.prev(item)
        if prev_item:
            # 获取两个项目的索引
            items = self.file_tree.get_children()
            current_index = items.index(item)
            prev_index = items.index(prev_item)

            # 交换位置
            self.file_tree.move(item, '', prev_index)
            self.save_file_timers()

    def move_file_down(self):
        """下移选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个文件")
            return

        item = selection[0]
        next_item = self.file_tree.next(item)
        if next_item:
            # 获取两个项目的索引
            items = self.file_tree.get_children()
            current_index = items.index(item)
            next_index = items.index(next_item)

            # 交换位置
            self.file_tree.move(item, '', next_index)
            self.save_file_timers()

    def on_file_click(self, event):
        """文件点击事件"""
        item = self.file_tree.identify_row(event.y)
        if item:
            self.drag_item = item
            self.drag_start_y = event.y

    def on_file_drag(self, event):
        """文件拖拽事件"""
        if self.drag_item and self.drag_start_y:
            # 计算拖拽距离
            drag_distance = abs(event.y - self.drag_start_y)
            if drag_distance > 5:  # 最小拖拽距离
                # 改变鼠标样式表示正在拖拽
                self.file_tree.config(cursor="hand2")

    def on_file_drop(self, event):
        """文件放下事件"""
        if self.drag_item:
            target_item = self.file_tree.identify_row(event.y)
            if target_item and target_item != self.drag_item:
                # 获取所有项目
                items = list(self.file_tree.get_children())
                drag_index = items.index(self.drag_item)
                target_index = items.index(target_item)

                # 移动项目
                if drag_index < target_index:
                    self.file_tree.move(self.drag_item, '', target_index)
                else:
                    self.file_tree.move(self.drag_item, '', target_index)

                # 保存更改
                self.save_file_timers()

            # 重置拖拽状态
            self.drag_item = None
            self.drag_start_y = None
            self.file_tree.config(cursor="")

    def save_file_timers(self):
        """保存文件计时器和顺序"""
        try:
            # 收集文件特定计时器（只保存手动设置的）
            file_timers = {}
            file_order = []  # 保存文件顺序
            for item in self.file_tree.get_children():
                values = self.file_tree.item(item, 'values')
                filename, duration, source = values
                file_order.append(filename)  # 记录文件顺序
                # 只保存手动设置的计时器
                if source == "手动设置":
                    file_timers[filename] = duration

            config_updates = {
                'files': {
                    'file_specific_timers': file_timers,
                    'file_order': file_order  # 保存文件顺序
                }
            }

            # 更新配置
            self.config_manager.update_config(config_updates)
            print(f" 文件顺序已保存: {file_order}")
        except Exception as e:
            print(f" 保存文件顺序失败: {e}")


class FileTimerDialog:
    """文件计时器对话框"""

    def __init__(self, parent, title, filename="", duration="00:08:00"):
        """
        初始化对话框

        Args:
            parent: 父窗口
            title: 对话框标题
            filename: 初始文件名
            duration: 初始时长
        """
        self.result = None

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 创建界面
        self.create_widgets(filename, duration)

        # 居中显示
        self.center_dialog()

        # 等待对话框关闭
        self.dialog.wait_window()

    def create_widgets(self, filename, duration):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 文件选择
        ttk.Label(main_frame, text="文件:").grid(row=0, column=0, sticky=tk.W, pady=5)

        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=0, column=1, sticky=tk.EW, pady=5)
        main_frame.grid_columnconfigure(1, weight=1)

        self.filename_var = tk.StringVar(value=filename)
        ttk.Entry(file_frame, textvariable=self.filename_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 时长设置
        ttk.Label(main_frame, text="时长:").grid(row=1, column=0, sticky=tk.W, pady=5)

        duration_frame = ttk.Frame(main_frame)
        duration_frame.grid(row=1, column=1, sticky=tk.W, pady=5)

        self.duration_var = tk.StringVar(value=duration)
        ttk.Entry(duration_frame, textvariable=self.duration_var, width=10).pack(side=tk.LEFT)
        ttk.Label(duration_frame, text="(格式: HH:MM:SS)").pack(side=tk.LEFT, padx=(5, 0))

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.RIGHT)

    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择PPT/PDF文件",
            filetypes=[
                ("演示文稿", "*.ppt *.pptx *.pdf"),
                ("PowerPoint文件", "*.ppt *.pptx"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.filename_var.set(filename)

    def ok_clicked(self):
        """确定按钮点击"""
        filename = self.filename_var.get().strip()
        duration = self.duration_var.get().strip()

        if not filename:
            messagebox.showwarning("警告", "请选择文件")
            return

        if not duration:
            messagebox.showwarning("警告", "请输入时长")
            return

        # 验证时长格式
        if not self.validate_duration(duration):
            messagebox.showwarning("警告", "时长格式不正确，请使用 HH:MM:SS 格式")
            return

        self.result = (filename, duration)
        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()

    def validate_duration(self, duration):
        """验证时长格式"""
        try:
            parts = duration.split(':')
            if len(parts) != 3:
                return False

            hours, minutes, seconds = map(int, parts)
            return 0 <= hours <= 23 and 0 <= minutes <= 59 and 0 <= seconds <= 59
        except ValueError:
            return False

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")


def main():
    """测试函数"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 创建设置窗口
    settings = SettingsWindow()

    root.mainloop()


if __name__ == "__main__":
    main()
