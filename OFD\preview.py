import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QFileDialog, QMessageBox, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QImage

try:
    from easyofd import OFD
    OFD_AVAILABLE = True
except ImportError:
    OFD_AVAILABLE = False

class OFDPreviewWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OFD文件预览")
        self.setGeometry(100, 100, 800, 600)
        
        self.ofd_document = None
        self.current_page = 0
        self.file_path = ""
        
        self.init_ui()
        
    def init_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建工具栏
        toolbar_layout = QHBoxLayout()
        
        # 打开文件按钮
        self.open_btn = QPushButton("打开OFD文件")
        self.open_btn.clicked.connect(self.open_file)
        toolbar_layout.addWidget(self.open_btn)
        
        # 页面导航按钮
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        self.prev_btn.setEnabled(False)
        toolbar_layout.addWidget(self.prev_btn)
        
        self.page_label = QLabel("页面: 0/0")
        toolbar_layout.addWidget(self.page_label)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        self.next_btn.setEnabled(False)
        toolbar_layout.addWidget(self.next_btn)
        
        # 添加弹簧
        toolbar_layout.addStretch()
        
        main_layout.addLayout(toolbar_layout)
        
        # 创建预览区域
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumSize(400, 500)
        self.preview_label.setStyleSheet("background-color: white; border: 1px solid gray;")
        main_layout.addWidget(self.preview_label)
        
        # 文件信息区域
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        main_layout.addWidget(self.info_text)
        
    def open_file(self):
        if not OFD_AVAILABLE:
            QMessageBox.critical(self, "错误", "缺少必要的库，请安装 easyofd")
            return
            
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择OFD文件", "", "OFD文件 (*.ofd)")
        
        if file_path:
            self.open_file_with_path(file_path)
    
    def open_file_with_path(self, file_path):
        """通过路径直接打开文件"""
        if not OFD_AVAILABLE:
            QMessageBox.critical(self, "错误", "缺少必要的库，请安装 easyofd")
            return
            
        try:
            with open(file_path, "rb") as f:
                ofd_data = f.read()
                self.ofd_document = OFD()
                self.ofd_document.read(ofd_data)
                self.file_path = file_path
            
            # 重置页面
            self.current_page = 0
            self.update_page_info()
            self.show_page()
            self.extract_info()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件:\n{str(e)}")
    
    def prev_page(self):
        if self.ofd_document and self.current_page > 0:
            self.current_page -= 1
            self.update_page_info()
            self.show_page()
    
    def next_page(self):
        if self.ofd_document and self.current_page < self.get_page_count() - 1:
            self.current_page += 1
            self.update_page_info()
            self.show_page()
    
    def get_page_count(self):
        if self.ofd_document:
            try:
                # 获取页面数量
                return len(self.ofd_document.pages)
            except:
                return 1
        return 0
    
    def update_page_info(self):
        page_count = self.get_page_count()
        self.page_label.setText(f"页面: {self.current_page + 1}/{page_count}")
        
        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_page > 0)
        self.next_btn.setEnabled(self.current_page < page_count - 1)
    
    def show_page(self):
        if not self.ofd_document:
            return
            
        try:
            # 获取当前页面的图像
            page_image = self.ofd_document.to_img(page=self.current_page)[0]  # 返回列表，取第一张图
            
            # 转换为QImage
            height, width, channel = page_image.shape
            bytes_per_line = 3 * width
            q_image = QImage(page_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            
            # 缩放以适应显示区域
            pixmap = QPixmap.fromImage(q_image)
            scaled_pixmap = pixmap.scaled(
                self.preview_label.size(), 
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            
            self.preview_label.setPixmap(scaled_pixmap)
        except Exception as e:
            self.preview_label.setText(f"无法显示页面:\n{str(e)}")
    
    def extract_info(self):
        if not self.ofd_document:
            return
            
        try:
            info_text = f"文件路径: {self.file_path}\n"
            
            # 这里可以添加更多文件信息提取逻辑
            info_text += f"- 总页数: {self.get_page_count()}\n"
            info_text += "- 文件格式: OFD\n"
            
            self.info_text.setText(info_text)
        except Exception as e:
            self.info_text.setText(f"无法提取文件信息: {str(e)}")

def main():
    app = QApplication(sys.argv)
    window = OFDPreviewWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()