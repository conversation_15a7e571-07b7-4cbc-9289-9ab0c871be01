#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时器管理模块
使用threading.Timer实现定时任务管理
支持多线程、动态调整时间间隔和任务的启动停止控制
"""

import threading
import time
import logging
from typing import Callable, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, Future

# 配置日志
logger = logging.getLogger(__name__)


class TimerManager:
    """定时器管理类 - 支持多线程和高级功能"""
    
    def __init__(self, callback_func: Callable, initial_interval: float = 5.0):
        """
        初始化定时器管理器
        
        Args:
            callback_func: 定时执行的回调函数
            initial_interval: 初始时间间隔（秒）
        """
        self.callback_func = callback_func
        self.interval = initial_interval
        self.is_running = False
        self.is_paused = False
        
        # 线程相关
        self.timer = None
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="TimerManager")
        self._lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'start_time': None,
            'last_execution_time': None,
            'average_execution_time': 0.0
        }
        self._stats_lock = threading.Lock()
        
        # 执行历史（保留最近10次）
        self.execution_history = []
        self._history_lock = threading.Lock()
        
        logger.info(f"TimerManager 初始化完成，初始间隔: {initial_interval}秒")
    
    def start_timer(self, interval: Optional[float] = None) -> bool:
        """
        启动定时器
        
        Args:
            interval: 时间间隔（秒），如果为None则使用当前间隔
            
        Returns:
            bool: 启动是否成功
        """
        with self._lock:
            if self.is_running:
                logger.warning("定时器已在运行")
                return False
            
            if interval is not None:
                self.interval = max(0.1, interval)  # 最小间隔0.1秒
            
            self.is_running = True
            self.is_paused = False
            
            # 记录启动时间
            with self._stats_lock:
                self.stats['start_time'] = time.time()
            
            # 启动第一次执行
            self._schedule_next()
            
            logger.info(f"定时器已启动，间隔: {self.interval}秒")
            return True
    
    def stop_timer(self) -> bool:
        """
        停止定时器
        
        Returns:
            bool: 停止是否成功
        """
        with self._lock:
            if not self.is_running:
                logger.warning("定时器未在运行")
                return False
            
            self.is_running = False
            self.is_paused = False
            
            # 取消当前定时器
            if self.timer:
                self.timer.cancel()
                self.timer = None
            
            logger.info("定时器已停止")
            return True    
    def pause_timer(self) -> bool:
        """
        暂停定时器
        
        Returns:
            bool: 暂停是否成功
        """
        with self._lock:
            if not self.is_running:
                logger.warning("定时器未在运行，无法暂停")
                return False
            
            if self.is_paused:
                logger.warning("定时器已暂停")
                return False
            
            self.is_paused = True
            
            # 取消当前定时器
            if self.timer:
                self.timer.cancel()
                self.timer = None
            
            logger.info("定时器已暂停")
            return True
    
    def resume_timer(self) -> bool:
        """
        恢复定时器
        
        Returns:
            bool: 恢复是否成功
        """
        with self._lock:
            if not self.is_running:
                logger.warning("定时器未在运行，无法恢复")
                return False
            
            if not self.is_paused:
                logger.warning("定时器未暂停")
                return False
            
            self.is_paused = False
            
            # 重新调度执行
            self._schedule_next()
            
            logger.info("定时器已恢复")
            return True
    
    def set_interval(self, interval: float, restart: bool = False) -> bool:
        """
        设置时间间隔
        
        Args:
            interval: 新的时间间隔（秒）
            restart: 是否立即重启定时器
            
        Returns:
            bool: 设置是否成功
        """
        if interval <= 0:
            logger.error(f"无效的时间间隔: {interval}")
            return False
        
        old_interval = self.interval
        self.interval = max(0.1, interval)  # 最小间隔0.1秒
        
        logger.info(f"时间间隔已更新: {old_interval}秒 -> {self.interval}秒")
        
        # 如果定时器正在运行且需要重启
        if restart and self.is_running and not self.is_paused:
            with self._lock:
                # 取消当前定时器
                if self.timer:
                    self.timer.cancel()
                
                # 重新调度
                self._schedule_next()
                logger.info("定时器已重启以应用新间隔")
        
        return True    
    def _schedule_next(self):
        """调度下一次执行（内部方法）"""
        if self.is_running and not self.is_paused:
            self.timer = threading.Timer(self.interval, self._execute)
            self.timer.daemon = True  # 守护线程，确保程序能正常退出
            self.timer.start()
    
    def _execute(self):
        """执行回调函数（内部方法）"""
        execution_start = time.time()
        
        try:
            # 异步执行回调函数
            future = self.executor.submit(self._safe_callback_execution)
            
            # 等待执行完成（最多等待间隔时间的一半）
            timeout = min(self.interval / 2, 30)  # 最多等待30秒
            result = future.result(timeout=timeout)
            
            # 记录成功执行
            with self._stats_lock:
                self.stats['successful_executions'] += 1
                self.stats['last_execution_time'] = time.time()
            
            execution_time = time.time() - execution_start
            self._record_execution(True, execution_time, None)
            
        except Exception as e:
            logger.error(f"定时任务执行失败: {e}")
            
            # 记录失败执行
            with self._stats_lock:
                self.stats['failed_executions'] += 1
            
            execution_time = time.time() - execution_start
            self._record_execution(False, execution_time, str(e))
        
        finally:
            # 更新总执行次数和平均执行时间
            with self._stats_lock:
                self.stats['total_executions'] += 1
                if self.stats['total_executions'] > 0:
                    total_time = time.time() - (self.stats['start_time'] or time.time())
                    self.stats['average_execution_time'] = total_time / self.stats['total_executions']
            
            # 调度下一次执行
            self._schedule_next()
    
    def _safe_callback_execution(self) -> bool:
        """
        安全执行回调函数（内部方法）
        
        Returns:
            bool: 执行是否成功
        """
        try:
            self.callback_func()
            return True
        except Exception as e:
            logger.error(f"回调函数执行出错: {e}")
            raise
    
    def _record_execution(self, success: bool, execution_time: float, error_msg: Optional[str]):
        """
        记录执行历史（内部方法）
        
        Args:
            success: 是否成功
            execution_time: 执行时间
            error_msg: 错误信息
        """
        with self._history_lock:
            record = {
                'timestamp': time.time(),
                'success': success,
                'execution_time': execution_time,
                'error_msg': error_msg
            }
            
            self.execution_history.append(record)
            
            # 保留最近10次记录
            if len(self.execution_history) > 10:
                self.execution_history.pop(0)    
    def get_status(self) -> Dict[str, Any]:
        """
        获取定时器状态
        
        Returns:
            Dict[str, Any]: 状态信息字典
        """
        with self._lock:
            return {
                'is_running': self.is_running,
                'is_paused': self.is_paused,
                'interval': self.interval,
                'next_execution_in': self._get_next_execution_time()
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self._stats_lock:
            stats = self.stats.copy()
            
            # 计算运行时间
            if stats['start_time']:
                stats['running_time'] = time.time() - stats['start_time']
            else:
                stats['running_time'] = 0
            
            # 计算成功率
            if stats['total_executions'] > 0:
                stats['success_rate'] = stats['successful_executions'] / stats['total_executions']
            else:
                stats['success_rate'] = 0
            
            return stats
    
    def get_execution_history(self) -> list:
        """
        获取执行历史
        
        Returns:
            list: 执行历史列表
        """
        with self._history_lock:
            return self.execution_history.copy()
    
    def _get_next_execution_time(self) -> Optional[float]:
        """
        获取下次执行的剩余时间（内部方法）
        
        Returns:
            Optional[float]: 剩余时间（秒），如果未运行则返回None
        """
        if not self.is_running or self.is_paused or not self.timer:
            return None
        
        # 这是一个近似值，因为Timer对象没有直接的方法获取剩余时间
        return self.interval
    
    def reset_stats(self):
        """重置统计信息"""
        with self._stats_lock:
            self.stats = {
                'total_executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'start_time': time.time() if self.is_running else None,
                'last_execution_time': None,
                'average_execution_time': 0.0
            }
        
        with self._history_lock:
            self.execution_history.clear()
        
        logger.info("统计信息已重置")    
    def execute_once(self, async_execution: bool = False) -> bool:
        """
        立即执行一次回调函数
        
        Args:
            async_execution: 是否异步执行
            
        Returns:
            bool: 执行是否成功
        """
        if async_execution:
            # 异步执行
            future = self.executor.submit(self._safe_callback_execution)
            return True  # 立即返回，实际结果通过Future获取
        else:
            # 同步执行
            try:
                return self._safe_callback_execution()
            except Exception as e:
                logger.error(f"立即执行失败: {e}")
                return False
    
    def execute_multiple(self, count: int, interval: float = 1.0) -> Future:
        """
        执行多次回调函数
        
        Args:
            count: 执行次数
            interval: 执行间隔（秒）
            
        Returns:
            Future: 异步操作的Future对象
        """
        def batch_execution():
            results = []
            for i in range(count):
                try:
                    result = self._safe_callback_execution()
                    results.append(result)
                    logger.info(f"批量执行 {i+1}/{count} 完成")
                    
                    if i < count - 1:  # 最后一次不需要等待
                        time.sleep(interval)
                        
                except Exception as e:
                    logger.error(f"批量执行 {i+1}/{count} 失败: {e}")
                    results.append(False)
            
            success_count = sum(1 for r in results if r)
            logger.info(f"批量执行完成: {success_count}/{count} 成功")
            return success_count
        
        return self.executor.submit(batch_execution)
    
    def shutdown(self):
        """关闭定时器管理器"""
        logger.info("正在关闭TimerManager...")
        
        # 停止定时器
        self.stop_timer()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("TimerManager已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except:
            pass  # 忽略析构时的异常


# 定时器管理器工厂函数
def create_timer_manager(callback_func: Callable, interval: float = 5.0) -> TimerManager:
    """
    创建定时器管理器实例
    
    Args:
        callback_func: 回调函数
        interval: 时间间隔
        
    Returns:
        TimerManager: 定时器管理器实例
    """
    return TimerManager(callback_func, interval)


# 全局定时器管理器实例
_timer_manager = None


def get_timer_manager(callback_func: Optional[Callable] = None, 
                     interval: float = 5.0) -> Optional[TimerManager]:
    """
    获取全局定时器管理器实例（单例模式）
    
    Args:
        callback_func: 回调函数（首次调用时必需）
        interval: 时间间隔
        
    Returns:
        Optional[TimerManager]: 定时器管理器实例
    """
    global _timer_manager
    
    if _timer_manager is None:
        if callback_func is None:
            logger.error("首次获取定时器管理器时必须提供回调函数")
            return None
        _timer_manager = TimerManager(callback_func, interval)
    
    return _timer_manager