#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 最终版打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")

def build_final():
    """构建最终版ToolClock exe"""
    print("\\n构建最终版ToolClock exe...")
    
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name', 'ToolClock_Final',
            
            # 添加数据文件
            '--add-data', 'gui' + os.pathsep + 'gui',
            '--add-data', 'core' + os.pathsep + 'core',
            '--add-data', 'assets' + os.pathsep + 'assets',
            '--add-data', 'window_detector_fixed.py' + os.pathsep + '.',
            
            # 核心隐藏导入
            '--hidden-import', 'threading',
            '--hidden-import', '_thread',
            '--hidden-import', 'queue',
            '--hidden-import', 'time',
            '--hidden-import', 'datetime',
            
            # Windows API
            '--hidden-import', 'win32gui',
            '--hidden-import', 'win32api',
            '--hidden-import', 'win32con',
            '--hidden-import', 'win32process',
            '--hidden-import', 'pywintypes',
            '--hidden-import', 'pythoncom',
            
            # GUI模块
            '--hidden-import', 'tkinter',
            '--hidden-import', 'tkinter.ttk',
            '--hidden-import', 'tkinter.messagebox',
            '--hidden-import', 'tkinter.filedialog',
            '--hidden-import', 'tkinter.simpledialog',
            
            # 音频模块
            '--hidden-import', 'pygame',
            '--hidden-import', 'pygame.mixer',
            '--hidden-import', 'winsound',
            
            # 其他必要模块
            '--hidden-import', 'logging',
            '--hidden-import', 'json',
            '--hidden-import', 'pathlib',
            '--hidden-import', 'subprocess',
            '--hidden-import', 're',
            
            'run_with_log.py'
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("ToolClock最终版exe构建成功")
        
        # 复制到当前目录
        if Path('dist/ToolClock_Final.exe').exists():
            shutil.copy2('dist/ToolClock_Final.exe', 'ToolClock_Final.exe')
            print("已复制到当前目录: ToolClock_Final.exe")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("ToolClock 最终版打包工具")
    print("=" * 60)
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建最终版exe
    success = build_final()
    
    # 清理临时文件（保留dist目录）
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
    
    # 总结
    print("\\n" + "=" * 60)
    print("构建总结")
    print("=" * 60)
    print(f"ToolClock最终版: {'成功' if success else '失败'}")
    
    if success:
        print("\\n✅ ToolClock最终版exe构建成功！")
        print("📁 输出文件: ToolClock_Final.exe")
        print("\\n🔧 修复内容:")
        print("  - ❌ 移除智能分析功能")
        print("  - 🎨 修复颜色切换逻辑")
        print("  - 🔴 修复负数倒计时保持")
        print("  - 🧵 强化线程模块打包")
        print("  - 🪟 完整win32api支持")
        print("  - ⚙️ 运行时模块初始化")
    else:
        print("\\n❌ 构建失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
