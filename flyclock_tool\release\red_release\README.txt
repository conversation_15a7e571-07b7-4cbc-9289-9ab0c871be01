# ToolClock V1.1.0 红色背景持续蜂鸣版本

## 📦 文件信息

**文件名**：ToolClock_V1.1.0_RedBeep.exe  
**文件大小**：10.15 MB  
**版本**：V1.1.0 红色背景持续蜂鸣版本  
**发布日期**：2025-07-31

## 🔴 版本特性

### 🎯 红色版本专有功能
- **持续红色背景**：一旦激活红色模式，始终显示红色背景
- **持续蜂鸣音**：每2秒播放一次蜂鸣音，强烈提醒
- **手动激活**：可通过右键菜单手动激活/停止红色模式
- **强烈提醒**：适用于嘈杂环境或需要强烈提醒的场合

### ✅ 标准功能（继承）
- **自动全屏检测**：支持PPT、PDF等演示文件的全屏检测
- **智能计时控制**：自动开始/停止计时，精确到秒
- **文件名清理**：自动移除"润文档"、"WPS Office"等后缀
- **悬浮窗口**：始终置顶显示，可拖拽移动位置
- **负数时间显示**：时间结束后显示负数时间递增

## 🚀 使用方法

### 方法一：自动激活（推荐）
1. **启动程序**：双击ToolClock_V1.1.0_RedBeep.exe
2. **设置时间**：右键计时器窗口 → 选择"设置时间"
3. **开始演示**：打开PPT/PDF → 按F5进入全屏放映
4. **自动计时**：程序检测到全屏后自动开始倒计时
5. **时间结束**：
   - 自动激活红色模式
   - 显示红色背景
   - 开始持续蜂鸣音（每2秒一次）
6. **停止蜂鸣**：右键菜单 → 选择"停止蜂鸣"

### 方法二：手动激活
1. **启动程序**：双击ToolClock_V1.1.0_RedBeep.exe
2. **手动激活**：右键计时器窗口 → 选择"激活红色版本"
3. **立即生效**：
   - 立即显示红色背景
   - 开始持续蜂鸣音
   - 显示负数时间
4. **停止功能**：右键菜单 → 选择"停止蜂鸣"

## 📝 右键菜单功能

### 🔴 红色版本专有菜单
- **激活红色版本**：手动激活红色背景和持续蜂鸣
- **停止蜂鸣**：停止红色模式和蜂鸣音

### ✅ 标准功能菜单
- **设置时间**：设置倒计时时间（支持MM:SS或HH:MM:SS格式）
- **调整文件时间**：为特定文件设置专用时间
- **退出**：关闭程序

## 🎯 适用场景

### 🔴 红色版本特别适用于：
- **嘈杂环境**：会场、展厅等噪音较大的环境
- **重要提醒**：关键时间节点、重要演示场合
- **紧急情况**：需要立即引起注意的紧急时间控制
- **多人协作**：需要提醒多人注意时间的场合
- **远距离提醒**：演讲者距离计时器较远的情况

### ✅ 通用场景：
- **商务演示**：重要的商务汇报和演示
- **学术报告**：会议发言、论文答辩等
- **教学培训**：需要严格时间控制的教学场景
- **竞赛活动**：演讲比赛、答辩比赛等

## 🔊 蜂鸣音特性

- **频率**：每2秒播放一次
- **音调**：800Hz，持续500毫秒
- **音量**：系统蜂鸣音，音量适中
- **持续性**：红色模式激活期间持续播放
- **停止方式**：右键菜单选择"停止蜂鸣"

## 🔧 技术特点

- **增强提醒**：视觉（红色背景）+ 听觉（蜂鸣音）双重提醒
- **单文件运行**：无需安装，双击即用
- **无依赖要求**：所有依赖已打包，无需额外安装
- **系统兼容**：支持Windows 10/11系统
- **资源占用**：轻量级设计，蜂鸣音不占用额外资源
- **稳定可靠**：经过充分测试，运行稳定

## 💡 使用提示

1. **音量调节**：蜂鸣音使用系统音量，可通过系统音量控制
2. **环境考虑**：在安静环境中使用时注意音量影响
3. **快速停止**：可随时通过右键菜单停止蜂鸣音
4. **手动控制**：可在任何时候手动激活红色模式
5. **视觉提醒**：即使关闭蜂鸣音，红色背景仍会保持

## 🔄 红色模式控制

### 激活条件：
- 计时结束时自动激活
- 手动通过右键菜单激活
- 时间到达0秒时自动激活

### 停止方式：
- 右键菜单选择"停止蜂鸣"
- 重置计时器
- 退出程序

### 状态指示：
- **红色背景**：红色模式已激活
- **蜂鸣音**：每2秒一次表示正在提醒
- **负数时间**：显示超时时间

## 🆕 V1.1.0 红色版本新增功能

- 🔴 新增持续红色背景显示功能
- 🔊 新增持续蜂鸣音提醒功能（每2秒一次）
- ⚙️ 新增手动激活/停止红色模式功能
- 🎯 新增强烈视觉和听觉双重提醒
- 📝 新增红色版本专用右键菜单选项
- 🔧 优化红色模式的状态管理和控制逻辑

## 📞 常见问题

**Q: 蜂鸣音太吵怎么办？**
A: 可以通过右键菜单选择"停止蜂鸣"，红色背景会保持但蜂鸣音会停止。

**Q: 如何手动激活红色模式？**
A: 右键计时器窗口，选择"激活红色版本"即可立即激活。

**Q: 红色模式会一直持续吗？**
A: 是的，一旦激活会持续到手动停止或程序退出。

**Q: 可以只要红色背景不要蜂鸣音吗？**
A: 可以，激活红色模式后，再选择"停止蜂鸣"即可。

**Q: 蜂鸣音频率可以调整吗？**
A: 当前版本固定为每2秒一次，后续版本可能会增加自定义选项。

---

**开发者**：AI Assistant + MCP工具集成  
**技术支持**：基于Claude Sonnet 4 + 多种MCP工具协作开发

⚠️ **注意**：此版本包含持续蜂鸣音功能，请在合适的环境中使用，避免影响他人。
