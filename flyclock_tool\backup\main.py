#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlyClock Tool - PPT/PDF全屏放映计时辅助工具
主程序入口文件

功能：
- 自动检测PPT/PDF全屏状态并开始计时
- 悬浮计时器窗口显示
- 支持正计时和倒计时模式
- 音频提示功能

作者：AI Assistant
版本：1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 强制清理相关模块缓存，确保使用修复后的代码
modules_to_remove = []
for module_name in list(sys.modules.keys()):
    if any(module_name.startswith(prefix) for prefix in ['core.window_detector', 'core.file_analyzer', 'gui.timer_window']):
        modules_to_remove.append(module_name)

for module_name in modules_to_remove:
    if module_name in sys.modules:
        del sys.modules[module_name]
        print(f"✓ 清理模块缓存: {module_name}")

# 智能导入：支持开发环境和exe环境
def smart_import(module_path):
    """智能导入模块，支持开发环境和打包环境"""
    try:
        # 首先尝试相对导入（开发环境）
        return __import__(module_path, fromlist=[''])
    except ImportError:
        try:
            # 然后尝试从flyclock_tool包导入（exe环境）
            full_path = f"flyclock_tool.{module_path}"
            return __import__(full_path, fromlist=[''])
        except ImportError:
            # 最后尝试直接导入
            return __import__(module_path.replace('.', '_'), fromlist=[''])

# 导入配置管理器
try:
    from core.config_manager import config_manager
except ImportError:
    try:
        from flyclock_tool.core.config_manager import config_manager
    except ImportError:
        # 如果都失败了，创建一个简单的配置管理器
        import json
        import os

        class SimpleConfigManager:
            def __init__(self):
                self.config_file = "flyclock_config.json"
                self.config = self.load_config()

            def load_config(self):
                if os.path.exists(self.config_file):
                    try:
                        with open(self.config_file, 'r', encoding='utf-8') as f:
                            return json.load(f)
                    except:
                        pass
                return {}

            def get_setting(self, key, default=None):
                keys = key.split('.')
                value = self.config
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return default
                return value

            def set_setting(self, key, value):
                keys = key.split('.')
                config = self.config
                for k in keys[:-1]:
                    if k not in config:
                        config[k] = {}
                    config = config[k]
                config[keys[-1]] = value
                self.save_config()

            def save_config(self):
                try:
                    with open(self.config_file, 'w', encoding='utf-8') as f:
                        json.dump(self.config, f, indent=2, ensure_ascii=False)
                except Exception as e:
                    print(f"保存配置失败: {e}")

        config_manager = SimpleConfigManager()

class FlyClockApp:
    """FlyClock 应用程序主类"""

    def __init__(self):
        """初始化应用程序"""
        self.config_manager = config_manager
        self.timer_window = None

        # 检查配置
        self.check_config()

    def check_config(self):
        """检查和初始化配置"""
        try:
            # 确保配置目录存在
            config_dir = Path.home() / ".flyclock"
            config_dir.mkdir(exist_ok=True)

            # 加载配置
            self.config_manager.load_config()
            print("✓ 配置加载成功")

        except Exception as e:
            print(f"✗ 配置初始化失败: {e}")
            messagebox.showerror("错误", f"配置初始化失败: {e}")

    def check_dependencies(self):
        """检查依赖包是否安装"""
        missing_deps = []

        try:
            import win32gui
            import win32process
            import win32api
        except ImportError:
            missing_deps.append("pywin32")

        try:
            import pygame
        except ImportError:
            missing_deps.append("pygame")

        if missing_deps:
            error_msg = f"缺少必要的依赖包：{', '.join(missing_deps)}\n\n"
            error_msg += "请运行以下命令安装：\n"
            error_msg += f"pip install {' '.join(missing_deps)}"

            # 如果tkinter可用，显示图形化错误信息
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                messagebox.showerror("依赖包缺失", error_msg)
                root.destroy()
            except:
                print(error_msg)

            return False

        return True

    def start_timer_window(self):
        """启动计时器窗口"""
        try:
            # 再次强制清理缓存
            self._force_reload_modules()

            try:
                from gui.timer_window import TimerWindow
            except ImportError:
                from flyclock_tool.gui.timer_window import TimerWindow
            self.timer_window = TimerWindow()
            print("✓ 计时器窗口启动成功")
            return self.timer_window
        except Exception as e:
            print(f"✗ 计时器窗口启动失败: {e}")
            messagebox.showerror("错误", f"计时器窗口启动失败: {e}")
            return None

    def _force_reload_modules(self):
        """强制重新加载相关模块"""
        try:
            import importlib

            # 需要重新加载的模块列表
            modules_to_reload = [
                'core.window_detector',
                'core.file_analyzer',
                'gui.timer_window'
            ]

            for module_name in modules_to_reload:
                if module_name in sys.modules:
                    print(f"🔄 重新加载模块: {module_name}")
                    importlib.reload(sys.modules[module_name])

            print("✓ 模块重新加载完成")

        except Exception as e:
            print(f"⚠ 模块重新加载失败: {e}")

    def start_settings_window(self):
        """启动设置窗口"""
        try:
            try:
                from gui.settings_window import SettingsWindow
            except ImportError:
                from flyclock_tool.gui.settings_window import SettingsWindow
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            settings = SettingsWindow()
            root.mainloop()
            print("✓ 设置窗口启动成功")
        except Exception as e:
            print(f"✗ 设置窗口启动失败: {e}")
            messagebox.showerror("错误", f"设置窗口启动失败: {e}")



    def run(self, mode="timer"):
        """运行应用程序"""
        print("=" * 50)
        print("FlyClock - PPT/PDF 全屏放映计时辅助工具")
        print("版本: V1.0.0")
        print("=" * 50)

        # 检查依赖包
        if not self.check_dependencies():
            print("程序启动失败：缺少必要的依赖包")
            input("按回车键退出...")
            sys.exit(1)

        if mode == "timer":
            # 启动计时器模式
            timer_window = self.start_timer_window()
            if timer_window:
                print("✓ 计时器窗口已启动，开始运行...")
                timer_window.run()
            else:
                print("✗ 计时器窗口启动失败")
                sys.exit(1)
        elif mode == "settings":
            # 启动设置模式
            self.start_settings_window()
        else:
            print(f"✗ 未知模式: {mode}")
            sys.exit(1)

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="FlyClock - PPT/PDF 全屏放映计时辅助工具")
    parser.add_argument("--mode", choices=["timer", "settings"], default="timer",
                       help="启动模式: timer(计时器) 或 settings(设置)")
    parser.add_argument("--config", help="指定配置文件路径")
    parser.add_argument("--duration", help="指定计时时长 (格式: HH:MM:SS)")
    parser.add_argument("--version", action="version", version="FlyClock V1.0.0")

    args = parser.parse_args()

    try:
        # 创建应用程序实例
        app = FlyClockApp()

        # 如果指定了时长，更新配置
        if args.duration:
            try:
                # 验证时长格式
                parts = args.duration.split(':')
                if len(parts) == 3:
                    hours, minutes, seconds = map(int, parts)
                    if 0 <= hours <= 23 and 0 <= minutes <= 59 and 0 <= seconds <= 59:
                        config_manager.set_setting('timer.default_duration', args.duration)
                        print(f"✓ 设置计时时长: {args.duration}")
                    else:
                        print("✗ 时长数值超出范围")
                else:
                    print("✗ 时长格式错误，请使用 HH:MM:SS 格式")
            except ValueError:
                print("✗ 时长格式错误，请使用 HH:MM:SS 格式")

        # 运行应用程序
        app.run(mode=args.mode)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"✗ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()