# Static Web Assets

This directory contains the static assets for the MCP Memory Service dashboard:

- `index.html` - Main dashboard interface
- `app.js` - Frontend JavaScript (vanilla JS, no build process)
- `style.css` - Dashboard styling

The dashboard will be a lightweight, progressive web app that works offline and provides:
- Real-time memory visualization via SSE
- Search interface with filters
- Memory management (CRUD operations)
- System health monitoring