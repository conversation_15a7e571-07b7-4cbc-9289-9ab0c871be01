#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 文件特定计时功能测试脚本
测试优化后的文件特定计时功能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def test_file_specific_timing():
    """测试文件特定计时功能"""
    print("=" * 60)
    print("ToolClock 文件特定计时功能测试")
    print("=" * 60)
    
    try:
        # 导入配置管理器
        from flyclock_tool.core.config_manager import config_manager
        print("✓ 配置管理器导入成功")
        
        # 测试文件特定计时器配置
        test_files = {
            'presentation1.pptx': '00:10:00',
            'document.pdf': '00:15:00',
            'slides.ppt': '00:08:00'
        }
        
        print("\n1. 测试文件特定时间设置...")
        config_manager.set_setting('files.file_specific_timers', test_files)
        saved_files = config_manager.get_setting('files.file_specific_timers', {})
        
        if saved_files == test_files:
            print("✓ 文件特定时间设置成功")
            for filename, duration in saved_files.items():
                print(f"  - {filename}: {duration}")
        else:
            print("✗ 文件特定时间设置失败")
            return False
        
        # 测试修复版窗口检测器
        print("\n2. 测试修复版窗口检测器...")
        from flyclock_tool.window_detector_fixed import WindowDetectorFixed
        detector = WindowDetectorFixed()
        print("✓ 修复版窗口检测器创建成功")
        
        # 测试文件特定时间获取
        print("\n3. 测试文件特定时间获取...")
        test_cases = [
            ("PowerPoint - presentation1.pptx", "00:10:00"),
            ("Adobe Acrobat - document.pdf", "00:15:00"),
            ("Microsoft PowerPoint - slides.ppt", "00:08:00"),
            ("unknown_file.pptx", None)
        ]
        
        for window_title, expected_duration in test_cases:
            result = detector._get_file_specific_duration(window_title)
            if result == expected_duration:
                print(f"✓ {window_title} -> {result}")
            else:
                print(f"✗ {window_title} -> 期望: {expected_duration}, 实际: {result}")
        
        # 测试时间格式化
        print("\n4. 测试时间格式化...")
        test_durations = [30, 90, 300, 3600, 3900]
        for seconds in test_durations:
            formatted = detector._format_duration(seconds)
            print(f"✓ {seconds}秒 -> {formatted}")
        
        # 测试删除功能的配置同步
        print("\n5. 测试删除功能...")
        # 模拟删除一个文件
        updated_files = saved_files.copy()
        if 'presentation1.pptx' in updated_files:
            del updated_files['presentation1.pptx']
            config_manager.set_setting('files.file_specific_timers', updated_files)
            
            # 验证删除结果
            final_files = config_manager.get_setting('files.file_specific_timers', {})
            if 'presentation1.pptx' not in final_files:
                print("✓ 文件删除功能正常")
            else:
                print("✗ 文件删除功能异常")
        
        print("\n6. 测试优先级逻辑...")
        # 模拟全屏检测场景
        class MockTimerWindow:
            def __init__(self):
                self.duration = None

            def set_duration(self, duration):
                self.duration = duration
                print(f"✓ 计时器时长已设置为: {duration}")

            def start_timer(self):
                print(f"✓ 模拟启动计时器")
        
        mock_timer = MockTimerWindow()
        detector.timer_window = mock_timer
        
        # 测试有文件特定时间的情况
        print("  测试场景1: 有文件特定时间")
        detector._on_fullscreen_enter(12345, "PowerPoint - document.pdf", "POWERPNT.EXE")
        
        print("\n✓ 文件特定计时功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_window():
    """测试设置窗口功能"""
    print("\n" + "=" * 60)
    print("设置窗口功能测试")
    print("=" * 60)
    
    try:
        # 这里只测试导入，不启动GUI
        from flyclock_tool.gui.settings_window import SettingsWindow
        print("✓ 设置窗口模块导入成功")
        
        # 测试智能分析功能是否存在
        import inspect
        methods = inspect.getmembers(SettingsWindow, predicate=inspect.isfunction)
        method_names = [name for name, _ in methods]
        
        if 'smart_analyze_file' in method_names:
            print("✓ 智能分析功能保留")
        else:
            print("✗ 智能分析功能缺失")
        
        if 'delete_file_timer' in method_names:
            print("✓ 删除文件功能存在")
        else:
            print("✗ 删除文件功能缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ 设置窗口测试失败: {e}")
        return False

if __name__ == "__main__":
    print("ToolClock 文件特定计时功能测试套件")
    print("请选择测试项目:")
    print("1. 文件特定计时功能测试")
    print("2. 设置窗口功能测试")
    print("3. 全部测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    success = True
    
    if choice == "1":
        success = test_file_specific_timing()
    elif choice == "2":
        success = test_settings_window()
    elif choice == "3":
        success = test_file_specific_timing() and test_settings_window()
    else:
        print("无效选择")
        success = False
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查问题")
