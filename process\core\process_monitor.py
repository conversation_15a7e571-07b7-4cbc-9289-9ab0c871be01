#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程监控模块
使用psutil库实现系统进程的监控、过滤和管理功能
"""

import psutil
import time
import threading
import logging
from typing import List, Dict, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProcessMonitor:
    """进程监控类"""
    
    def __init__(self, update_interval: float = 2.0):
        """
        初始化进程监控器
        
        Args:
            update_interval: 进程列表更新间隔（秒）
        """
        self.update_interval = update_interval
        self.processes = []
        self.is_monitoring = False
        self.monitor_thread = None
        self._lock = threading.Lock()
        
        logger.info("ProcessMonitor 初始化完成")
    
    def get_exe_processes(self) -> List[Dict]:
        """
        获取所有exe进程
        
        Returns:
            List[Dict]: 进程信息列表，每个字典包含pid、name、exe_path等信息
        """
        processes = []
        
        try:
            # 使用psutil获取所有进程
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'create_time']):
                try:
                    proc_info = proc.info
                    
                    # 过滤exe进程
                    if proc_info['name'] and proc_info['name'].lower().endswith('.exe'):
                        process_data = {
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'exe_path': proc_info['exe'] or 'Unknown',
                            'create_time': proc_info['create_time']
                        }
                        processes.append(process_data)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # 忽略无法访问的进程
                    continue
                except Exception as e:
                    logger.warning(f"获取进程信息时出错: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"获取进程列表失败: {e}")
            
        # 按进程名称排序（优化：只在进程数量较少时排序）
        if len(processes) < 1000:
            processes.sort(key=lambda x: x['name'].lower())
        
        logger.info(f"获取到 {len(processes)} 个exe进程")
        return processes    
    def is_process_running(self, pid: int) -> bool:
        """
        检查进程是否仍在运行
        
        Args:
            pid: 进程ID
            
        Returns:
            bool: 进程是否存在
        """
        try:
            return psutil.pid_exists(pid)
        except Exception as e:
            logger.warning(f"检查进程 {pid} 状态时出错: {e}")
            return False
    
    def get_process_info(self, pid: int) -> Optional[Dict]:
        """
        获取指定进程的详细信息
        
        Args:
            pid: 进程ID
            
        Returns:
            Optional[Dict]: 进程信息字典，如果进程不存在则返回None
        """
        try:
            proc = psutil.Process(pid)
            return {
                'pid': proc.pid,
                'name': proc.name(),
                'exe_path': proc.exe(),
                'status': proc.status(),
                'create_time': proc.create_time(),
                'memory_info': proc.memory_info()._asdict(),
                'cpu_percent': proc.cpu_percent()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return None
        except Exception as e:
            logger.warning(f"获取进程 {pid} 详细信息时出错: {e}")
            return None
    
    def filter_processes_by_name(self, processes: List[Dict], name_filter: str) -> List[Dict]:
        """
        根据进程名称过滤进程列表
        
        Args:
            processes: 进程列表
            name_filter: 名称过滤条件（支持部分匹配）
            
        Returns:
            List[Dict]: 过滤后的进程列表
        """
        if not name_filter:
            return processes
            
        name_filter = name_filter.lower()
        filtered = []
        
        for proc in processes:
            if name_filter in proc['name'].lower():
                filtered.append(proc)
                
        logger.info(f"根据名称 '{name_filter}' 过滤后得到 {len(filtered)} 个进程")
        return filtered    
    def start_monitoring(self, callback=None):
        """
        启动进程监控
        
        Args:
            callback: 进程列表更新时的回调函数
        """
        if self.is_monitoring:
            logger.warning("进程监控已在运行")
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, 
            args=(callback,), 
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("进程监控已启动")
    
    def stop_monitoring(self):
        """停止进程监控"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("进程监控已停止")
    
    def _monitoring_loop(self, callback):
        """
        监控循环（内部方法）
        
        Args:
            callback: 进程列表更新时的回调函数
        """
        while self.is_monitoring:
            try:
                # 获取最新进程列表
                new_processes = self.get_exe_processes()
                
                # 线程安全地更新进程列表
                with self._lock:
                    self.processes = new_processes
                
                # 调用回调函数
                if callback:
                    try:
                        callback(new_processes)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")
                
                # 等待下次更新
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(self.update_interval)
    
    def get_cached_processes(self) -> List[Dict]:
        """
        获取缓存的进程列表（线程安全）
        
        Returns:
            List[Dict]: 缓存的进程列表
        """
        with self._lock:
            return self.processes.copy()    
    def get_process_count(self) -> int:
        """
        获取当前监控的进程数量
        
        Returns:
            int: 进程数量
        """
        with self._lock:
            return len(self.processes)
    
    def find_processes_by_pids(self, pids: List[int]) -> List[Dict]:
        """
        根据PID列表查找进程
        
        Args:
            pids: 进程ID列表
            
        Returns:
            List[Dict]: 找到的进程列表
        """
        found_processes = []
        
        with self._lock:
            for proc in self.processes:
                if proc['pid'] in pids:
                    found_processes.append(proc)
                    
        return found_processes
    
    def refresh_processes(self) -> List[Dict]:
        """
        手动刷新进程列表
        
        Returns:
            List[Dict]: 最新的进程列表
        """
        new_processes = self.get_exe_processes()
        
        with self._lock:
            self.processes = new_processes
            
        return new_processes
    
    def __del__(self):
        """析构函数，确保监控线程正确停止"""
        self.stop_monitoring()


# 全局进程监控器实例
_process_monitor = None


def get_process_monitor(update_interval: float = 2.0) -> ProcessMonitor:
    """
    获取进程监控器实例（单例模式）
    
    Args:
        update_interval: 更新间隔
        
    Returns:
        ProcessMonitor: 进程监控器实例
    """
    global _process_monitor
    if _process_monitor is None:
        _process_monitor = ProcessMonitor(update_interval)
    return _process_monitor