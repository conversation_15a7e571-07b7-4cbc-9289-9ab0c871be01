import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QFileDialog, QMessageBox, QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QImage

try:
    from easyofd import OFD
    EASYOFD_AVAILABLE = True
except ImportError:
    EASYOFD_AVAILABLE = False

class PreviewWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件预览")
        self.setGeometry(100, 100, 800, 600)
        
        self.current_file = ""
        self.ofd_document = None
        self.current_page = 0
        
        self.init_ui()
        
    def init_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建工具栏
        toolbar_layout = QHBoxLayout()
        
        self.open_btn = QPushButton("打开文件")
        self.open_btn.clicked.connect(self.open_file)
        toolbar_layout.addWidget(self.open_btn)
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        self.prev_btn.setEnabled(False)
        toolbar_layout.addWidget(self.prev_btn)
        
        self.page_label = QLabel("页面: 0/0")
        toolbar_layout.addWidget(self.page_label)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        self.next_btn.setEnabled(False)
        toolbar_layout.addWidget(self.next_btn)
        
        toolbar_layout.addStretch()
        
        main_layout.addLayout(toolbar_layout)
        
        # 创建预览区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumSize(400, 500)
        self.preview_label.setStyleSheet("background-color: white; border: 1px solid gray;")
        self.scroll_area.setWidget(self.preview_label)
        main_layout.addWidget(self.scroll_area)
        
        # 文件信息区域
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        main_layout.addWidget(self.info_text)
        
    def open_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", "支持的文件 (*.ofd *.pdf)")
        
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path):
        """加载并显示文件"""
        self.current_file = file_path
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_ext == ".ofd":
                self.load_ofd_file(file_path)
            elif file_ext == ".pdf":
                self.load_pdf_file(file_path)
            
            # 显示文件信息
            self.show_file_info()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件:\n{str(e)}")
    
    def load_ofd_file(self, file_path):
        """加载OFD文件"""
        if not EASYOFD_AVAILABLE:
            raise ImportError("缺少必要的库，请安装 easyofd")
            
        with open(file_path, "rb") as f:
            ofd_data = f.read()
            self.ofd_document = OFD()
            self.ofd_document.read(ofd_data)
        
        # 重置页面
        self.current_page = 0
        self.update_page_info()
        self.show_page()
    
    def load_pdf_file(self, file_path):
        """加载PDF文件（简化处理，仅显示信息）"""
        self.ofd_document = None
        self.current_page = 0
        self.update_page_info()
        
        # 显示PDF文件信息而不是内容
        info_text = f"PDF文件: {os.path.basename(file_path)}\n"
        info_text += f"路径: {file_path}\n"
        info_text += "注意: PDF预览功能需要额外的库支持\n"
        info_text += "请安装 pdf2image 或 PyMuPDF 库以启用PDF预览功能"
        self.preview_label.setText(info_text)
    
    def prev_page(self):
        if self.ofd_document and self.current_page > 0:
            self.current_page -= 1
            self.update_page_info()
            self.show_page()
    
    def next_page(self):
        if self.ofd_document and self.current_page < self.get_page_count() - 1:
            self.current_page += 1
            self.update_page_info()
            self.show_page()
    
    def get_page_count(self):
        if self.ofd_document:
            try:
                return len(self.ofd_document.pages)
            except:
                return 1
        return 0
    
    def update_page_info(self):
        page_count = self.get_page_count()
        self.page_label.setText(f"页面: {self.current_page + 1}/{page_count}")
        
        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_page > 0 and self.ofd_document is not None)
        self.next_btn.setEnabled(self.current_page < page_count - 1 and self.ofd_document is not None)
    
    def show_page(self):
        """显示当前页面"""
        if not self.ofd_document:
            return
            
        try:
            # 获取当前页面的图像
            page_images = self.ofd_document.to_img(page=self.current_page)
            if page_images:
                page_image = page_images[0]  # 取第一张图
                
                # 转换为QImage
                height, width, channel = page_image.shape
                bytes_per_line = 3 * width
                q_image = QImage(page_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
                
                # 转换为QPixmap并显示
                pixmap = QPixmap.fromImage(q_image)
                self.preview_label.setPixmap(pixmap)
                self.preview_label.resize(pixmap.size())
            else:
                self.preview_label.setText("无法显示页面内容")
                
        except Exception as e:
            self.preview_label.setText(f"无法显示页面:\n{str(e)}")
    
    def show_file_info(self):
        """显示文件信息"""
        if not self.current_file:
            return
            
        try:
            info_text = f"文件路径: {self.current_file}\n"
            info_text += f"文件大小: {os.path.getsize(self.current_file)} 字节\n"
            info_text += f"修改时间: {os.path.getmtime(self.current_file)}\n"
            
            if self.ofd_document:
                info_text += f"总页数: {self.get_page_count()}\n"
                info_text += "文件格式: OFD\n"
            else:
                info_text += "文件格式: PDF\n"
            
            self.info_text.setText(info_text)
        except Exception as e:
            self.info_text.setText(f"无法提取文件信息: {str(e)}")

def main():
    app = QApplication(sys.argv)
    window = PreviewWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()