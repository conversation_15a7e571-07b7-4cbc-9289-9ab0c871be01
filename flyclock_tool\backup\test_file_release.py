#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件资源释放
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_analyzer():
    """测试文件分析器的资源释放"""
    print("测试文件分析器资源释放...")
    
    try:
        from core.file_analyzer import analyze_presentation_file
        
        # 创建一个测试文件路径（不需要真实存在）
        test_file = "test.pptx"
        
        print(f"分析文件: {test_file}")
        result = analyze_presentation_file(test_file)
        print(f"分析结果: {result}")
        
        # 强制垃圾回收
        import gc
        gc.collect()
        
        print("✓ 文件分析器测试完成")
        
    except Exception as e:
        print(f"✗ 文件分析器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_file_operations():
    """测试文件操作"""
    print("\n测试文件操作...")
    
    # 创建一个测试文件
    test_file = "test_temp.txt"
    
    try:
        # 写入测试文件
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件")
        
        print(f"✓ 创建测试文件: {test_file}")
        
        # 读取测试文件
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✓ 读取测试文件内容: {content}")
        
        # 尝试删除文件
        time.sleep(0.1)  # 短暂等待确保文件句柄释放
        os.remove(test_file)
        print(f"✓ 成功删除测试文件: {test_file}")
        
    except Exception as e:
        print(f"✗ 文件操作测试失败: {e}")
        # 清理测试文件
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
        except:
            pass

def test_ppt_analysis():
    """测试PPT分析（如果有真实文件）"""
    print("\n测试PPT文件分析...")
    
    # 查找可能的PPT文件
    common_dirs = [
        os.path.expanduser("~/Desktop"),
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/Downloads"),
    ]
    
    ppt_file = None
    for directory in common_dirs:
        if os.path.exists(directory):
            for file in os.listdir(directory):
                if file.lower().endswith(('.ppt', '.pptx')):
                    ppt_file = os.path.join(directory, file)
                    break
            if ppt_file:
                break
    
    if ppt_file:
        try:
            from core.file_analyzer import analyze_presentation_file
            
            print(f"分析PPT文件: {os.path.basename(ppt_file)}")
            result = analyze_presentation_file(ppt_file)
            print(f"分析结果: {result}")
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 尝试重命名文件（测试是否被占用）
            temp_name = ppt_file + ".temp"
            os.rename(ppt_file, temp_name)
            os.rename(temp_name, ppt_file)
            
            print("✓ PPT文件资源释放正常")
            
        except Exception as e:
            print(f"✗ PPT文件分析测试失败: {e}")
    else:
        print("未找到PPT文件进行测试")

def main():
    """主函数"""
    print("FlyClock 文件资源释放测试")
    print("=" * 40)
    
    test_file_analyzer()
    test_file_operations()
    test_ppt_analysis()
    
    print("\n测试完成！")
    print("\n如果所有测试都通过，说明文件资源释放正常。")
    print("现在应该可以正常删除添加到程序中的文件了。")

if __name__ == "__main__":
    main()
