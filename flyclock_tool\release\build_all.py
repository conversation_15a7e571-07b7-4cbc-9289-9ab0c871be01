#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 一键打包脚本
同时生成标准版本和红色背景持续蜂鸣版本的exe文件
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("ToolClock V1.1.0 一键打包工具")
    print("=" * 80)
    print("将生成两个版本的exe文件：")
    print("   1. 标准版本 - ToolClock_V1.1.0_Standard.exe")
    print("   2. 红色蜂鸣版本 - ToolClock_V1.1.0_RedBeep.exe")
    print("=" * 80)

def check_environment():
    """检查环境"""
    print("\n🔍 检查环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 请先安装PyInstaller: pip install pyinstaller")
        return False
    
    # 检查必要文件
    required_files = [
        'run_with_log.py',
        'run_red_version.py',
        'timer_window.py',
        'timer_window_red.py',
        'window_detector_fixed.py',
        '../gui/timer_window.py',
        '../core/config_manager.py',
        '../core/audio_player.py',
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 环境检查通过")
    return True

def clean_all():
    """清理所有构建目录"""
    print("\n🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"✓ 已清理文件: {spec_file}")

def build_standard_version():
    """构建标准版本"""
    print("\n📦 构建标准版本...")
    try:
        result = subprocess.run([
            sys.executable, 'build_standard.py'
        ], check=True, capture_output=True, text=True)
        
        print("✅ 标准版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 标准版本构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def build_red_version():
    """构建红色版本"""
    print("\n🔴 构建红色背景持续蜂鸣版本...")
    try:
        result = subprocess.run([
            sys.executable, 'build_red_version.py'
        ], check=True, capture_output=True, text=True)
        
        print("✅ 红色版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 红色版本构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_output_files():
    """检查输出文件"""
    print("\n📁 检查输出文件...")
    
    expected_files = [
        'dist/ToolClock_V1.1.0_Standard.exe',
        'dist/ToolClock_V1.1.0_RedBeep.exe'
    ]
    
    success_count = 0
    for file_path in expected_files:
        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size / (1024 * 1024)  # MB
            print(f"✅ {file_path} ({file_size:.1f} MB)")
            success_count += 1
        else:
            print(f"❌ {file_path} - 文件不存在")
    
    return success_count == len(expected_files)

def create_readme():
    """创建说明文件"""
    readme_content = """# ToolClock V1.1.0 发布版本

## 📦 包含文件

### 1. ToolClock_V1.1.0_Standard.exe
**标准版本**
- 基于用户确认的智能负数时间显示
- 计时结束后用户点击确定，二次全屏显示红色背景和负数时间
- 负数时间从-00:00开始动态递增
- 二次全屏时不会自动退出全屏

### 2. ToolClock_V1.1.0_RedBeep.exe  
**红色背景持续蜂鸣版本**
- 一旦激活红色模式，始终显示红色背景
- 持续播放蜂鸣音提醒
- 适用于需要强烈提醒的场合
- 可通过右键菜单手动激活/停止

## 🚀 使用方法

1. **运行程序**：双击exe文件启动
2. **设置时间**：右键计时器窗口，选择"设置时间"
3. **开始演示**：按F5全屏放映PPT/PDF
4. **自动计时**：程序自动检测全屏并开始倒计时
5. **时间结束**：自动退出全屏，显示提醒对话框
6. **确认操作**：点击确定按钮
7. **二次全屏**：再次按F5查看负数时间显示

## 🔧 功能特性

- ✅ 自动全屏检测
- ✅ 智能计时控制  
- ✅ 文件名清理（移除"润文档"等后缀）
- ✅ 基于用户确认的智能判断
- ✅ 红色背景负数时间显示
- ✅ 音频提醒功能
- ✅ 悬浮窗口显示

## 📝 版本信息

- **版本**：V1.1.0
- **发布日期**：2025-07-31
- **开发者**：AI Assistant + MCP工具集成

---
更多信息请参考版本信息.txt和V1.1.0_修复说明.md
"""
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ 已创建README.txt说明文件")

def main():
    """主函数"""
    start_time = time.time()
    
    # 打印横幅
    print_banner()
    
    # 检查环境
    if not check_environment():
        return
    
    # 清理构建目录
    clean_all()
    
    # 构建标准版本
    standard_success = build_standard_version()
    
    # 构建红色版本
    red_success = build_red_version()
    
    # 检查输出文件
    files_ok = check_output_files()
    
    # 创建说明文件
    if files_ok:
        create_readme()
    
    # 总结
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 80)
    print("📊 构建总结")
    print("=" * 80)
    print(f"⏱️  总耗时: {duration:.1f}秒")
    print(f"📦 标准版本: {'✅ 成功' if standard_success else '❌ 失败'}")
    print(f"🔴 红色版本: {'✅ 成功' if red_success else '❌ 失败'}")
    print(f"📁 文件检查: {'✅ 通过' if files_ok else '❌ 失败'}")
    
    if standard_success and red_success and files_ok:
        print("\n🎉 所有版本构建成功！")
        print("📁 输出目录: dist/")
        print("   - ToolClock_V1.1.0_Standard.exe (标准版本)")
        print("   - ToolClock_V1.1.0_RedBeep.exe (红色蜂鸣版本)")
        print("   - README.txt (使用说明)")
    else:
        print("\n❌ 部分构建失败，请检查错误信息")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
