#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ToolClock 功能测试脚本
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "flyclock_tool"))

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("ToolClock 基本功能测试")
    print("=" * 50)
    
    try:
        # 测试配置管理器
        from flyclock_tool.core.config_manager import config_manager
        print("✓ 配置管理器导入成功")
        
        # 测试设置
        config_manager.set_setting('test.value', 'hello')
        value = config_manager.get_setting('test.value', 'default')
        if value == 'hello':
            print("✓ 配置读写功能正常")
        else:
            print("✗ 配置读写功能异常")
        
        # 测试窗口检测器
        try:
            from flyclock_tool.window_detector_fixed import WindowDetectorFixed
            detector = WindowDetectorFixed()
            print("✓ 修复版窗口检测器导入成功")
        except ImportError:
            from flyclock_tool.core.window_detector import WindowDetector
            detector = WindowDetector()
            print("✓ 原始窗口检测器导入成功")
        
        # 测试计时器窗口（不启动GUI）
        print("✓ 所有核心模块导入成功")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_settings_window():
    """测试设置窗口功能"""
    print("=" * 50)
    print("ToolClock 设置窗口测试")
    print("=" * 50)
    
    try:
        from flyclock_tool.gui.settings_window import SettingsWindow
        print("✓ 设置窗口模块导入成功")
        
        # 测试配置项
        from flyclock_tool.core.config_manager import config_manager
        
        # 设置一些测试值
        config_manager.set_setting('timer.default_duration', '00:10:00')
        config_manager.set_setting('timer.mode', 'countdown')
        config_manager.set_setting('timer.duration', 600)
        
        # 读取测试
        duration = config_manager.get_setting('timer.default_duration', '00:05:00')
        mode = config_manager.get_setting('timer.mode', 'countdown')
        timer_duration = config_manager.get_setting('timer.duration', 300)
        
        print(f"✓ 默认时长: {duration}")
        print(f"✓ 计时模式: {mode}")
        print(f"✓ 计时器时长: {timer_duration}秒")
        
    except Exception as e:
        print(f"✗ 设置窗口测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_file_operations():
    """测试文件操作功能"""
    print("=" * 50)
    print("ToolClock 文件操作测试")
    print("=" * 50)
    
    try:
        from flyclock_tool.core.config_manager import config_manager
        
        # 测试文件特定计时器
        test_files = {
            'test1.pptx': '00:05:00',
            'test2.pdf': '00:10:00',
            'test3.pptx': '00:15:00'
        }
        
        # 保存测试文件
        config_manager.set_setting('files.file_specific_timers', test_files)
        
        # 读取测试
        saved_files = config_manager.get_setting('files.file_specific_timers', {})
        
        if saved_files == test_files:
            print("✓ 文件特定计时器保存/读取正常")
        else:
            print("✗ 文件特定计时器保存/读取异常")
            print(f"期望: {test_files}")
            print(f"实际: {saved_files}")
        
        # 测试删除功能
        del saved_files['test2.pdf']
        config_manager.set_setting('files.file_specific_timers', saved_files)
        
        updated_files = config_manager.get_setting('files.file_specific_timers', {})
        if 'test2.pdf' not in updated_files:
            print("✓ 文件删除功能正常")
        else:
            print("✗ 文件删除功能异常")
        
    except Exception as e:
        print(f"✗ 文件操作测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("ToolClock 功能测试套件")
    print("请选择测试项目:")
    print("1. 基本功能测试")
    print("2. 设置窗口测试")
    print("3. 文件操作测试")
    print("4. 全部测试")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        test_basic_functionality()
    elif choice == "2":
        test_settings_window()
    elif choice == "3":
        test_file_operations()
    elif choice == "4":
        test_basic_functionality()
        test_settings_window()
        test_file_operations()
    else:
        print("无效选择")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
