#!/usr/bin/env python3
"""
OFD转PDF工具MCP测试工具
用于测试和验证OFD转PDF工具的各项功能
"""

import os
import sys
import json
import tempfile
import traceback
from pathlib import Path

# 尝试导入必要的库
try:
    import easyofd
    EASYOFD_AVAILABLE = True
except ImportError:
    EASYOFD_AVAILABLE = False
    print("警告: 未安装easyofd库，OFD处理功能将不可用")

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QTimer
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False
    print("警告: 未安装PyQt5库，GUI功能将不可用")

def test_dependencies():
    """测试依赖库是否正确安装"""
    print("=== 依赖库测试 ===")
    
    # 测试easyofd
    if EASYOFD_AVAILABLE:
        print("✓ easyofd库可用")
        try:
            from easyofd import OFD
            print("✓ OFD类可导入")
        except Exception as e:
            print(f"✗ OFD类导入失败: {e}")
    else:
        print("✗ easyofd库不可用")
    
    # 测试PyQt5
    if PYQT5_AVAILABLE:
        print("✓ PyQt5库可用")
    else:
        print("✗ PyQt5库不可用")
    
    # 测试pywin32
    try:
        import win32api
        import win32print
        print("✓ pywin32库可用")
    except ImportError:
        print("警告: 未安装pywin32库，打印功能将不可用")
    
    print()

def test_file_operations():
    """测试文件操作功能"""
    print("=== 文件操作测试 ===")
    
    # 创建测试目录
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    # 检查是否存在测试OFD文件
    ofd_files = list(Path(".").glob("*.ofd"))
    if ofd_files:
        print(f"✓ 找到 {len(ofd_files)} 个OFD文件用于测试:")
        for f in ofd_files:
            print(f"  - {f}")
    else:
        print("ℹ 未找到测试OFD文件，将跳过转换测试")
    
    print()

def test_preview_module():
    """测试预览模块"""
    print("=== 预览模块测试 ===")
    
    try:
        from preview_window import PreviewWindow
        print("✓ PreviewWindow类可从preview_window模块导入")
    except ImportError as e:
        print(f"✗ 无法从preview_window模块导入PreviewWindow类: {e}")
        return
    except Exception as e:
        print(f"✗ 导入preview_window模块时出错: {e}")
        return
    
    # 如果PyQt5可用，尝试创建预览窗口实例
    if PYQT5_AVAILABLE:
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            # 创建预览窗口实例
            preview_window = PreviewWindow()
            print("✓ PreviewWindow实例创建成功")
            
            # 测试窗口属性
            if hasattr(preview_window, 'load_file'):
                print("✓ PreviewWindow具有load_file方法")
            else:
                print("✗ PreviewWindow缺少load_file方法")
                
            if hasattr(preview_window, 'load_ofd_file'):
                print("✓ PreviewWindow具有load_ofd_file方法")
            else:
                print("✗ PreviewWindow缺少load_ofd_file方法")
                
        except Exception as e:
            print(f"✗ 创建PreviewWindow实例时出错: {e}")
            traceback.print_exc()
    else:
        print("ℹ PyQt5不可用，跳过PreviewWindow实例创建测试")
    
    print()

def test_main_converter():
    """测试主转换器"""
    print("=== 主转换器测试 ===")
    
    # 检查主程序文件是否存在
    main_file = Path(__file__).parent / "ofd_converter.py"
    if main_file.exists():
        print("✓ ofd_converter.py文件存在")
    else:
        print("✗ ofd_converter.py文件不存在")
        print(f"  查找路径: {main_file}")
        return
    
    # 尝试导入主程序中的类
    try:
        # 动态导入主程序模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("ofd_converter", main_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 检查OFDConverterWindow类是否存在
        if hasattr(module, 'OFDConverterWindow'):
            print("✓ OFDConverterWindow类存在")
        else:
            print("✗ OFDConverterWindow类不存在")
            
        # 检查ConvertWorker类是否存在
        if hasattr(module, 'ConvertWorker'):
            print("✓ ConvertWorker类存在")
        else:
            print("✗ ConvertWorker类不存在")
            
        # 检查PrintWorker类是否存在
        if hasattr(module, 'PrintWorker'):
            print("✓ PrintWorker类存在")
        else:
            print("✗ PrintWorker类不存在")
            
    except Exception as e:
        print(f"✗ 导入ofd_converter模块时出错: {e}")
        traceback.print_exc()
    
    print()

def test_conversion_functionality():
    """测试转换功能"""
    print("=== 转换功能测试 ===")
    
    if not EASYOFD_AVAILABLE:
        print("✗ 由于缺少easyofd库，跳过转换功能测试")
        print()
        return
    
    # 查找测试OFD文件
    ofd_files = list(Path(".").glob("*.ofd"))
    if not ofd_files:
        print("ℹ 未找到测试OFD文件，跳过转换功能测试")
        print()
        return
    
    # 创建临时输出目录
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = Path(temp_dir)
        print(f"使用临时目录进行测试: {output_dir}")
        
        try:
            # 测试转换第一个OFD文件
            test_file = ofd_files[0]
            print(f"尝试转换文件: {test_file}")
            
            from easyofd import OFD
            
            # 读取OFD文件
            with open(test_file, "rb") as f:
                ofd_data = f.read()
                
            # 创建OFD对象并解析
            ofd = OFD()
            ofd.read(ofd_data)
            print("✓ OFD文件解析成功")
            
            # 转换为PDF
            pdf_bytes = ofd.to_pdf()
            print(f"✓ PDF转换成功，生成 {len(pdf_bytes)} 字节数据")
            
            # 保存PDF文件
            pdf_path = output_dir / f"{test_file.stem}.pdf"
            with open(pdf_path, "wb") as f:
                f.write(pdf_bytes)
                
            print(f"✓ PDF文件保存成功: {pdf_path}")
            
        except Exception as e:
            print(f"✗ 转换过程中出错: {e}")
            traceback.print_exc()
    
    print()

def test_preview_functionality():
    """测试预览功能"""
    print("=== 预览功能测试 ===")
    
    if not EASYOFD_AVAILABLE:
        print("✗ 由于缺少easyofd库，跳过预览功能测试")
        print()
        return
    
    if not PYQT5_AVAILABLE:
        print("✗ 由于缺少PyQt5库，跳过预览功能测试")
        print()
        return
    
    # 查找测试OFD文件
    ofd_files = list(Path(".").glob("*.ofd"))
    if not ofd_files:
        print("ℹ 未找到测试OFD文件，跳过预览功能测试")
        print()
        return
    
    try:
        # 测试预览第一个OFD文件
        test_file = ofd_files[0]
        print(f"尝试预览文件: {test_file}")
        
        from easyofd import OFD
        
        # 读取OFD文件
        with open(test_file, "rb") as f:
            ofd_data = f.read()
            
        # 创建OFD对象并解析
        ofd = OFD()
        ofd.read(ofd_data)
        print("✓ OFD文件解析成功")
        
        # 尝试生成图像
        images = ofd.to_img()
        if images:
            print(f"✓ 成功生成 {len(images)} 页图像")
            print(f"  第一页图像尺寸: {images[0].shape}")
        else:
            print("✗ 未能生成图像")
            
    except Exception as e:
        print(f"✗ 预览过程中出错: {e}")
        traceback.print_exc()
    
    print()

def main():
    """主测试函数"""
    print("OFD转PDF工具MCP测试工具")
    print("=" * 50)
    print()
    
    # 运行各项测试
    test_dependencies()
    test_file_operations()
    test_preview_module()
    test_main_converter()
    test_conversion_functionality()
    test_preview_functionality()
    
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()