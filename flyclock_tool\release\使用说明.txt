# ToolClock - PPT/PDF全屏放映计时辅助工具
版本: V1.0.0
构建时间: 2025-07-29

## 🚀 快速开始

1. 双击 ToolClock.exe 启动程序
2. 程序启动后会显示一个悬浮的计时器窗口
3. 右键点击计时器窗口打开菜单
4. 选择"本地设置"配置计时器参数
5. 打开PPT或PDF文件并进入全屏模式
6. 程序自动检测并开始计时

## 📋 功能特性

### 🎯 核心功能
- 自动检测PPT/PDF全屏状态并启动计时
- 悬浮计时器窗口，始终置顶显示
- 支持正计时和倒计时模式
- 音频提醒功能（到时提醒和提前警告）
- 可拖拽移动计时器位置

### 📱 兼容性支持
- Microsoft Office PowerPoint (PPT、PPTX、PPS、PPSX)
- WPS演示文稿
- Adobe Reader、Foxit Reader、SumatraPDF等PDF阅读器

### 🎨 个性化设置
- 字体大小和颜色自定义
- 背景颜色和透明度调整
- 自定义提示音文件
- 快捷键设置

## 🔧 详细使用说明

### 基本操作
1. **启动程序**: 双击ToolClock.exe
2. **设置计时**: 右键菜单 → "本地设置"
3. **开始计时**: 进入PPT/PDF全屏模式
4. **暂停/恢复**: 右键菜单或快捷键
5. **重置计时**: 右键菜单或快捷键
6. **退出程序**: 右键菜单 → "退出"

### 设置选项

#### 计时器设置
- 计时模式: 正计时/倒计时
- 计时时长: 自定义分钟数
- 自动启动: 检测到全屏时自动开始

#### 控制设置
- 快捷键: 自定义暂停/恢复和重置快捷键
- 鼠标操作: 支持拖拽移动

#### 外观设置
- 字体: 大小、颜色、样式
- 背景: 颜色、透明度
- 显示: 机器时间、提示文字等

#### 音频设置
- 启用音频提醒
- 提前警告时间设置
- 自定义提示音文件
- 音量控制

### 快捷键操作
- 暂停/恢复: 默认 Ctrl+Space
- 重置计时: 默认 Ctrl+R
- (可在设置中自定义)

## 🔍 常见问题

### Q: 程序无法检测到全屏状态？
A: 请确保：
   - PPT/PDF确实处于全屏模式
   - 没有其他窗口遮挡
   - 尝试重新进入全屏模式

### Q: 计时器窗口不显示？
A: 请检查：
   - 程序是否正常启动
   - 计时器窗口是否被移动到屏幕外
   - 尝试重启程序

### Q: 音频提醒不工作？
A: 请确认：
   - 音频设置已启用
   - 系统音量未静音
   - 音频文件路径正确

### Q: 如何自定义提示音？
A: 在设置中：
   - 点击"浏览"按钮
   - 选择WAV格式的音频文件
   - 点击"试听"测试效果

## 💡 使用技巧

1. **演示前准备**: 提前设置好计时时长和提醒时间
2. **位置调整**: 将计时器拖拽到不影响演示的位置
3. **颜色搭配**: 选择与演示背景对比明显的颜色
4. **音量适中**: 设置合适的提示音音量，避免干扰演示
5. **快捷键**: 熟练使用快捷键进行快速操作

## 🖥️ 系统要求

- 操作系统: Windows 10/11
- 内存: 建议512MB以上可用内存
- 磁盘空间: 约20MB
- 无需安装Python环境

## 📞 技术支持

如遇到问题或有改进建议，请：
1. 检查本使用说明
2. 尝试重启程序
3. 联系开发者反馈问题

## 📄 版本信息

- 当前版本: V1.0.0
- 发布日期: 2025-07-29
- 更新内容: 初始发布版本

## 🔒 许可证

本软件采用MIT许可证，允许自由使用和分发。

---

感谢使用FlyClock！祝您演示顺利！
