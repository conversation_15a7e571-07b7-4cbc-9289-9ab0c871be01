{"tag_schema": {"version": "2.0", "last_updated": "2025-06-07", "description": "Standardized tag schema for MCP Memory Service knowledge management", "total_categories": 6, "naming_convention": {"format": "lowercase-with-hyphens", "rules": ["Use lowercase letters only", "Replace spaces with hyphens", "Use descriptive but concise terms", "Avoid abbreviations unless widely understood", "Use singular form when possible"], "examples": {"good": ["memory-service", "github-integration", "best-practices"], "bad": ["memoryservice", "GitHub_Integration", "bestPractices"]}}, "categories": {"projects_and_repositories": {"description": "Primary projects, repositories, and major components", "color": "#3b82f6", "icon": "🚀", "tags": {"primary_projects": [{"tag": "mcp-memory-service", "description": "Core memory service development", "usage_count": 45, "examples": ["Core development", "Memory storage features", "Database operations"]}, {"tag": "memory-dashboard", "description": "Dashboard application for memory management", "usage_count": 23, "examples": ["UI development", "Dashboard features", "User interface"]}, {"tag": "github-integration", "description": "GitHub connectivity and automation", "usage_count": 15, "examples": ["Issue tracking", "Repository management", "CI/CD"]}, {"tag": "mcp-protocol", "description": "Model Context Protocol development", "usage_count": 12, "examples": ["Protocol specifications", "Communication standards"]}, {"tag": "cloudflare-workers", "description": "Edge computing integration", "usage_count": 8, "examples": ["Edge deployment", "Worker scripts", "Distributed systems"]}], "project_components": [{"tag": "frontend", "description": "User interface components and client-side development", "usage_count": 18, "examples": ["React components", "UI/UX", "Client applications"]}, {"tag": "backend", "description": "Server-side development and APIs", "usage_count": 32, "examples": ["Server logic", "Database operations", "API development"]}, {"tag": "api", "description": "API design and implementation", "usage_count": 14, "examples": ["REST APIs", "Endpoints", "API documentation"]}, {"tag": "database", "description": "Data storage and management", "usage_count": 22, "examples": ["ChromaDB", "Data models", "Database optimization"]}, {"tag": "infrastructure", "description": "Deployment and DevOps", "usage_count": 16, "examples": ["<PERSON>er", "Cloud deployment", "System architecture"]}]}}, "technologies_and_tools": {"description": "Programming languages, frameworks, libraries, and development tools", "color": "#10b981", "icon": "⚙️", "tags": {"programming_languages": [{"tag": "python", "description": "Python development and scripts", "usage_count": 38, "examples": ["Backend development", "<PERSON><PERSON><PERSON>", "Data processing"]}, {"tag": "typescript", "description": "TypeScript development", "usage_count": 25, "examples": ["Frontend development", "Type safety", "React applications"]}, {"tag": "javascript", "description": "JavaScript development", "usage_count": 20, "examples": ["Client-side logic", "Node.js", "Web development"]}, {"tag": "bash", "description": "Shell scripting and command-line operations", "usage_count": 12, "examples": ["Automation scripts", "System administration", "Build processes"]}, {"tag": "sql", "description": "Database queries and operations", "usage_count": 8, "examples": ["Database queries", "Data analysis", "Schema design"]}], "frameworks_and_libraries": [{"tag": "react", "description": "React framework development", "usage_count": 22, "examples": ["Component development", "UI frameworks", "Frontend applications"]}, {"tag": "<PERSON><PERSON><PERSON>", "description": "FastAPI framework for Python APIs", "usage_count": 15, "examples": ["API development", "Web services", "Backend frameworks"]}, {"tag": "chromadb", "description": "ChromaDB vector database", "usage_count": 28, "examples": ["Vector storage", "Embedding operations", "Similarity search"]}, {"tag": "sentence-transformers", "description": "Sentence transformer models for embeddings", "usage_count": 18, "examples": ["Text embeddings", "Semantic search", "NLP models"]}, {"tag": "pytest", "description": "Python testing framework", "usage_count": 10, "examples": ["Unit testing", "Test automation", "Quality assurance"]}], "tools_and_platforms": [{"tag": "git", "description": "Version control and repository management", "usage_count": 24, "examples": ["Version control", "Collaboration", "Code management"]}, {"tag": "docker", "description": "Containerization and deployment", "usage_count": 16, "examples": ["Container deployment", "Application packaging", "DevOps"]}, {"tag": "github", "description": "GitHub platform and repository management", "usage_count": 20, "examples": ["Repository hosting", "Issue tracking", "Collaboration"]}, {"tag": "aws", "description": "Amazon Web Services cloud platform", "usage_count": 12, "examples": ["Cloud infrastructure", "Deployment", "Scalability"]}, {"tag": "npm", "description": "Node package management", "usage_count": 8, "examples": ["Package management", "Dependencies", "JavaScript ecosystem"]}]}}, "activities_and_processes": {"description": "Development activities, operational processes, and workflows", "color": "#f59e0b", "icon": "🔧", "tags": {"development_activities": [{"tag": "development", "description": "General development work and programming", "usage_count": 35, "examples": ["Feature development", "Code writing", "Implementation"]}, {"tag": "implementation", "description": "Feature implementation and code realization", "usage_count": 28, "examples": ["Feature implementation", "Code realization", "System building"]}, {"tag": "debugging", "description": "Bug investigation and problem solving", "usage_count": 22, "examples": ["Bug fixes", "Problem investigation", "Issue resolution"]}, {"tag": "testing", "description": "Quality assurance and testing activities", "usage_count": 30, "examples": ["Unit testing", "Integration testing", "Quality assurance"]}, {"tag": "refactoring", "description": "Code improvement and restructuring", "usage_count": 12, "examples": ["Code cleanup", "Architecture improvement", "Optimization"]}, {"tag": "optimization", "description": "Performance enhancement and efficiency improvements", "usage_count": 15, "examples": ["Performance tuning", "Resource optimization", "Speed improvements"]}], "documentation_activities": [{"tag": "documentation", "description": "Writing documentation and guides", "usage_count": 25, "examples": ["Technical documentation", "User guides", "API documentation"]}, {"tag": "tutorial", "description": "Creating tutorials and learning materials", "usage_count": 8, "examples": ["Step-by-step guides", "Learning materials", "How-to documents"]}, {"tag": "guide", "description": "Comprehensive guides and references", "usage_count": 12, "examples": ["Best practice guides", "Implementation guides", "Reference materials"]}, {"tag": "reference", "description": "Reference materials and quick lookups", "usage_count": 18, "examples": ["Quick reference", "Lookup tables", "Technical specifications"]}, {"tag": "examples", "description": "Code examples and practical demonstrations", "usage_count": 15, "examples": ["Code samples", "Usage examples", "Demonstrations"]}], "operational_activities": [{"tag": "deployment", "description": "Application deployment and release management", "usage_count": 18, "examples": ["Production deployment", "Release management", "Environment setup"]}, {"tag": "monitoring", "description": "System monitoring and observability", "usage_count": 10, "examples": ["Performance monitoring", "Health checks", "System observability"]}, {"tag": "backup", "description": "Data backup and recovery processes", "usage_count": 8, "examples": ["Data backup", "Disaster recovery", "Data preservation"]}, {"tag": "migration", "description": "Data or system migration processes", "usage_count": 12, "examples": ["Data migration", "System upgrades", "Platform transitions"]}, {"tag": "maintenance", "description": "System maintenance and upkeep", "usage_count": 15, "examples": ["Regular maintenance", "System updates", "Preventive care"]}, {"tag": "troubleshooting", "description": "Problem resolution and diagnostic work", "usage_count": 20, "examples": ["Issue diagnosis", "Problem solving", "System repair"]}]}}, "content_types_and_formats": {"description": "Types of knowledge content and documentation formats", "color": "#8b5cf6", "icon": "📚", "tags": {"knowledge_types": [{"tag": "concept", "description": "Conceptual information and theoretical content", "usage_count": 18, "examples": ["Design concepts", "Theoretical frameworks", "Ideas"]}, {"tag": "architecture", "description": "System architecture and design patterns", "usage_count": 22, "examples": ["System design", "Architecture patterns", "Technical blueprints"]}, {"tag": "design", "description": "Design decisions and design patterns", "usage_count": 16, "examples": ["Design decisions", "UI/UX design", "System design"]}, {"tag": "best-practices", "description": "Proven methodologies and recommended approaches", "usage_count": 20, "examples": ["Industry standards", "Recommended practices", "Quality guidelines"]}, {"tag": "methodology", "description": "Systematic approaches and methodologies", "usage_count": 12, "examples": ["Development methodologies", "Process frameworks", "Systematic approaches"]}, {"tag": "workflow", "description": "Process workflows and operational procedures", "usage_count": 14, "examples": ["Business processes", "Development workflows", "Operational procedures"]}], "documentation_formats": [{"tag": "tutorial", "description": "Step-by-step instructional content", "usage_count": 15, "examples": ["Learning tutorials", "How-to guides", "Educational content"]}, {"tag": "reference", "description": "Quick reference materials and lookups", "usage_count": 18, "examples": ["API reference", "Command reference", "Quick lookups"]}, {"tag": "example", "description": "Practical examples and demonstrations", "usage_count": 22, "examples": ["Code examples", "Use cases", "Practical demonstrations"]}, {"tag": "template", "description": "Reusable templates and boilerplates", "usage_count": 10, "examples": ["Document templates", "Code templates", "Process templates"]}, {"tag": "checklist", "description": "Verification checklists and task lists", "usage_count": 8, "examples": ["Quality checklists", "Process verification", "Task lists"]}, {"tag": "summary", "description": "Condensed information and overviews", "usage_count": 12, "examples": ["Executive summaries", "Project overviews", "Condensed reports"]}], "technical_content": [{"tag": "configuration", "description": "System configuration and setup information", "usage_count": 16, "examples": ["System setup", "Configuration files", "Environment setup"]}, {"tag": "specification", "description": "Technical specifications and requirements", "usage_count": 14, "examples": ["Technical specs", "Requirements", "Standards"]}, {"tag": "analysis", "description": "Technical analysis and research findings", "usage_count": 18, "examples": ["Performance analysis", "Technical research", "Data analysis"]}, {"tag": "research", "description": "Research findings and investigations", "usage_count": 15, "examples": ["Research results", "Investigations", "Study findings"]}, {"tag": "review", "description": "Code reviews and process evaluations", "usage_count": 10, "examples": ["Code reviews", "Process reviews", "Quality assessments"]}]}}, "status_and_progress": {"description": "Development status, progress indicators, and priority levels", "color": "#ef4444", "icon": "📊", "tags": {"development_status": [{"tag": "resolved", "description": "Completed and verified work", "usage_count": 25, "examples": ["Completed features", "Fixed bugs", "Resolved issues"]}, {"tag": "in-progress", "description": "Currently being worked on", "usage_count": 18, "examples": ["Active development", "Ongoing work", "Current tasks"]}, {"tag": "blocked", "description": "Waiting for external dependencies", "usage_count": 8, "examples": ["Dependency blocks", "External waiting", "Resource constraints"]}, {"tag": "needs-investigation", "description": "Requires further analysis or research", "usage_count": 12, "examples": ["Research needed", "Analysis required", "Investigation pending"]}, {"tag": "planned", "description": "Scheduled for future work", "usage_count": 15, "examples": ["Future work", "Roadmap items", "Planned features"]}, {"tag": "cancelled", "description": "No longer being pursued", "usage_count": 5, "examples": ["Cancelled projects", "Deprecated features", "Abandoned work"]}], "quality_status": [{"tag": "verified", "description": "Tested and confirmed working", "usage_count": 20, "examples": ["Verified functionality", "Confirmed working", "Quality assured"]}, {"tag": "tested", "description": "Has undergone testing", "usage_count": 22, "examples": ["Tested code", "QA complete", "Testing done"]}, {"tag": "reviewed", "description": "Has been peer reviewed", "usage_count": 15, "examples": ["Code reviewed", "Peer reviewed", "Quality checked"]}, {"tag": "approved", "description": "Officially approved for use", "usage_count": 12, "examples": ["Management approved", "Officially sanctioned", "Authorized"]}, {"tag": "experimental", "description": "Proof of concept or experimental stage", "usage_count": 8, "examples": ["Proof of concept", "Experimental features", "Research stage"]}, {"tag": "deprecated", "description": "No longer recommended for use", "usage_count": 6, "examples": ["Legacy code", "Outdated practices", "Superseded methods"]}], "priority_levels": [{"tag": "urgent", "description": "Immediate attention required", "usage_count": 8, "examples": ["Critical bugs", "Emergency fixes", "Immediate action"]}, {"tag": "high-priority", "description": "Important, should be addressed soon", "usage_count": 15, "examples": ["Important features", "Key improvements", "High-impact work"]}, {"tag": "normal-priority", "description": "Standard priority work", "usage_count": 25, "examples": ["Regular work", "Standard features", "Normal development"]}, {"tag": "low-priority", "description": "Can be addressed when time allows", "usage_count": 18, "examples": ["Nice-to-have features", "Minor improvements", "Low-impact work"]}, {"tag": "nice-to-have", "description": "Enhancement, not critical", "usage_count": 12, "examples": ["Optional features", "Enhancements", "Convenience improvements"]}]}}, "context_and_temporal": {"description": "Temporal markers, environmental context, and scope indicators", "color": "#06b6d4", "icon": "🕒", "tags": {"temporal_markers": [{"tag": "january-2025", "description": "Content from January 2025", "usage_count": 50, "examples": ["Project initialization", "Early development", "Planning phase"]}, {"tag": "june-2025", "description": "Content from June 2025", "usage_count": 45, "examples": ["Recent development", "Current work", "Latest updates"]}, {"tag": "q1-2025", "description": "First quarter 2025 content", "usage_count": 18, "examples": ["Quarterly planning", "Q1 objectives", "First quarter work"]}, {"tag": "milestone-v1", "description": "Version 1 milestone content", "usage_count": 12, "examples": ["Version milestones", "Release markers", "Development phases"]}, {"tag": "sprint-3", "description": "Development sprint markers", "usage_count": 8, "examples": ["Sprint work", "Iteration markers", "Development cycles"]}], "environmental_context": [{"tag": "development", "description": "Development environment context", "usage_count": 30, "examples": ["Development work", "Local environment", "Dev testing"]}, {"tag": "staging", "description": "Staging environment context", "usage_count": 12, "examples": ["Staging deployment", "Pre-production", "Staging testing"]}, {"tag": "production", "description": "Production environment context", "usage_count": 20, "examples": ["Live systems", "Production deployment", "Production issues"]}, {"tag": "testing", "description": "Testing environment context", "usage_count": 25, "examples": ["Test environment", "QA testing", "Testing infrastructure"]}, {"tag": "local", "description": "Local development context", "usage_count": 15, "examples": ["Local development", "Local testing", "Local setup"]}], "scope_and_impact": [{"tag": "breaking-change", "description": "Introduces breaking changes", "usage_count": 8, "examples": ["API changes", "Backwards incompatible", "Major updates"]}, {"tag": "feature", "description": "New feature development", "usage_count": 28, "examples": ["New features", "Feature additions", "Functionality expansion"]}, {"tag": "enhancement", "description": "Improvement to existing features", "usage_count": 22, "examples": ["Feature improvements", "Performance enhancements", "User experience"]}, {"tag": "hotfix", "description": "Critical fix for production issues", "usage_count": 6, "examples": ["Emergency fixes", "Critical patches", "Production fixes"]}, {"tag": "security", "description": "Security-related content", "usage_count": 10, "examples": ["Security fixes", "Security analysis", "Vulnerability patches"]}, {"tag": "performance", "description": "Performance-related improvements", "usage_count": 15, "examples": ["Performance optimization", "Speed improvements", "Efficiency gains"]}]}}}, "tag_combination_patterns": {"description": "Common patterns for combining tags across categories", "examples": [{"pattern": "Project + Technology + Activity + Status", "example": ["mcp-memory-service", "python", "debugging", "resolved"], "usage": "Most comprehensive tagging for technical work"}, {"pattern": "Content Type + Domain + Technology + Context", "example": ["documentation", "backend", "chromadb", "production"], "usage": "Documentation and reference materials"}, {"pattern": "Activity + Status + Priority + Temporal", "example": ["testing", "in-progress", "high-priority", "june-2025"], "usage": "Active work items with clear status"}, {"pattern": "Concept + Architecture + Research + Domain", "example": ["concept", "architecture", "research", "system-design"], "usage": "Conceptual and design-related content"}]}, "usage_guidelines": {"recommended_tag_count": {"minimum": 3, "maximum": 8, "optimal": "4-6 tags from different categories"}, "category_distribution": {"required": ["Project context (1-2 tags)", "Content type or activity (1-2 tags)"], "recommended": ["Technology (1-2 tags)", "Status (1 tag)"], "optional": ["Context/Temporal (0-2 tags)", "Priority (0-1 tags)"]}, "quality_indicators": ["Tags from multiple categories", "Specific rather than generic terms", "Consistent with established patterns", "Relevant to content and future retrieval"]}, "maintenance": {"review_schedule": {"weekly": "Check new tag usage patterns", "monthly": "Review tag frequency and consistency", "quarterly": "Update schema based on usage patterns"}, "evolution_process": ["Identify new patterns in content", "Propose new tags or categories", "Test with sample content", "Update schema documentation", "Migrate existing content if needed"]}}}